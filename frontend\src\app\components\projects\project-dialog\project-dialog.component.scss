.project-dialog {
  min-width: 500px;
  max-width: 600px;

  h2[mat-dialog-title] {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    color: #1976d2;
    
    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  }

  .project-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 8px 0;

    .full-width {
      width: 100%;
    }

    mat-form-field {
      .mat-mdc-form-field-icon-suffix {
        color: rgba(0, 0, 0, 0.54);
      }
    }

    .project-info {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background-color: #e3f2fd;
      border-radius: 4px;
      color: #1976d2;
      font-size: 14px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  mat-dialog-actions {
    padding: 16px 0 8px 0;
    gap: 8px;

    button {
      display: flex;
      align-items: center;
      gap: 4px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    button[mat-raised-button] {
      min-width: 100px;
    }
  }
}

// Responsive design
@media (max-width: 600px) {
  .project-dialog {
    min-width: 90vw;
    max-width: 90vw;
  }
}

// Dark theme support
.dark-theme {
  .project-dialog {
    .project-info {
      background-color: #1e3a5f;
      color: #90caf9;
    }
  }
}

// Animation for dialog
.project-dialog {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
