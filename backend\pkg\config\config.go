package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Environment string          `json:"environment" mapstructure:"environment"`
	Server      ServerConfig    `json:"server" mapstructure:"server"`
	Database    DBConfig        `json:"database" mapstructure:"database"`
	Security    SecurityConfig  `json:"security" mapstructure:"security"`
	Scanning    ScanningConfig  `json:"scanning" mapstructure:"scanning"`
	Reporting   ReportingConfig `json:"reporting" mapstructure:"reporting"`
	Plugins     PluginsConfig   `json:"plugins" mapstructure:"plugins"`
	Logging     LoggingConfig   `json:"logging" mapstructure:"logging"`
}

type ServerConfig struct {
	Port int    `json:"port" mapstructure:"port"`
	Host string `json:"host" mapstructure:"host"`
}

type DBConfig struct {
	Type     string `json:"type" mapstructure:"type"`
	Host     string `json:"host" mapstructure:"host"`
	Port     int    `json:"port" mapstructure:"port"`
	Database string `json:"database" mapstructure:"database"`
	Username string `json:"username" mapstructure:"username"`
	Password string `json:"password" mapstructure:"password"`
	SSLMode  string `json:"ssl_mode" mapstructure:"ssl_mode"`
}

type SecurityConfig struct {
	Level string                 `json:"level" mapstructure:"level"`
	Rules SecurityRulesConfig    `json:"rules" mapstructure:"rules"`
}

type SecurityRulesConfig struct {
	KeyLeakDetection    KeyLeakConfig    `json:"keyLeakDetection" mapstructure:"keyLeakDetection"`
	SmartContractAudit  ContractConfig   `json:"smartContractAudit" mapstructure:"smartContractAudit"`
	DependencyScanning  DependencyConfig `json:"dependencyScanning" mapstructure:"dependencyScanning"`
	EnvironmentSecurity EnvSecurityConfig `json:"environmentSecurity" mapstructure:"environmentSecurity"`
}

type KeyLeakConfig struct {
	Enabled  bool     `json:"enabled" mapstructure:"enabled"`
	Patterns []string `json:"patterns" mapstructure:"patterns"`
}

type ContractConfig struct {
	Enabled  bool            `json:"enabled" mapstructure:"enabled"`
	Ethereum EthereumConfig  `json:"ethereum" mapstructure:"ethereum"`
	Bitcoin  BitcoinConfig   `json:"bitcoin" mapstructure:"bitcoin"`
}

type EthereumConfig struct {
	Reentrancy      bool `json:"reentrancy" mapstructure:"reentrancy"`
	IntegerOverflow bool `json:"integerOverflow" mapstructure:"integerOverflow"`
	UncheckedCalls  bool `json:"uncheckedCalls" mapstructure:"uncheckedCalls"`
	GasOptimization bool `json:"gasOptimization" mapstructure:"gasOptimization"`
	AccessControl   bool `json:"accessControl" mapstructure:"accessControl"`
}

type BitcoinConfig struct {
	ScriptValidation bool `json:"scriptValidation" mapstructure:"scriptValidation"`
	MultisigSecurity bool `json:"multisigSecurity" mapstructure:"multisigSecurity"`
	UTXOPatterns     bool `json:"utxoPatterns" mapstructure:"utxoPatterns"`
}

type DependencyConfig struct {
	Enabled  bool     `json:"enabled" mapstructure:"enabled"`
	Sources  []string `json:"sources" mapstructure:"sources"`
	Severity []string `json:"severity" mapstructure:"severity"`
}

type EnvSecurityConfig struct {
	Enabled          bool `json:"enabled" mapstructure:"enabled"`
	CheckDockerfiles bool `json:"checkDockerfiles" mapstructure:"checkDockerfiles"`
	CheckCICD        bool `json:"checkCICD" mapstructure:"checkCICD"`
	CheckEnvFiles    bool `json:"checkEnvFiles" mapstructure:"checkEnvFiles"`
}

type ScanningConfig struct {
	Paths     PathsConfig `json:"paths" mapstructure:"paths"`
	FileTypes []string    `json:"fileTypes" mapstructure:"fileTypes"`
}

type PathsConfig struct {
	Include []string `json:"include" mapstructure:"include"`
	Exclude []string `json:"exclude" mapstructure:"exclude"`
}

type ReportingConfig struct {
	Format           string   `json:"format" mapstructure:"format"`
	OutputPath       string   `json:"outputPath" mapstructure:"outputPath"`
	IncludeTimestamp bool     `json:"includeTimestamp" mapstructure:"includeTimestamp"`
	Sections         []string `json:"sections" mapstructure:"sections"`
}

type PluginsConfig struct {
	Ethereum PluginConfig `json:"ethereum" mapstructure:"ethereum"`
	Bitcoin  PluginConfig `json:"bitcoin" mapstructure:"bitcoin"`
}

type PluginConfig struct {
	Enabled bool `json:"enabled" mapstructure:"enabled"`
}

// Load loads configuration from various sources
func Load() (*Config, error) {
	// Set default values
	viper.SetDefault("environment", "development")
	viper.SetDefault("server.port", 8080)
	viper.SetDefault("server.host", "localhost")
	viper.SetDefault("database.type", "sqlite")
	viper.SetDefault("database.database", "spt.db")

	// Set config name and paths
	viper.SetConfigName("spt.config")
	viper.SetConfigType("json")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// Read environment variables
	viper.AutomaticEnv()

	// Read config file
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	return &config, nil
}

// LoadFromFile loads configuration from a specific file
func LoadFromFile(configPath string) (*Config, error) {
	if !filepath.IsAbs(configPath) {
		wd, err := os.Getwd()
		if err != nil {
			return nil, fmt.Errorf("failed to get working directory: %w", err)
		}
		configPath = filepath.Join(wd, configPath)
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	return &config, nil
}

// LoggingConfig represents logging configuration
type LoggingConfig struct {
	Level  string `json:"level" mapstructure:"level"`
	Format string `json:"format" mapstructure:"format"`
	Output string `json:"output" mapstructure:"output"`
}
