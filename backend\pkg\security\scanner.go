package security

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// Scanner represents the general security scanner
type Scanner struct {
	config *config.Config
	logger *logrus.Logger
}

// NewScanner creates a new security scanner instance
func NewScanner(cfg *config.Config) (*Scanner, error) {
	return &Scanner{
		config: cfg,
		logger: logrus.New(),
	}, nil
}

// ScanProject scans an entire project for general security issues
func (s *Scanner) ScanProject(ctx context.Context, projectPath string) ([]models.SecurityIssue, error) {
	s.logger.Infof("Starting general security scan for project: %s", projectPath)

	var allIssues []models.SecurityIssue

	// Find all relevant files
	files, err := s.findRelevantFiles(projectPath)
	if err != nil {
		return nil, fmt.Errorf("failed to find files: %w", err)
	}

	// Scan each file
	for _, file := range files {
		issues, err := s.ScanFile(ctx, file)
		if err != nil {
			s.logger.Errorf("Failed to scan file %s: %v", file, err)
			continue
		}
		allIssues = append(allIssues, issues...)
	}

	// Scan for environment-specific issues
	envIssues, err := s.scanEnvironmentFiles(projectPath)
	if err != nil {
		s.logger.Errorf("Failed to scan environment files: %v", err)
	} else {
		allIssues = append(allIssues, envIssues...)
	}

	s.logger.Infof("General security scan completed, found %d issues", len(allIssues))
	return allIssues, nil
}

// ScanFile scans a specific file for general security issues
func (s *Scanner) ScanFile(ctx context.Context, filePath string) ([]models.SecurityIssue, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	var issues []models.SecurityIssue

	// Run various security checks
	issues = append(issues, s.checkSecretLeaks(filePath, string(content))...)
	issues = append(issues, s.checkHardcodedCredentials(filePath, string(content))...)
	issues = append(issues, s.checkInsecurePatterns(filePath, string(content))...)
	issues = append(issues, s.checkCryptographicIssues(filePath, string(content))...)

	return issues, nil
}

// checkSecretLeaks checks for leaked secrets and sensitive information
func (s *Scanner) checkSecretLeaks(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Define patterns for various types of secrets
	secretPatterns := []struct {
		regex       *regexp.Regexp
		secretType  string
		severity    string
		description string
	}{
		{
			regex:       regexp.MustCompile(`(?i)(api[_-]?key|apikey)\s*[:=]\s*["'][^"']{10,}["']`),
			secretType:  "api_key",
			severity:    "high",
			description: "API key found in code",
		},
		{
			regex:       regexp.MustCompile(`(?i)(secret[_-]?key|secretkey)\s*[:=]\s*["'][^"']{10,}["']`),
			secretType:  "secret_key",
			severity:    "high",
			description: "Secret key found in code",
		},
		{
			regex:       regexp.MustCompile(`(?i)(password|passwd|pwd)\s*[:=]\s*["'][^"']{3,}["']`),
			secretType:  "password",
			severity:    "high",
			description: "Password found in code",
		},
		{
			regex:       regexp.MustCompile(`(?i)(token|auth[_-]?token)\s*[:=]\s*["'][^"']{10,}["']`),
			secretType:  "token",
			severity:    "high",
			description: "Authentication token found in code",
		},
		{
			regex:       regexp.MustCompile(`(?i)(private[_-]?key|privatekey)\s*[:=]\s*["'][^"']{20,}["']`),
			secretType:  "private_key",
			severity:    "critical",
			description: "Private key found in code",
		},
		{
			regex:       regexp.MustCompile(`(?i)(mnemonic|seed[_-]?phrase)\s*[:=]\s*["'][^"']{20,}["']`),
			secretType:  "mnemonic",
			severity:    "critical",
			description: "Mnemonic phrase found in code",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		// Skip comments
		if strings.Contains(line, "//") || strings.Contains(line, "#") || strings.Contains(line, "/*") {
			continue
		}

		for _, pattern := range secretPatterns {
			if pattern.regex.MatchString(line) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("secret_%s_%s_%d", pattern.secretType, filepath.Base(filePath), i+1),
					Type:        "secret_leak",
					Severity:    pattern.severity,
					Title:       "Secret Leak Detected",
					Description: pattern.description,
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "general",
					Category:    "environment",
					CWE:         "CWE-798",
					OWASP:       "A02:2021 – Cryptographic Failures",
					Suggestion:  "Move secrets to environment variables or secure key management",
					References:  []string{"https://owasp.org/www-project-top-ten/2017/A3_2017-Sensitive_Data_Exposure"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkHardcodedCredentials checks for hardcoded credentials
func (s *Scanner) checkHardcodedCredentials(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Patterns for hardcoded credentials
	credentialPatterns := []struct {
		regex       *regexp.Regexp
		description string
	}{
		{
			regex:       regexp.MustCompile(`(?i)(username|user)\s*[:=]\s*["']admin["']`),
			description: "Hardcoded admin username",
		},
		{
			regex:       regexp.MustCompile(`(?i)(password|passwd)\s*[:=]\s*["'](admin|password|123456|root)["']`),
			description: "Weak hardcoded password",
		},
		{
			regex:       regexp.MustCompile(`(?i)database.*password.*["'][^"']+["']`),
			description: "Hardcoded database password",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, pattern := range credentialPatterns {
			if pattern.regex.MatchString(line) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("hardcoded_%s_%d", filepath.Base(filePath), i+1),
					Type:        "hardcoded_credentials",
					Severity:    "high",
					Title:       "Hardcoded Credentials",
					Description: pattern.description,
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "general",
					Category:    "environment",
					CWE:         "CWE-798",
					Suggestion:  "Use environment variables or secure configuration management",
					References:  []string{"https://owasp.org/www-project-top-ten/2017/A2_2017-Broken_Authentication"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkInsecurePatterns checks for insecure coding patterns
func (s *Scanner) checkInsecurePatterns(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Patterns for insecure code
	insecurePatterns := []struct {
		regex       *regexp.Regexp
		severity    string
		description string
		suggestion  string
	}{
		{
			regex:       regexp.MustCompile(`(?i)eval\s*\(`),
			severity:    "high",
			description: "Use of eval() function can lead to code injection",
			suggestion:  "Avoid using eval() and use safer alternatives",
		},
		{
			regex:       regexp.MustCompile(`(?i)innerHTML\s*=.*\+`),
			severity:    "medium",
			description: "Potential XSS vulnerability with innerHTML",
			suggestion:  "Use textContent or proper sanitization",
		},
		{
			regex:       regexp.MustCompile(`(?i)document\.write\s*\(`),
			severity:    "medium",
			description: "Use of document.write can be dangerous",
			suggestion:  "Use modern DOM manipulation methods",
		},
		{
			regex:       regexp.MustCompile(`(?i)Math\.random\(\).*crypto`),
			severity:    "high",
			description: "Math.random() used for cryptographic purposes",
			suggestion:  "Use cryptographically secure random number generators",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, pattern := range insecurePatterns {
			if pattern.regex.MatchString(line) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("insecure_%s_%d", filepath.Base(filePath), i+1),
					Type:        "insecure_pattern",
					Severity:    pattern.severity,
					Title:       "Insecure Coding Pattern",
					Description: pattern.description,
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "general",
					Category:    "smart_contract",
					Suggestion:  pattern.suggestion,
					References:  []string{"https://owasp.org/www-project-top-ten/"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkCryptographicIssues checks for cryptographic vulnerabilities
func (s *Scanner) checkCryptographicIssues(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Patterns for cryptographic issues
	cryptoPatterns := []struct {
		regex       *regexp.Regexp
		severity    string
		description string
		suggestion  string
	}{
		{
			regex:       regexp.MustCompile(`(?i)(md5|sha1)\s*\(`),
			severity:    "medium",
			description: "Use of weak cryptographic hash function",
			suggestion:  "Use SHA-256 or stronger hash functions",
		},
		{
			regex:       regexp.MustCompile(`(?i)des|3des|rc4`),
			severity:    "high",
			description: "Use of weak encryption algorithm",
			suggestion:  "Use AES or other modern encryption algorithms",
		},
		{
			regex:       regexp.MustCompile(`(?i)ssl.*v[12]|tls.*v1\.0`),
			severity:    "high",
			description: "Use of deprecated SSL/TLS version",
			suggestion:  "Use TLS 1.2 or higher",
		},
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, pattern := range cryptoPatterns {
			if pattern.regex.MatchString(line) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("crypto_%s_%d", filepath.Base(filePath), i+1),
					Type:        "cryptographic_issue",
					Severity:    pattern.severity,
					Title:       "Cryptographic Vulnerability",
					Description: pattern.description,
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "general",
					Category:    "environment",
					Suggestion:  pattern.suggestion,
					References:  []string{"https://owasp.org/www-project-top-ten/2017/A3_2017-Sensitive_Data_Exposure"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// scanEnvironmentFiles scans environment and configuration files
func (s *Scanner) scanEnvironmentFiles(projectPath string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// Find environment files
	envFiles := []string{".env", ".env.local", ".env.production", "docker-compose.yml", "Dockerfile"}

	for _, envFile := range envFiles {
		filePath := filepath.Join(projectPath, envFile)
		if _, err := os.Stat(filePath); err == nil {
			fileIssues, err := s.scanEnvironmentFile(filePath)
			if err != nil {
				s.logger.Errorf("Failed to scan environment file %s: %v", filePath, err)
				continue
			}
			issues = append(issues, fileIssues...)
		}
	}

	return issues, nil
}

// scanEnvironmentFile scans a specific environment file
func (s *Scanner) scanEnvironmentFile(filePath string) ([]models.SecurityIssue, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var issues []models.SecurityIssue

	// Check for exposed secrets in environment files
	lines := strings.Split(string(content), "\n")
	for i, line := range lines {
		// Skip comments
		if strings.HasPrefix(strings.TrimSpace(line), "#") {
			continue
		}

		// Check for potential secrets
		if strings.Contains(line, "=") && !strings.Contains(line, "YOUR_") && !strings.Contains(line, "REPLACE_") {
			parts := strings.SplitN(line, "=", 2)
			if len(parts) == 2 {
				key := strings.TrimSpace(parts[0])
				value := strings.TrimSpace(parts[1])

				// Check if it looks like a secret
				if len(value) > 10 && !strings.Contains(value, "localhost") && !strings.Contains(value, "example") {
					issues = append(issues, models.SecurityIssue{
						ID:          fmt.Sprintf("env_secret_%s_%d", filepath.Base(filePath), i+1),
						Type:        "environment_secret",
						Severity:    "medium",
						Title:       "Potential Secret in Environment File",
						Description: fmt.Sprintf("Environment variable %s may contain sensitive data", key),
						File:        filePath,
						Line:        i + 1,
						Code:        strings.TrimSpace(line),
						Chain:       "general",
						Category:    "environment",
						Suggestion:  "Ensure this file is not committed to version control",
						References:  []string{"https://12factor.net/config"},
						CreatedAt:   time.Now(),
					})
				}
			}
		}
	}

	return issues, nil
}

// findRelevantFiles finds all files that should be scanned
func (s *Scanner) findRelevantFiles(projectPath string) ([]string, error) {
	var files []string

	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Check if path should be excluded
		for _, excludePath := range s.config.Scanning.Paths.Exclude {
			if strings.Contains(path, excludePath) {
				return nil
			}
		}

		// Check if file type should be included
		for _, fileType := range s.config.Scanning.FileTypes {
			if matched, _ := filepath.Match(fileType, filepath.Base(path)); matched {
				files = append(files, path)
				break
			}
		}

		return nil
	})

	return files, err
}
