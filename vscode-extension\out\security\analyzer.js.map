{"version": 3, "file": "analyzer.js", "sourceRoot": "", "sources": ["../../src/security/analyzer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAsCjC,MAAa,gBAAgB;IAKzB,YACY,SAAuB,EACvB,aAAmC;QADnC,cAAS,GAAT,SAAS,CAAc;QACvB,kBAAa,GAAb,aAAa,CAAsB;QALvC,kBAAa,GAAiC,IAAI,GAAG,EAAE,CAAC;QACxD,eAAU,GAAY,KAAK,CAAC;QAMhC,IAAI,CAAC,oBAAoB,GAAG,MAAM,CAAC,SAAS,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAC;IAC5F,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,WAAmB;QACjC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,IAAI;YACA,gBAAgB;YAChB,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBACpC,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,yCAAyC;gBAChD,WAAW,EAAE,IAAI;aACpB,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACzB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;gBAE/D,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAa,CAAC;gBAElE,IAAI;oBACA,uBAAuB;oBACvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;wBAC1C,YAAY,EAAE,WAAW;wBACzB,MAAM,EAAE,MAAM;wBACd,SAAS,EAAE,MAAM;qBACpB,CAAC,CAAC;oBAEH,IAAI,KAAK,CAAC,uBAAuB,EAAE;wBAC/B,OAAO,IAAI,CAAC;qBACf;oBAED,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;oBAErE,mBAAmB;oBACnB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;oBAErF,IAAI,UAAU,EAAE;wBACZ,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;wBAC1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,mBAAmB,UAAU,CAAC,MAAM,CAAC,MAAM,eAAe,CAC7D,CAAC;qBACL;oBAED,OAAO,UAAU,CAAC;iBACrB;gBAAC,OAAO,KAAK,EAAE;oBACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,KAAK,EAAE,CAAC,CAAC;oBACxD,OAAO,IAAI,CAAC;iBACf;YACL,CAAC,CAAC,CAAC;SACN;gBAAS;YACN,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;SAC3B;IACL,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAC3B,IAAI;YACA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAa,CAAC;YAElE,qCAAqC;YACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE/D,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;gBACzB,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;gBACtD,OAAO,MAAM,CAAC,MAAM,CAAC;aACxB;YAED,OAAO,EAAE,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,EAAE,CAAC;SACb;IACL,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAC/B,MAAc,EACd,QAAmE,EACnE,KAA+B;QAE/B,MAAM,WAAW,GAAG,EAAE,CAAC,CAAC,gBAAgB;QACxC,IAAI,QAAQ,GAAG,CAAC,CAAC;QAEjB,OAAO,QAAQ,GAAG,WAAW,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE;YAC7D,IAAI;gBACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;gBAE1D,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE;oBAC/B,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC,CAAC;oBAChE,OAAO,MAAM,CAAC;iBACjB;qBAAM,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;oBACnC,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;iBAC5C;gBAED,kBAAkB;gBAClB,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;gBACpE,QAAQ,CAAC,MAAM,CAAC;oBACZ,SAAS,EAAE,eAAe,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrF,OAAO,EAAE,gBAAgB,MAAM,CAAC,MAAM,GAAG;iBAC5C,CAAC,CAAC;gBAEH,mCAAmC;gBACnC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBACxD,QAAQ,EAAE,CAAC;aACd;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACpD,QAAQ,EAAE,CAAC;gBACX,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;aAC3D;SACJ;QAED,IAAI,KAAK,CAAC,uBAAuB,EAAE;YAC/B,OAAO,IAAI,CAAC;SACf;QAED,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;IACnE,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,UAAsB;QACnD,6BAA6B;QAC7B,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QAClC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAE3B,uBAAuB;QACvB,MAAM,YAAY,GAAG,IAAI,GAAG,EAA2B,CAAC;QAExD,KAAK,MAAM,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE;YACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBAC7B,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;aAClC;YACD,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC3C;QAED,oBAAoB;QACpB,KAAK,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,YAAY,EAAE;YAC3C,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SAClD;QAED,8BAA8B;QAC9B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,uBAAuB,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACxG,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,MAAuB;QACrE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QAEzC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE;YAC9C,MAAM,WAAW,GAAwB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACxD,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAC7B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CACjC,CAAC;gBAEF,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACpC,KAAK,EACL,IAAI,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,EAAE,EACxE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ,CAAC,CACxC,CAAC;gBAEF,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;gBAC1B,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;gBAE7B,OAAO,UAAU,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;SACnD;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE;YACvD,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;SAClD;IACL,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACrC,QAAQ,QAAQ,EAAE;YACd,KAAK,UAAU,CAAC;YAChB,KAAK,MAAM;gBACP,OAAO,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;YAC3C,KAAK,QAAQ;gBACT,OAAO,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC;YAC7C,KAAK,KAAK;gBACN,OAAO,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC;YACjD,KAAK,MAAM;gBACP,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAC1C;gBACI,OAAO,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC;SAChD;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,MAAuB;QACrE,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CACnD,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,KAAK,QAAQ,CACpD,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC1B,mDAAmD;YACnD,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAElD,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAsC,CAAC;YAExE,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBACxB,MAAM,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACvD,IAAI,CAAC,cAAc,EAAE;oBACjB,SAAS;iBACZ;gBAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;oBACxC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;iBAC7C;gBAED,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAC7B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,EAC3B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CACjC,CAAC;gBAEF,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAE,CAAC,IAAI,CAAC;oBACxC,KAAK;oBACL,YAAY,EAAE,KAAK,KAAK,CAAC,KAAK,SAAS,KAAK,CAAC,WAAW,oBAAoB,KAAK,CAAC,UAAU,GAAG;iBAClG,CAAC,CAAC;aACN;YAED,oBAAoB;YACpB,KAAK,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,IAAI,iBAAiB,EAAE;gBACrD,MAAM,cAAc,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;gBACjD,MAAM,CAAC,cAAc,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;aACtD;SACJ;IACL,CAAC;IAEO,kBAAkB;QACtB,OAAO;YACH,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;gBACnD,eAAe,EAAE,sBAAsB;gBACvC,MAAM,EAAE,eAAe;gBACvB,YAAY,EAAE,KAAK;aACtB,CAAC;YACF,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;gBAC/C,eAAe,EAAE,wBAAwB;gBACzC,MAAM,EAAE,kBAAkB;gBAC1B,YAAY,EAAE,KAAK;aACtB,CAAC;YACF,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;gBACjD,eAAe,EAAE,wBAAwB;gBACzC,MAAM,EAAE,kBAAkB;gBAC1B,YAAY,EAAE,KAAK;aACtB,CAAC;YACF,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,8BAA8B,CAAC;gBAC9C,eAAe,EAAE,sBAAsB;gBACvC,MAAM,EAAE,iBAAiB;gBACzB,YAAY,EAAE,KAAK;aACtB,CAAC;SACL,CAAC;IACN,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,KAAoB;QAC/B,sCAAsC;QACtC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,sBAAsB,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC;IACnG,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAoB;QAClC,2BAA2B;QAC3B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,SAAS,KAAK,CAAC,KAAK,mBAAmB,CAAC,CAAC;IAClF,CAAC;IAED,gBAAgB,CAAC,QAAgB;QAC7B,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;IAClD,CAAC;IAED,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;CACJ;AA/RD,4CA+RC"}