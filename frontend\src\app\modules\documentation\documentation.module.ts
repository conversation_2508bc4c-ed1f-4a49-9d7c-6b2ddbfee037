import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatTableModule } from '@angular/material/table';
import { MatBadgeModule } from '@angular/material/badge';
import { MatTooltipModule } from '@angular/material/tooltip';

import { DocumentationLayoutComponent } from './components/documentation-layout/documentation-layout.component';
import { DocumentationNavComponent } from './components/documentation-nav/documentation-nav.component';
import { GettingStartedComponent } from './components/getting-started/getting-started.component';
import { ApiReferenceComponent } from './components/api-reference/api-reference.component';
import { SecurityPracticesComponent } from './components/security-practices/security-practices.component';
import { CliGuideComponent } from './components/cli-guide/cli-guide.component';
import { VscodeExtensionComponent } from './components/vscode-extension/vscode-extension.component';
import { ArchitectureComponent } from './components/architecture/architecture.component';
import { OverviewComponent } from './components/overview/overview.component';

const routes: Routes = [
  {
    path: '',
    component: DocumentationLayoutComponent,
    children: [
      { path: '', redirectTo: 'overview', pathMatch: 'full' },
      { path: 'overview', component: OverviewComponent },
      { path: 'getting-started', component: GettingStartedComponent },
      { path: 'api-reference', component: ApiReferenceComponent },
      { path: 'security-practices', component: SecurityPracticesComponent },
      { path: 'cli-guide', component: CliGuideComponent },
      { path: 'vscode-extension', component: VscodeExtensionComponent },
      { path: 'architecture', component: ArchitectureComponent }
    ]
  }
];

@NgModule({
  declarations: [
    DocumentationLayoutComponent,
    DocumentationNavComponent,
    GettingStartedComponent,
    ApiReferenceComponent,
    SecurityPracticesComponent,
    CliGuideComponent,
    VscodeExtensionComponent,
    ArchitectureComponent,
    OverviewComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    MatSidenavModule,
    MatToolbarModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatTabsModule,
    MatExpansionModule,
    MatChipsModule,
    MatDividerModule,
    MatTableModule,
    MatBadgeModule,
    MatTooltipModule
  ]
})
export class DocumentationModule { }
