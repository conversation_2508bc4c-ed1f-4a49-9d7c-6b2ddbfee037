import * as vscode from 'vscode';
import WebSocket from 'ws';
import { ConfigurationManager } from '../config/manager';

export interface WebSocketMessage {
    type: string;
    data: any;
    timestamp: string;
    client_id?: string;
}

export interface ScanProgressMessage {
    scan_id: string;
    status: string;
    progress: number;
    current_file?: string;
    files_scanned: number;
    total_files: number;
    issues_found: number;
    message?: string;
}

export interface ScanResultMessage {
    scan_id: string;
    result: any;
    issues: any[];
}

export class WebSocketClient {
    private ws: WebSocket | null = null;
    private reconnectTimer: NodeJS.Timeout | null = null;
    private isConnecting: boolean = false;
    private reconnectAttempts: number = 0;
    private maxReconnectAttempts: number = 5;
    private reconnectDelay: number = 5000; // 5 seconds
    private statusBarItem: vscode.StatusBarItem;

    private eventEmitter = new vscode.EventEmitter<WebSocketMessage>();
    public readonly onMessage = this.eventEmitter.event;

    constructor(private configManager: ConfigurationManager) {
        this.statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Right,
            99
        );
        this.statusBarItem.text = '$(plug) SPT';
        this.statusBarItem.tooltip = 'SPT WebSocket Connection';
        this.statusBarItem.show();

        this.connect();
    }

    private connect(): void {
        if (this.isConnecting || this.ws?.readyState === WebSocket.OPEN) {
            return;
        }

        this.isConnecting = true;
        this.updateStatusBar('connecting');

        const serverUrl = this.configManager.getServerUrl();
        const wsUrl = serverUrl.replace(/^http/, 'ws') + '/ws';

        try {
            this.ws = new WebSocket(wsUrl);

            if (this.ws) {
                this.ws.on('open', () => {
                console.log('SPT WebSocket connected');
                this.isConnecting = false;
                this.reconnectAttempts = 0;
                this.updateStatusBar('connected');

                // Send initial message
                this.send({
                    type: 'subscribe',
                    data: {
                        client_type: 'vscode-extension',
                        version: '1.0.0'
                    },
                    timestamp: new Date().toISOString()
                });
            });

                this.ws.on('message', (data: WebSocket.Data) => {
                    try {
                        const message: WebSocketMessage = JSON.parse(data.toString());
                        this.handleMessage(message);
                    } catch (error) {
                        console.error('Failed to parse WebSocket message:', error);
                    }
                });

                this.ws.on('close', (code: number, reason: string) => {
                    console.log(`SPT WebSocket closed: ${code} - ${reason}`);
                    this.isConnecting = false;
                    this.ws = null;
                    this.updateStatusBar('disconnected');
                    this.scheduleReconnect();
                });

                this.ws.on('error', (error: Error) => {
                    console.error('SPT WebSocket error:', error);
                    this.isConnecting = false;
                    this.updateStatusBar('error');
                    this.scheduleReconnect();
                });
            }

        } catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.isConnecting = false;
            this.updateStatusBar('error');
            this.scheduleReconnect();
        }
    }

    private handleMessage(message: WebSocketMessage): void {
        console.log('Received WebSocket message:', message.type);

        switch (message.type) {
            case 'welcome':
                vscode.window.showInformationMessage('Connected to SPT real-time updates');
                break;

            case 'scan_progress':
                this.handleScanProgress(message.data as ScanProgressMessage);
                break;

            case 'scan_result':
                this.handleScanResult(message.data as ScanResultMessage);
                break;

            case 'system_alert':
                this.handleSystemAlert(message.data);
                break;

            case 'pong':
                // Handle ping/pong for keep-alive
                break;

            default:
                console.log('Unknown message type:', message.type);
        }

        // Emit event for other components to listen
        this.eventEmitter.fire(message);
    }

    private handleScanProgress(progress: ScanProgressMessage): void {
        const progressPercent = Math.round(progress.progress * 100);
        
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: `Scanning: ${progress.scan_id}`,
            cancellable: false
        }, async (progressReporter) => {
            progressReporter.report({
                increment: progressPercent,
                message: `${progress.status} - ${progress.files_scanned}/${progress.total_files} files (${progress.issues_found} issues found)`
            });
        });

        // Update status bar with scan progress
        this.statusBarItem.text = `$(sync~spin) SPT: ${progressPercent}%`;
        this.statusBarItem.tooltip = `Scanning in progress: ${progress.current_file || 'Processing...'}`;
    }

    private handleScanResult(result: ScanResultMessage): void {
        const issueCount = result.issues.length;
        const message = issueCount > 0 
            ? `Scan completed: ${issueCount} security issues found`
            : 'Scan completed: No security issues found';

        vscode.window.showInformationMessage(message, 'View Results').then(selection => {
            if (selection === 'View Results') {
                vscode.commands.executeCommand('spt.showSecurityReport');
            }
        });

        // Reset status bar
        this.updateStatusBar('connected');
    }

    private handleSystemAlert(alert: any): void {
        const alertType = alert.alert_type || 'info';
        const message = alert.message || 'System alert received';

        switch (alertType) {
            case 'error':
                vscode.window.showErrorMessage(`SPT Alert: ${message}`);
                break;
            case 'warning':
                vscode.window.showWarningMessage(`SPT Alert: ${message}`);
                break;
            default:
                vscode.window.showInformationMessage(`SPT Alert: ${message}`);
        }
    }

    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
            this.updateStatusBar('failed');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts;

        console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, delay);
    }

    private updateStatusBar(status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'failed'): void {
        switch (status) {
            case 'connecting':
                this.statusBarItem.text = '$(sync~spin) SPT';
                this.statusBarItem.tooltip = 'Connecting to SPT server...';
                this.statusBarItem.backgroundColor = undefined;
                break;
            case 'connected':
                this.statusBarItem.text = '$(plug) SPT';
                this.statusBarItem.tooltip = 'Connected to SPT server';
                this.statusBarItem.backgroundColor = undefined;
                break;
            case 'disconnected':
                this.statusBarItem.text = '$(debug-disconnect) SPT';
                this.statusBarItem.tooltip = 'Disconnected from SPT server';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
                break;
            case 'error':
                this.statusBarItem.text = '$(error) SPT';
                this.statusBarItem.tooltip = 'SPT connection error';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
                break;
            case 'failed':
                this.statusBarItem.text = '$(x) SPT';
                this.statusBarItem.tooltip = 'SPT connection failed - click to retry';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
                this.statusBarItem.command = 'spt.reconnectWebSocket';
                break;
        }
    }

    public send(message: WebSocketMessage): void {
        if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.warn('WebSocket not connected, message not sent:', message);
        }
    }

    public reconnect(): void {
        this.disconnect();
        this.reconnectAttempts = 0;
        this.connect();
    }

    public disconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }

        this.isConnecting = false;
        this.updateStatusBar('disconnected');
    }

    public isConnected(): boolean {
        return this.ws?.readyState === WebSocket.OPEN;
    }

    public getConnectionStatus(): string {
        if (!this.ws) {
            return 'disconnected';
        }
        
        switch (this.ws.readyState) {
            case WebSocket.CONNECTING:
                return 'connecting';
            case WebSocket.OPEN:
                return 'connected';
            case WebSocket.CLOSING:
                return 'closing';
            case WebSocket.CLOSED:
                return 'closed';
            default:
                return 'unknown';
        }
    }

    public dispose(): void {
        this.disconnect();
        this.statusBarItem.dispose();
        this.eventEmitter.dispose();
    }
}
