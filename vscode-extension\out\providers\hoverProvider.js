"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HoverProvider = void 0;
const vscode = __importStar(require("vscode"));
class HoverProvider {
    constructor(securityAnalyzer) {
        this.securityAnalyzer = securityAnalyzer;
    }
    provideHover(document, position, token) {
        const issues = this.securityAnalyzer.getIssuesForFile(document.uri.fsPath);
        for (const issue of issues) {
            const issueRange = new vscode.Range(Math.max(0, issue.line - 1), Math.max(0, issue.column - 1), Math.max(0, issue.line - 1), Math.max(0, issue.column + 10));
            if (issueRange.contains(position)) {
                const markdown = new vscode.MarkdownString();
                markdown.appendMarkdown(`**🛡️ Security Issue: ${issue.title}**\n\n`);
                markdown.appendMarkdown(`**Severity:** ${issue.severity.toUpperCase()}\n\n`);
                markdown.appendMarkdown(`**Description:** ${issue.description}\n\n`);
                markdown.appendMarkdown(`**Suggestion:** ${issue.suggestion}\n\n`);
                if (issue.references && issue.references.length > 0) {
                    markdown.appendMarkdown(`**References:**\n`);
                    issue.references.forEach(ref => {
                        markdown.appendMarkdown(`- [${ref}](${ref})\n`);
                    });
                }
                return new vscode.Hover(markdown, issueRange);
            }
        }
        return undefined;
    }
}
exports.HoverProvider = HoverProvider;
//# sourceMappingURL=hoverProvider.js.map