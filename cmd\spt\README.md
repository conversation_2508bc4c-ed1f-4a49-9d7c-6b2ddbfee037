# SPT CLI - Security Penetration Testing Tool

## Overview

SPT CLI is a comprehensive command-line interface for blockchain security analysis. It provides powerful tools for scanning smart contracts, analyzing dependencies, checking environment configurations, and generating detailed security reports.

## Installation

### From Source
```bash
git clone https://blockchain-spt/spt.git
cd spt
go build -o spt cmdmain.go
```

### Using Go Install
```bash
go install blockchain-spt/cmd/spt@latest
```

### Binary Releases
Download pre-built binaries from the [releases page](https://blockchain-spt/releases).

## Quick Start

```bash
# Initialize configuration
spt config init

# Scan current directory for all security issues
spt scan

# Audit Ethereum smart contracts
spt audit ethereum ./contracts

# Check dependencies for vulnerabilities
spt check deps

# Generate security report
spt report scan-results.json --format html
```

## Commands

### `spt scan` - Security Scanning

Perform comprehensive security scans on files and directories.

```bash
# Basic usage
spt scan                                    # Scan current directory
spt scan ./project                          # Scan specific directory
spt scan ./contract.sol                     # Scan specific file

# Chain-specific scanning
spt scan --chain ethereum ./contracts      # Ethereum only
spt scan --chain bitcoin ./wallet          # Bitcoin only

# Type-specific scanning
spt scan --type contracts ./src            # Smart contracts only
spt scan --type dependencies ./            # Dependencies only
spt scan --type environment ./config       # Environment configs only

# Filtering and output
spt scan --severity high ./                 # High severity and above
spt scan --format json --output report.json # JSON output
spt scan --fail-on-issues ./                # Exit with error if issues found

# Advanced options
spt scan --recursive=false ./               # Non-recursive scan
spt scan --include "*.sol,*.js" ./          # Include specific patterns
spt scan --exclude "test/*" ./              # Exclude patterns
spt scan --timeout 10m ./                   # Set scan timeout
spt scan --parallel 8 ./                    # Parallel workers
```

### `spt audit` - Security Auditing

Perform comprehensive security audits with detailed analysis.

```bash
# Ethereum auditing
spt audit ethereum ./contracts             # Audit Ethereum contracts
spt audit ethereum --depth deep ./src      # Deep analysis
spt audit ethereum --interactive ./token   # Interactive mode
spt audit ethereum --generate-report ./    # Generate audit report

# Bitcoin auditing
spt audit bitcoin ./wallet                 # Audit Bitcoin code
spt audit bitcoin --depth deep ./scripts   # Deep script analysis

# General contract auditing
spt audit contracts ./                      # All supported chains
spt audit contracts --chain ethereum ./src # Specific chain
```

### `spt check` - Quick Security Checks

Perform fast, targeted security checks.

```bash
# Dependency checks
spt check deps                             # Check current directory
spt check deps ./package.json              # Check specific file
spt check deps --quick ./                  # Quick vulnerability check
spt check deps --fix ./                    # Show fix suggestions

# Environment checks
spt check env                              # Check environment configs
spt check env .env                         # Check specific env file
spt check env --quick ./config             # Quick config check

# Key and secret checks
spt check keys                             # Check for exposed secrets
spt check keys ./src                       # Check specific directory
spt check keys --quick ./                  # Quick secret scan

# CI/CD checks
spt check cicd                             # Check CI/CD configs
spt check cicd .github/workflows           # Check GitHub Actions
spt check cicd --quick ./                  # Quick CI/CD check
```

### `spt analyze` - Code Analysis

Perform detailed code analysis with metrics and insights.

```bash
# Ethereum analysis
spt analyze ethereum ./contracts           # Analyze Ethereum contracts
spt analyze ethereum --detailed ./token    # Detailed analysis
spt analyze ethereum --metrics ./src       # Include metrics
spt analyze ethereum --performance ./      # Performance analysis

# Bitcoin analysis
spt analyze bitcoin ./wallet               # Analyze Bitcoin code
spt analyze bitcoin --detailed ./scripts   # Detailed analysis

# General analysis
spt analyze contract ./                     # All supported chains
spt analyze contract --chain ethereum ./   # Specific chain
spt analyze contract --complexity ./       # Complexity analysis
```

### `spt report` - Report Generation

Generate comprehensive security reports from scan results.

```bash
# Basic report generation
spt report scan-results.json               # Generate HTML report
spt report --format pdf results.json       # Generate PDF report
spt report --format json results.json      # Generate JSON report

# Report customization
spt report --template executive results.json # Executive summary
spt report --template technical results.json # Technical details
spt report --title "Security Audit" results.json # Custom title

# Filtering and options
spt report --severity high results.json    # High severity only
spt report --include "vulnerability" results.json # Include specific types
spt report --exclude "low_priority" results.json # Exclude types
spt report --detailed results.json         # Include detailed info
spt report --charts results.json           # Include visualizations
```

### `spt config` - Configuration Management

Manage SPT configuration files and settings.

```bash
# Configuration management
spt config init                            # Initialize default config
spt config init --output ~/.spt.yaml       # Custom location
spt config init --force                    # Overwrite existing

spt config show                            # Show current config
spt config validate                        # Validate config file
spt config validate spt.yaml              # Validate specific file

spt config set security.level strict       # Set configuration value
spt config set logging.level debug         # Set log level
```

### `spt update` - Update Management

Update SPT and security databases.

```bash
# Update management
spt update                                  # Update everything
spt update --check                         # Check for updates only
spt update --version 1.2.0                # Update to specific version
spt update --channel beta                  # Use beta channel
spt update --force                         # Force update
```

### `spt server` - Web Server

Start the SPT web server for browser-based analysis.

```bash
# Server management
spt server                                 # Start on default port (3000)
spt server --port 8080                     # Custom port
spt server --host 0.0.0.0                  # Bind to all interfaces

# Security options
spt server --tls --cert cert.pem --key key.pem # Enable TLS
spt server --auth                          # Enable authentication
spt server --cors                          # Enable CORS

# Development
spt server --dev                           # Development mode
```

## Global Flags

All commands support these global flags:

```bash
--config, -c        Configuration file path
--log-level, -l     Log level (debug, info, warn, error)
--verbose, -v       Verbose output
--quiet, -q         Quiet mode (minimal output)
--output-format     Output format (table, json, yaml, csv)
```

## Configuration

### Configuration File

SPT uses YAML configuration files. Initialize with:

```bash
spt config init
```

Example configuration:

```yaml
environment: development
security:
  level: medium
  rules:
    private_key_exposure:
      enabled: true
      ethereum:
        private_keys: true
        addresses: true
        seeds: true
        rpc: true
      bitcoin:
        private_keys: true
        addresses: true
        seeds: true
        rpc: true
scanning:
  paths:
    include:
      - "*.sol"
      - "*.js"
      - "*.py"
      - "*.json"
      - "*.yml"
    exclude:
      - "node_modules"
      - "vendor"
      - ".git"
      - "build"
      - "dist"
  timeout: 300
logging:
  level: info
  format: text
  output: stdout
```

### Environment Variables

Override configuration with environment variables:

```bash
export SPT_SECURITY_LEVEL=strict
export SPT_LOG_LEVEL=debug
export SPT_SCAN_TIMEOUT=600
```

## Output Formats

SPT supports multiple output formats:

### Table (Default)
Human-readable table format with colors and formatting.

### JSON
Machine-readable JSON format for integration:

```json
{
  "scan_time": "2023-12-15T10:00:00Z",
  "total_issues": 5,
  "issues": [
    {
      "id": "ETH-001",
      "type": "reentrancy_vulnerability",
      "severity": "high",
      "title": "Reentrancy Vulnerability",
      "description": "Function is vulnerable to reentrancy attacks",
      "file": "contract.sol",
      "line": 42,
      "chain": "ethereum",
      "category": "smart_contract"
    }
  ]
}
```

### YAML
YAML format for configuration and human-readable output.

### CSV
CSV format for data analysis and spreadsheet import.

## Examples

### Complete Project Security Audit

```bash
# 1. Initialize configuration
spt config init

# 2. Comprehensive scan
spt scan --format json --output scan-results.json ./project

# 3. Detailed audit
spt audit contracts --generate-report --report-path audit-report.html ./

# 4. Check specific components
spt check deps --fix ./
spt check env ./
spt check keys ./

# 5. Generate executive report
spt report scan-results.json --template executive --format pdf
```

### CI/CD Integration

```yaml
# .github/workflows/security.yml
name: Security Scan
on: [push, pull_request]
jobs:
  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Install SPT
      run: go install blockchain-spt/cmd/spt@latest
    - name: Security Scan
      run: |
        spt scan --format json --output security-report.json --fail-on-issues ./
    - name: Upload Results
      uses: actions/upload-artifact@v3
      with:
        name: security-report
        path: security-report.json
```

### Docker Integration

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o spt cmdmain.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/spt .
ENTRYPOINT ["./spt"]
```

## Exit Codes

- `0` - Success, no issues found
- `1` - Issues found (when using --fail-on-issues)
- `2` - Configuration error
- `3` - Runtime error
- `4` - Invalid arguments

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   chmod +x spt
   ```

2. **Configuration Not Found**
   ```bash
   spt config init
   ```

3. **Scan Timeout**
   ```bash
   spt scan --timeout 10m ./
   ```

4. **Memory Issues with Large Projects**
   ```bash
   spt scan --exclude "node_modules,vendor" ./
   ```

### Debug Mode

Enable debug logging for troubleshooting:

```bash
spt --log-level debug scan ./
```

### Getting Help

```bash
spt --help                    # General help
spt scan --help              # Command-specific help
spt config validate --help   # Subcommand help
```

## Integration

### IDE Integration

SPT can be integrated with popular IDEs:

- **VS Code**: Use the SPT extension
- **IntelliJ**: Configure as external tool
- **Vim**: Use ALE plugin integration

### API Integration

Use the web server mode for API integration:

```bash
spt server --port 8080
curl -X POST http://localhost:8080/api/scan -d '{"path": "./project"}'
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

- Documentation: https://docs.spt.dev
- Issues: https://blockchain-spt/issues
- Discussions: https://blockchain-spt/discussions
- Discord: https://discord.gg/spt
