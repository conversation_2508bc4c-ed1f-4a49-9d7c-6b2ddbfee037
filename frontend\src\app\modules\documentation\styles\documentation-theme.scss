// Documentation Theme Variables
:root {
  // Primary Colors (Pastel)
  --doc-primary: #667eea;
  --doc-primary-light: #8fa4f3;
  --doc-primary-dark: #4c63d2;
  
  // Secondary Colors (Pastel)
  --doc-secondary: #764ba2;
  --doc-accent: #f093fb;
  --doc-accent-alt: #f5576c;
  
  // Gradient Colors
  --doc-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --doc-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --doc-gradient-tertiary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --doc-gradient-quaternary: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  
  // Background Colors
  --doc-bg-primary: #ffffff;
  --doc-bg-secondary: #f8f9ff;
  --doc-bg-tertiary: #f0f4ff;
  --doc-bg-card: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  
  // Text Colors
  --doc-text-primary: #2d3748;
  --doc-text-secondary: #4a5568;
  --doc-text-muted: #64748b;
  --doc-text-light: #8892b0;
  
  // Border Colors
  --doc-border-light: #e8eaff;
  --doc-border-medium: #d1d9ff;
  --doc-border-dark: #b8c5ff;
  
  // Shadow Colors
  --doc-shadow-light: rgba(102, 126, 234, 0.1);
  --doc-shadow-medium: rgba(102, 126, 234, 0.2);
  --doc-shadow-dark: rgba(102, 126, 234, 0.3);
  
  // Spacing
  --doc-spacing-xs: 4px;
  --doc-spacing-sm: 8px;
  --doc-spacing-md: 16px;
  --doc-spacing-lg: 24px;
  --doc-spacing-xl: 32px;
  --doc-spacing-2xl: 48px;
  --doc-spacing-3xl: 64px;
  
  // Border Radius
  --doc-radius-sm: 8px;
  --doc-radius-md: 12px;
  --doc-radius-lg: 16px;
  --doc-radius-xl: 20px;
  --doc-radius-2xl: 24px;
  --doc-radius-full: 50px;
  
  // Typography
  --doc-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --doc-font-mono: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
}

// Global Documentation Styles
.documentation-container {
  font-family: var(--doc-font-family);
  
  // Headings
  h1, h2, h3, h4, h5, h6 {
    color: var(--doc-text-primary);
    font-weight: 600;
    line-height: 1.3;
    letter-spacing: -0.025em;
  }
  
  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }
  
  h2 {
    font-size: 2rem;
    margin-bottom: 0.875rem;
  }
  
  h3 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
  }
  
  h4 {
    font-size: 1.25rem;
    margin-bottom: 0.625rem;
  }
  
  // Paragraphs
  p {
    color: var(--doc-text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
  }
  
  // Links
  a {
    color: var(--doc-primary);
    text-decoration: none;
    transition: color 0.2s ease;
    
    &:hover {
      color: var(--doc-primary-dark);
    }
  }
  
  // Code blocks
  code {
    font-family: var(--doc-font-mono);
    background: var(--doc-bg-tertiary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--doc-radius-sm);
    font-size: 0.875em;
    color: var(--doc-text-primary);
    border: 1px solid var(--doc-border-light);
  }
  
  pre {
    background: var(--doc-bg-tertiary);
    padding: 1rem;
    border-radius: var(--doc-radius-md);
    overflow-x: auto;
    border: 1px solid var(--doc-border-light);
    
    code {
      background: none;
      padding: 0;
      border: none;
      border-radius: 0;
    }
  }
  
  // Lists
  ul, ol {
    color: var(--doc-text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
    
    li {
      margin-bottom: 0.5rem;
    }
  }
  
  // Tables
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
    
    th, td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid var(--doc-border-light);
    }
    
    th {
      background: var(--doc-bg-secondary);
      font-weight: 600;
      color: var(--doc-text-primary);
    }
    
    td {
      color: var(--doc-text-secondary);
    }
  }
}

// Material Design Overrides for Documentation
.documentation-container {
  .mat-mdc-card {
    border-radius: var(--doc-radius-xl) !important;
    box-shadow: 0 8px 32px var(--doc-shadow-light) !important;
    border: 1px solid var(--doc-border-light);
    
    &:hover {
      box-shadow: 0 12px 40px var(--doc-shadow-medium) !important;
    }
  }
  
  .mat-mdc-button {
    border-radius: var(--doc-radius-full) !important;
    font-weight: 500 !important;
    text-transform: none !important;
    letter-spacing: 0.025em !important;
  }
  
  .mat-mdc-raised-button {
    box-shadow: 0 4px 12px var(--doc-shadow-light) !important;
    
    &:hover {
      box-shadow: 0 6px 16px var(--doc-shadow-medium) !important;
    }
  }
  
  .mat-mdc-chip {
    border-radius: var(--doc-radius-full) !important;
    font-weight: 500 !important;
  }
  
  .mat-expansion-panel {
    border-radius: var(--doc-radius-md) !important;
    margin-bottom: var(--doc-spacing-sm) !important;
    box-shadow: 0 2px 8px var(--doc-shadow-light) !important;
    border: 1px solid var(--doc-border-light) !important;
  }
  
  .mat-tab-group {
    .mat-mdc-tab {
      font-weight: 500 !important;
      text-transform: none !important;
    }
  }
}

// Utility Classes
.doc-gradient-text {
  background: var(--doc-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.doc-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px var(--doc-shadow-medium) !important;
  }
}

.doc-glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// Responsive Design
@media (max-width: 768px) {
  .documentation-container {
    h1 {
      font-size: 2rem;
    }
    
    h2 {
      font-size: 1.75rem;
    }
    
    h3 {
      font-size: 1.375rem;
    }
  }
}

@media (max-width: 480px) {
  .documentation-container {
    h1 {
      font-size: 1.75rem;
    }
    
    h2 {
      font-size: 1.5rem;
    }
    
    h3 {
      font-size: 1.25rem;
    }
  }
}
