import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatToolbarModule } from '@angular/material/toolbar';
import { AuthService, LoginRequest } from '../../services/auth.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatCheckboxModule,
    MatToolbarModule
  ],
  template: `
    <div class="login-container">
      <!-- Top Navigation Bar -->
      <mat-toolbar class="login-toolbar">
        <div class="toolbar-brand">
          <mat-icon class="brand-icon">shield</mat-icon>
          <span class="brand-title">SPT - Blockchain Security Protocol Tool</span>
        </div>
        <span class="spacer"></span>
        <button mat-stroked-button routerLink="/doc" class="doc-btn">
          <mat-icon>description</mat-icon>
          Documentation
        </button>
      </mat-toolbar>

      <div class="login-content">
        <div class="login-card-wrapper">
          <mat-card class="login-card">
            <mat-card-header class="login-header">
              <div class="logo">
                <mat-icon class="logo-icon">security</mat-icon>
                <h1>Welcome Back</h1>
              </div>
              <mat-card-subtitle>Sign in to access your security dashboard</mat-card-subtitle>
            </mat-card-header>

          <mat-card-content>
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
              <div class="form-fields">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Username</mat-label>
                  <input matInput formControlName="username" autocomplete="username">
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="loginForm.get('username')?.hasError('required')">
                    Username is required
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Password</mat-label>
                  <input matInput 
                         [type]="hidePassword ? 'password' : 'text'" 
                         formControlName="password"
                         autocomplete="current-password">
                  <button mat-icon-button matSuffix 
                          (click)="hidePassword = !hidePassword" 
                          [attr.aria-label]="'Hide password'" 
                          [attr.aria-pressed]="hidePassword"
                          type="button">
                    <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
                  </button>
                  <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                    Password is required
                  </mat-error>
                </mat-form-field>

                <div class="form-options">
                  <mat-checkbox formControlName="rememberMe">
                    Remember me
                  </mat-checkbox>
                  <a href="#" class="forgot-password">Forgot password?</a>
                </div>
              </div>

              <div class="form-actions">
                <button mat-raised-button 
                        color="primary" 
                        type="submit" 
                        class="login-button"
                        [disabled]="loginForm.invalid || isLoading">
                  <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
                  <span *ngIf="!isLoading">Sign In</span>
                </button>

                <button mat-button 
                        type="button" 
                        class="demo-button"
                        (click)="loginWithDemo()"
                        [disabled]="isLoading">
                  Demo Login
                </button>
              </div>
            </form>
          </mat-card-content>

          <mat-card-actions class="card-actions">
            <p>Don't have an account? 
              <a routerLink="/register" class="register-link">Sign up</a>
            </p>
          </mat-card-actions>
        </mat-card>

        <div class="features-info">
          <h3>🛡️ Security Features</h3>
          <ul>
            <li>✅ Smart Contract Analysis</li>
            <li>✅ Vulnerability Detection</li>
            <li>✅ Real-time Monitoring</li>
            <li>✅ Comprehensive Reports</li>
          </ul>
        </div>
      </div>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    }

    .login-toolbar {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
      padding: 0 24px;
      min-height: 72px;
    }

    .toolbar-brand {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .brand-icon {
      font-size: 28px;
      width: 28px;
      height: 28px;
    }

    .brand-title {
      font-size: 1.2em;
      font-weight: 600;
      letter-spacing: -0.5px;
    }

    .spacer {
      flex: 1 1 auto;
    }

    .doc-btn {
      background: rgba(255, 255, 255, 0.15);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      border-radius: 24px;
      padding: 8px 20px;
      font-weight: 500;
    }

    .doc-btn:hover {
      background: rgba(255, 255, 255, 0.25);
    }

    .login-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
    }

    .login-card-wrapper {
      display: flex;
      gap: 40px;
      align-items: center;
      max-width: 900px;
      width: 100%;
    }

    .login-card {
      flex: 1;
      max-width: 400px;
      padding: 20px;
    }

    .login-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      margin-bottom: 10px;
    }

    .logo-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #667eea;
    }

    .logo h1 {
      margin: 0;
      color: #333;
      font-weight: 300;
    }

    .form-fields {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-bottom: 30px;
    }

    .full-width {
      width: 100%;
    }

    .form-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
    }

    .forgot-password {
      color: #667eea;
      text-decoration: none;
      font-size: 14px;
    }

    .forgot-password:hover {
      text-decoration: underline;
    }

    .form-actions {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .login-button {
      height: 48px;
      font-size: 16px;
      font-weight: 500;
    }

    .demo-button {
      height: 40px;
      color: #667eea;
    }

    .card-actions {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid #e0e0e0;
    }

    .register-link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
    }

    .register-link:hover {
      text-decoration: underline;
    }

    .features-info {
      flex: 1;
      color: white;
      padding: 40px;
    }

    .features-info h3 {
      font-size: 24px;
      margin-bottom: 20px;
      font-weight: 300;
    }

    .features-info ul {
      list-style: none;
      padding: 0;
    }

    .features-info li {
      padding: 10px 0;
      font-size: 16px;
      opacity: 0.9;
    }

    @media (max-width: 768px) {
      .login-card-wrapper {
        flex-direction: column;
        gap: 20px;
      }

      .features-info {
        order: -1;
        padding: 20px;
        text-align: center;
      }

      .login-card {
        max-width: 100%;
      }
    }

    mat-spinner {
      margin-right: 10px;
    }
  `]
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  hidePassword = true;
  isLoading = false;
  returnUrl = '/dashboard';

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.loginForm = this.formBuilder.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required]],
      rememberMe: [false]
    });
  }

  ngOnInit(): void {
    // Get return URL from route parameters or default to dashboard
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.isLoading = true;
      
      const credentials: LoginRequest = {
        username: this.loginForm.value.username,
        password: this.loginForm.value.password
      };

      this.authService.login(credentials).subscribe({
        next: (response) => {
          this.isLoading = false;
          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
          this.router.navigate([this.returnUrl]);
        },
        error: (error) => {
          this.isLoading = false;
          this.snackBar.open(error.message || 'Login failed', 'Close', { duration: 5000 });
        }
      });
    }
  }

  loginWithDemo(): void {
    this.isLoading = true;
    
    // Use mock login for development
    setTimeout(() => {
      this.authService.mockLogin('admin', 'admin');
      this.isLoading = false;
      this.snackBar.open('Demo login successful!', 'Close', { duration: 3000 });
      this.router.navigate([this.returnUrl]);
    }, 1000);
  }
}
