import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DocumentationNavComponent } from '../documentation-nav/documentation-nav.component';

@Component({
  selector: 'app-documentation-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatSidenavModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    DocumentationNavComponent
  ],
  template: `
    <div class="spt-documentation-container">
      <!-- Enhanced Header -->
      <header class="spt-doc-header">
        <div class="spt-header-content">
          <div class="spt-brand">
            <div class="spt-brand-icon">
              <mat-icon>shield</mat-icon>
            </div>
            <div class="spt-brand-text">
              <h1 class="spt-brand-title">SPT Documentation</h1>
              <p class="spt-brand-subtitle">Security Protocol Tool</p>
            </div>
          </div>

          <nav class="spt-header-nav">
            <div class="spt-nav-actions">
              <button mat-button class="spt-nav-btn" routerLink="/dashboard">
                <mat-icon>dashboard</mat-icon>
                <span>Dashboard</span>
              </button>
              <button mat-button class="spt-nav-btn" routerLink="/scan">
                <mat-icon>security</mat-icon>
                <span>Scan</span>
              </button>
              <button mat-raised-button color="primary" routerLink="/dashboard" class="spt-back-btn">
                <mat-icon>arrow_back</mat-icon>
                <span>Back to App</span>
              </button>
            </div>
          </nav>
        </div>
      </header>

      <!-- Main Content Area -->
      <div class="spt-doc-main">
        <mat-sidenav-container class="spt-sidenav-container">
          <mat-sidenav mode="side" opened="true" class="spt-sidebar">
            <div class="spt-sidebar-content">
              <app-documentation-nav></app-documentation-nav>
            </div>
          </mat-sidenav>

          <mat-sidenav-content class="spt-content">
            <main class="spt-content-wrapper">
              <router-outlet></router-outlet>
            </main>
          </mat-sidenav-content>
        </mat-sidenav-container>
      </div>
    </div>
  `,
  styles: [`
    /* Main Container */
    .spt-documentation-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
      background: var(--spt-gray-50);
      font-family: 'Inter', sans-serif;
    }

    /* Enhanced Header */
    .spt-doc-header {
      background: white;
      border-bottom: 1px solid var(--spt-gray-200);
      box-shadow: var(--spt-shadow-sm);
      position: sticky;
      top: 0;
      z-index: var(--spt-z-sticky);
    }

    .spt-header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 var(--spt-space-8);
      min-height: 72px;
      margin: 0 auto;
    }

    /* Brand Section */
    .spt-brand {
      display: flex;
      align-items: center;
      gap: var(--spt-space-4);
    }

    .spt-brand-icon {
      background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);
      border-radius: var(--spt-radius-xl);
      padding: var(--spt-space-3);
      box-shadow: var(--spt-shadow-md);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .spt-brand-icon mat-icon {
      color: white;
      font-size: 28px;
      width: 28px;
      height: 28px;
    }

    .spt-brand-text {
      display: flex;
      flex-direction: column;
      gap: var(--spt-space-1);
    }

    .spt-brand-title {
      font-size: var(--spt-text-2xl);
      font-weight: var(--spt-font-bold);
      color: var(--spt-gray-900);
      margin: 0;
      letter-spacing: -0.025em;
    }

    .spt-brand-subtitle {
      font-size: var(--spt-text-sm);
      color: var(--spt-gray-600);
      margin: 0;
      font-weight: var(--spt-font-medium);
    }

    /* Header Navigation */
    .spt-header-nav {
      display: flex;
      align-items: center;
    }

    .spt-nav-actions {
      display: flex;
      align-items: center;
      gap: var(--spt-space-2);
    }

    .spt-nav-btn {
      color: var(--spt-gray-600) !important;
      font-weight: var(--spt-font-medium) !important;
      border-radius: var(--spt-radius-lg) !important;
      padding: var(--spt-space-2) var(--spt-space-3) !important;
      transition: all 0.2s ease !important;
    }

    .spt-nav-btn:hover {
      background: var(--spt-gray-100) !important;
      color: var(--spt-gray-900) !important;
    }

    .spt-nav-btn mat-icon {
      margin-right: var(--spt-space-2);
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .spt-back-btn {
      background: var(--spt-primary-600) !important;
      color: white !important;
      font-weight: var(--spt-font-semibold) !important;
      border-radius: var(--spt-radius-lg) !important;
      padding: var(--spt-space-3) var(--spt-space-5) !important;
      box-shadow: var(--spt-shadow-sm) !important;
      transition: all 0.2s ease !important;
    }

    .spt-back-btn:hover {
      background: var(--spt-primary-700) !important;
      box-shadow: var(--spt-shadow-md) !important;
      transform: translateY(-1px);
    }

    .spt-back-btn mat-icon {
      margin-right: var(--spt-space-2);
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    /* Main Content Area */
    .spt-doc-main {
      flex: 1;
      display: flex;
      overflow: hidden;
    }

    .spt-sidenav-container {
      flex: 1;
      background: transparent;
    }

    /* Sidebar */
    .spt-sidebar {
      width: 320px;
      background: white;
      border-right: 1px solid var(--spt-gray-200);
      box-shadow: none;
    }

    .spt-sidebar-content {
      height: 100%;
      overflow-y: auto;
      padding: var(--spt-space-6) 0;
    }

    /* Content Area */
    .spt-content {
      background: transparent;
      overflow-y: auto;
    }

    .spt-content-wrapper {
      padding: var(--spt-space-8);
      max-width: 1200px;
      margin: 0 auto;
      min-height: calc(100vh - 72px);
    }

    /* Global Icon Fixes */
    mat-icon {
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      vertical-align: middle !important;
      line-height: 1 !important;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
      .spt-header-content {
        padding: 0 var(--spt-space-6);
      }

      .spt-content-wrapper {
        padding: var(--spt-space-6);
      }
    }

    @media (max-width: 1024px) {
      .spt-sidebar {
        width: 280px;
      }

      .spt-header-content {
        padding: 0 var(--spt-space-4);
      }

      .spt-content-wrapper {
        padding: var(--spt-space-5);
      }

      .spt-nav-btn span {
        display: none;
      }

      .spt-nav-btn {
        min-width: 44px;
        padding: var(--spt-space-2) !important;
      }
    }

    @media (max-width: 768px) {
      .spt-header-content {
        min-height: 64px;
        padding: 0 var(--spt-space-4);
      }

      .spt-brand-title {
        font-size: var(--spt-text-xl);
      }

      .spt-brand-subtitle {
        font-size: var(--spt-text-xs);
      }

      .spt-sidebar {
        width: 260px;
      }

      .spt-content-wrapper {
        padding: var(--spt-space-4);
      }

      .spt-nav-actions {
        gap: var(--spt-space-1);
      }

      .spt-back-btn {
        padding: var(--spt-space-2) var(--spt-space-3) !important;
        font-size: var(--spt-text-sm) !important;
      }
    }

    @media (max-width: 640px) {
      .spt-brand {
        gap: var(--spt-space-3);
      }

      .spt-brand-icon {
        padding: var(--spt-space-2);
      }

      .spt-brand-icon mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      .spt-nav-btn {
        display: none;
      }

      .spt-back-btn span {
        display: none;
      }

      .spt-back-btn {
        min-width: 44px;
        padding: var(--spt-space-2) !important;
        border-radius: 50% !important;
      }

      .spt-back-btn mat-icon {
        margin: 0;
      }

      .spt-sidebar {
        width: 240px;
      }
    }

    @media (max-width: 480px) {
      .spt-header-content {
        padding: 0 var(--spt-space-3);
      }

      .spt-content-wrapper {
        padding: var(--spt-space-3);
      }

      .spt-brand-text {
        display: none;
      }

      .spt-sidebar {
        width: 220px;
      }
    }
  `]
})
export class DocumentationLayoutComponent { }
