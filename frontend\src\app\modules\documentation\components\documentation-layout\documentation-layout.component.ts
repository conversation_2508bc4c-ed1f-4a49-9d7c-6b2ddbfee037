import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DocumentationNavComponent } from '../documentation-nav/documentation-nav.component';

@Component({
  selector: 'app-documentation-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatSidenavModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    DocumentationNavComponent
  ],
  template: `
    <div class="documentation-container">
      <mat-toolbar color="primary" class="doc-toolbar">
        <mat-icon>description</mat-icon>
        <span class="toolbar-title">SPT Documentation</span>
        <span class="spacer"></span>
        <button mat-icon-button routerLink="/" matTooltip="Back to App">
          <mat-icon>home</mat-icon>
        </button>
      </mat-toolbar>

      <mat-sidenav-container class="doc-sidenav-container">
        <mat-sidenav mode="side" opened="true" class="doc-sidenav">
          <app-documentation-nav></app-documentation-nav>
        </mat-sidenav>
        
        <mat-sidenav-content class="doc-content">
          <div class="content-wrapper">
            <router-outlet></router-outlet>
          </div>
        </mat-sidenav-content>
      </mat-sidenav-container>
    </div>
  `,
  styles: [`
    .documentation-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .doc-toolbar {
      z-index: 2;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .toolbar-title {
      margin-left: 8px;
      font-size: 1.2em;
      font-weight: 500;
    }

    .spacer {
      flex: 1 1 auto;
    }

    .doc-sidenav-container {
      flex: 1;
    }

    .doc-sidenav {
      width: 280px;
      border-right: 1px solid #e0e0e0;
      background: #fafafa;
    }

    .doc-content {
      background: #ffffff;
    }

    .content-wrapper {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    @media (max-width: 768px) {
      .doc-sidenav {
        width: 240px;
      }
      
      .content-wrapper {
        padding: 16px;
      }
    }
  `]
})
export class DocumentationLayoutComponent { }
