import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DocumentationNavComponent } from '../documentation-nav/documentation-nav.component';

@Component({
  selector: 'app-documentation-layout',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatSidenavModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    DocumentationNavComponent
  ],
  template: `
    <div class="documentation-container">
      <mat-toolbar class="doc-toolbar">
        <div class="toolbar-brand">
          <div class="brand-icon">
            <mat-icon>shield</mat-icon>
          </div>
          <div class="brand-text">
            <span class="brand-title">SPT Documentation</span>
            <span class="brand-subtitle">Security Protocol Tool</span>
          </div>
        </div>
        <span class="spacer"></span>
        <div class="toolbar-actions">
          <button mat-stroked-button routerLink="/dashboard" class="back-to-app-btn">
            <mat-icon>arrow_back</mat-icon>
            Back to App
          </button>
        </div>
      </mat-toolbar>

      <mat-sidenav-container class="doc-sidenav-container">
        <mat-sidenav mode="side" opened="true" class="doc-sidenav">
          <app-documentation-nav></app-documentation-nav>
        </mat-sidenav>

        <mat-sidenav-content class="doc-content">
          <div class="content-wrapper">
            <router-outlet></router-outlet>
          </div>
        </mat-sidenav-content>
      </mat-sidenav-container>
    </div>
  `,
  styles: [`
    .documentation-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
      background: #fafbfc;
    }

    .doc-toolbar {
      background: #ffffff;
      color: #1a202c;
      z-index: 2;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
      padding: 0 32px;
      min-height: 64px;
      border-bottom: 1px solid #e2e8f0;
    }

    .toolbar-brand {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .brand-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      padding: 10px;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .brand-icon mat-icon {
      color: #ffffff;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .brand-text {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .brand-title {
      font-size: 1.5em;
      font-weight: 700;
      letter-spacing: -0.5px;
      color: #1a202c;
    }

    .brand-subtitle {
      font-size: 0.875em;
      color: #64748b;
      font-weight: 500;
    }

    .spacer {
      flex: 1 1 auto;
    }

    .toolbar-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .back-to-app-btn {
      background: #667eea;
      border: 1px solid #667eea;
      color: white;
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 600;
      font-size: 0.875em;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
    }

    .back-to-app-btn:hover {
      background: #5a67d8;
      border-color: #5a67d8;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .back-to-app-btn mat-icon {
      margin-right: 8px;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .doc-sidenav-container {
      flex: 1;
    }

    .doc-sidenav {
      width: 320px;
      border-right: 1px solid #e2e8f0;
      background: #ffffff;
      box-shadow: none;
    }

    .doc-content {
      background: transparent;
    }

    .content-wrapper {
      padding: 32px;
      max-width: 1200px;
      margin: 0 auto;
      min-height: calc(100vh - 72px);
    }

    @media (max-width: 1024px) {
      .doc-sidenav {
        width: 280px;
      }

      .content-wrapper {
        padding: 24px;
      }
    }

    @media (max-width: 768px) {
      .doc-toolbar {
        padding: 0 16px;
        min-height: 64px;
      }

      .brand-title {
        font-size: 1.2em;
      }

      .brand-subtitle {
        font-size: 0.8em;
      }

      .doc-sidenav {
        width: 260px;
      }

      .content-wrapper {
        padding: 16px;
      }

      .back-to-app-btn {
        padding: 6px 16px;
        font-size: 0.9em;
      }

      .back-to-app-btn mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }

    @media (max-width: 480px) {
      .toolbar-brand {
        gap: 12px;
      }

      .brand-icon {
        padding: 6px;
      }

      .brand-icon mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      .back-to-app-btn span {
        display: none;
      }

      .back-to-app-btn {
        min-width: 40px;
        padding: 8px;
        border-radius: 50%;
      }

      .back-to-app-btn mat-icon {
        margin: 0;
      }
    }
  `]
})
export class DocumentationLayoutComponent { }
