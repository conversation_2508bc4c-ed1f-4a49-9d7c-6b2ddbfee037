import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatExpansionModule } from '@angular/material/expansion';

interface SystemComponent {
  name: string;
  description: string;
  technology: string;
  responsibilities: string[];
  apis?: string[];
  dependencies?: string[];
}

interface DataFlow {
  from: string;
  to: string;
  description: string;
  protocol: string;
}

@Component({
  selector: 'app-architecture',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatCardModule,
    MatIconModule,
    MatChipsModule,
    MatExpansionModule
  ],
  template: `
    <div class="architecture-container">
      <div class="page-header">
        <h1>
          <mat-icon>account_tree</mat-icon>
          System Architecture
        </h1>
        <p class="page-subtitle">
          Comprehensive overview of SPT system architecture and components
        </p>
      </div>

      <div class="architecture-overview">
        <mat-card class="overview-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>info</mat-icon>
            <mat-card-title>Architecture Overview</mat-card-title>
            <mat-card-subtitle>Modular, scalable, and secure design</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <p>SPT follows a microservices architecture with clear separation of concerns, enabling scalability, maintainability, and extensibility.</p>
            <div class="architecture-principles">
              <div class="principle" *ngFor="let principle of architecturePrinciples">
                <mat-icon [style.color]="principle.color">{{ principle.icon }}</mat-icon>
                <div>
                  <strong>{{ principle.title }}</strong>
                  <p>{{ principle.description }}</p>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <mat-tab-group class="architecture-tabs" animationDuration="300ms">
        <!-- System Overview Tab -->
        <mat-tab label="System Overview">
          <div class="tab-content">
            <h2>High-Level Architecture</h2>
            <p>SPT consists of multiple interconnected components working together to provide comprehensive security analysis.</p>
            
            <div class="architecture-diagram">
              <mat-card class="diagram-card">
                <mat-card-header>
                  <mat-icon mat-card-avatar>architecture</mat-icon>
                  <mat-card-title>System Components</mat-card-title>
                  <mat-card-subtitle>Main architectural components and their relationships</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="diagram-content">
                    <div class="component-layer frontend-layer">
                      <h4>Frontend Layer</h4>
                      <div class="components">
                        <div class="component frontend">
                          <mat-icon>web</mat-icon>
                          <span>Angular Dashboard</span>
                        </div>
                        <div class="component vscode">
                          <mat-icon>extension</mat-icon>
                          <span>VS Code Extension</span>
                        </div>
                        <div class="component cli">
                          <mat-icon>terminal</mat-icon>
                          <span>CLI Tool</span>
                        </div>
                      </div>
                    </div>
                    
                    <div class="connection-layer">
                      <div class="connection">
                        <mat-icon>swap_vert</mat-icon>
                        <span>HTTP/WebSocket</span>
                      </div>
                    </div>
                    
                    <div class="component-layer backend-layer">
                      <h4>Backend Layer</h4>
                      <div class="components">
                        <div class="component api">
                          <mat-icon>api</mat-icon>
                          <span>REST API</span>
                        </div>
                        <div class="component scanner">
                          <mat-icon>security</mat-icon>
                          <span>Scanner Engine</span>
                        </div>
                        <div class="component reports">
                          <mat-icon>assessment</mat-icon>
                          <span>Report Generator</span>
                        </div>
                      </div>
                    </div>
                    
                    <div class="connection-layer">
                      <div class="connection">
                        <mat-icon>swap_vert</mat-icon>
                        <span>Database Queries</span>
                      </div>
                    </div>
                    
                    <div class="component-layer data-layer">
                      <h4>Data Layer</h4>
                      <div class="components">
                        <div class="component database">
                          <mat-icon>storage</mat-icon>
                          <span>SQLite/PostgreSQL</span>
                        </div>
                        <div class="component cache">
                          <mat-icon>memory</mat-icon>
                          <span>In-Memory Cache</span>
                        </div>
                        <div class="component files">
                          <mat-icon>folder</mat-icon>
                          <span>File System</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Components Tab -->
        <mat-tab label="Components">
          <div class="tab-content">
            <h2>System Components</h2>
            <p>Detailed breakdown of each component in the SPT architecture.</p>
            
            <div class="components-grid">
              <mat-card class="component-card" *ngFor="let component of systemComponents">
                <mat-card-header>
                  <mat-icon mat-card-avatar [style.background-color]="getComponentColor(component.technology)">
                    {{ getComponentIcon(component.technology) }}
                  </mat-icon>
                  <mat-card-title>{{ component.name }}</mat-card-title>
                  <mat-card-subtitle>{{ component.technology }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <p>{{ component.description }}</p>
                  
                  <div class="responsibilities" *ngIf="component.responsibilities.length > 0">
                    <h4>Responsibilities:</h4>
                    <ul>
                      <li *ngFor="let responsibility of component.responsibilities">{{ responsibility }}</li>
                    </ul>
                  </div>
                  
                  <div class="apis" *ngIf="component.apis && component.apis.length > 0">
                    <h4>APIs:</h4>
                    <mat-chip-listbox class="api-chips">
                      <mat-chip *ngFor="let api of component.apis">{{ api }}</mat-chip>
                    </mat-chip-listbox>
                  </div>
                  
                  <div class="dependencies" *ngIf="component.dependencies && component.dependencies.length > 0">
                    <h4>Dependencies:</h4>
                    <mat-chip-listbox class="dependency-chips">
                      <mat-chip *ngFor="let dep of component.dependencies">{{ dep }}</mat-chip>
                    </mat-chip-listbox>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Data Flow Tab -->
        <mat-tab label="Data Flow">
          <div class="tab-content">
            <h2>Data Flow Patterns</h2>
            <p>How data flows through the SPT system during different operations.</p>
            
            <div class="data-flow-sections">
              <mat-card class="flow-card">
                <mat-card-header>
                  <mat-icon mat-card-avatar>timeline</mat-icon>
                  <mat-card-title>Security Scan Flow</mat-card-title>
                  <mat-card-subtitle>End-to-end scan process</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="flow-diagram">
                    <div class="flow-step" *ngFor="let step of scanFlowSteps; let i = index">
                      <div class="step-number">{{ i + 1 }}</div>
                      <div class="step-content">
                        <h4>{{ step.title }}</h4>
                        <p>{{ step.description }}</p>
                        <div class="step-details" *ngIf="step.details">
                          <mat-chip-listbox>
                            <mat-chip *ngFor="let detail of step.details">{{ detail }}</mat-chip>
                          </mat-chip-listbox>
                        </div>
                      </div>
                      <div class="flow-arrow" *ngIf="i < scanFlowSteps.length - 1">
                        <mat-icon>arrow_downward</mat-icon>
                      </div>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>

              <mat-card class="flow-card">
                <mat-card-header>
                  <mat-icon mat-card-avatar>swap_horiz</mat-icon>
                  <mat-card-title>Real-time Updates</mat-card-title>
                  <mat-card-subtitle>WebSocket communication flow</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="realtime-flow">
                    <div class="flow-item" *ngFor="let flow of realtimeFlows">
                      <div class="flow-source">
                        <mat-icon>{{ getFlowIcon(flow.from) }}</mat-icon>
                        <span>{{ flow.from }}</span>
                      </div>
                      <div class="flow-connection">
                        <mat-icon>arrow_forward</mat-icon>
                        <span class="protocol">{{ flow.protocol }}</span>
                      </div>
                      <div class="flow-target">
                        <mat-icon>{{ getFlowIcon(flow.to) }}</mat-icon>
                        <span>{{ flow.to }}</span>
                      </div>
                      <div class="flow-description">
                        <p>{{ flow.description }}</p>
                      </div>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Security Tab -->
        <mat-tab label="Security">
          <div class="tab-content">
            <h2>Security Architecture</h2>
            <p>Security measures and patterns implemented throughout the SPT system.</p>
            
            <div class="security-sections">
              <mat-card class="security-card" *ngFor="let section of securitySections">
                <mat-card-header>
                  <mat-icon mat-card-avatar [style.background-color]="section.color">
                    {{ section.icon }}
                  </mat-icon>
                  <mat-card-title>{{ section.title }}</mat-card-title>
                  <mat-card-subtitle>{{ section.description }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="security-measures">
                    <div class="measure" *ngFor="let measure of section.measures">
                      <mat-icon>check_circle</mat-icon>
                      <span>{{ measure }}</span>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Deployment Tab -->
        <mat-tab label="Deployment">
          <div class="tab-content">
            <h2>Deployment Architecture</h2>
            <p>Deployment patterns and infrastructure considerations for SPT.</p>
            
            <div class="deployment-options">
              <mat-card class="deployment-card" *ngFor="let option of deploymentOptions">
                <mat-card-header>
                  <mat-icon mat-card-avatar [style.background-color]="option.color">
                    {{ option.icon }}
                  </mat-icon>
                  <mat-card-title>{{ option.title }}</mat-card-title>
                  <mat-card-subtitle>{{ option.description }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="deployment-details">
                    <div class="pros-cons">
                      <div class="pros">
                        <h4>Advantages:</h4>
                        <ul>
                          <li *ngFor="let pro of option.pros">{{ pro }}</li>
                        </ul>
                      </div>
                      <div class="cons">
                        <h4>Considerations:</h4>
                        <ul>
                          <li *ngFor="let con of option.cons">{{ con }}</li>
                        </ul>
                      </div>
                    </div>
                    <div class="use-cases" *ngIf="option.useCases">
                      <h4>Best for:</h4>
                      <mat-chip-listbox>
                        <mat-chip *ngFor="let useCase of option.useCases">{{ useCase }}</mat-chip>
                      </mat-chip-listbox>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styles: [`
    .architecture-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      text-align: center;
      margin-bottom: 32px;
    }

    .page-header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      color: #1976d2;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      color: #666;
      font-size: 1.1em;
      margin: 0;
    }

    .architecture-overview {
      margin-bottom: 32px;
    }

    .overview-card {
      border: 1px solid #e0e0e0;
    }

    .architecture-principles {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }

    .principle {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .principle mat-icon {
      margin-top: 2px;
    }

    .principle strong {
      display: block;
      margin-bottom: 4px;
    }

    .principle p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    .architecture-tabs {
      margin-bottom: 32px;
    }

    .tab-content {
      padding: 24px 0;
    }

    .tab-content h2 {
      color: #1976d2;
      margin-bottom: 8px;
    }

    .architecture-diagram {
      margin-top: 24px;
    }

    .diagram-card {
      border: 1px solid #e0e0e0;
    }

    .diagram-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 16px;
    }

    .component-layer {
      padding: 16px;
      border-radius: 8px;
      text-align: center;
    }

    .frontend-layer {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    }

    .backend-layer {
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    }

    .data-layer {
      background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    }

    .component-layer h4 {
      margin: 0 0 16px 0;
      color: #1976d2;
    }

    .components {
      display: flex;
      justify-content: center;
      gap: 16px;
      flex-wrap: wrap;
    }

    .component {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      min-width: 120px;
    }

    .component mat-icon {
      color: #1976d2;
    }

    .component span {
      font-size: 0.9em;
      font-weight: 500;
    }

    .connection-layer {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .connection {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      color: #666;
    }

    .connection span {
      font-size: 0.8em;
    }

    .components-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .component-card {
      height: 100%;
    }

    .responsibilities,
    .apis,
    .dependencies {
      margin-top: 16px;
    }

    .responsibilities h4,
    .apis h4,
    .dependencies h4 {
      margin: 0 0 8px 0;
      color: #1976d2;
      font-size: 0.9em;
    }

    .responsibilities ul {
      margin: 0;
      padding-left: 20px;
    }

    .responsibilities li {
      margin-bottom: 4px;
      color: #666;
      font-size: 0.9em;
    }

    .api-chips,
    .dependency-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }

    .data-flow-sections {
      margin-top: 24px;
    }

    .flow-card {
      margin-bottom: 24px;
    }

    .flow-diagram {
      padding: 16px;
    }

    .flow-step {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      margin-bottom: 24px;
    }

    .step-number {
      background: #1976d2;
      color: white;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      flex-shrink: 0;
    }

    .step-content {
      flex: 1;
    }

    .step-content h4 {
      margin: 0 0 8px 0;
      color: #1976d2;
    }

    .step-content p {
      margin: 0 0 8px 0;
      color: #666;
    }

    .step-details {
      margin-top: 8px;
    }

    .flow-arrow {
      text-align: center;
      color: #1976d2;
      margin: 8px 0;
    }

    .realtime-flow {
      padding: 16px;
    }

    .flow-item {
      display: grid;
      grid-template-columns: 1fr auto 1fr;
      grid-template-rows: auto auto;
      gap: 16px;
      margin-bottom: 24px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 8px;
    }

    .flow-source,
    .flow-target {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }

    .flow-connection {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      color: #1976d2;
    }

    .protocol {
      font-size: 0.8em;
      color: #666;
    }

    .flow-description {
      grid-column: 1 / -1;
      color: #666;
      font-size: 0.9em;
    }

    .flow-description p {
      margin: 0;
    }

    .security-sections {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .security-card {
      height: fit-content;
    }

    .security-measures {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .measure {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
    }

    .measure mat-icon {
      color: #4caf50;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .deployment-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .deployment-card {
      height: fit-content;
    }

    .deployment-details {
      margin-top: 16px;
    }

    .pros-cons {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }

    .pros h4,
    .cons h4 {
      margin: 0 0 8px 0;
      color: #1976d2;
      font-size: 0.9em;
    }

    .pros ul,
    .cons ul {
      margin: 0;
      padding-left: 20px;
    }

    .pros li,
    .cons li {
      margin-bottom: 4px;
      color: #666;
      font-size: 0.9em;
    }

    .use-cases h4 {
      margin: 0 0 8px 0;
      color: #1976d2;
      font-size: 0.9em;
    }

    @media (max-width: 768px) {
      .architecture-principles {
        grid-template-columns: 1fr;
      }
      
      .components {
        flex-direction: column;
        align-items: center;
      }
      
      .components-grid {
        grid-template-columns: 1fr;
      }
      
      .flow-item {
        grid-template-columns: 1fr;
        text-align: center;
      }
      
      .pros-cons {
        grid-template-columns: 1fr;
      }
      
      .security-sections {
        grid-template-columns: 1fr;
      }
      
      .deployment-options {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ArchitectureComponent {
  architecturePrinciples = [
    {
      title: 'Modularity',
      description: 'Clear separation of concerns with independent, reusable components',
      icon: 'view_module',
      color: '#1976d2'
    },
    {
      title: 'Scalability',
      description: 'Horizontal and vertical scaling capabilities for growing workloads',
      icon: 'trending_up',
      color: '#4caf50'
    },
    {
      title: 'Security',
      description: 'Security-first design with multiple layers of protection',
      icon: 'security',
      color: '#f44336'
    },
    {
      title: 'Extensibility',
      description: 'Plugin architecture for adding new blockchain support and features',
      icon: 'extension',
      color: '#ff9800'
    }
  ];

  systemComponents: SystemComponent[] = [
    {
      name: 'Angular Dashboard',
      description: 'Modern web interface for security scanning, project management, and report generation',
      technology: 'Angular',
      responsibilities: [
        'User interface and experience',
        'Real-time scan monitoring',
        'Report visualization',
        'Project management',
        'User authentication'
      ],
      apis: ['REST API', 'WebSocket'],
      dependencies: ['Angular Material', 'RxJS', 'Chart.js']
    },
    {
      name: 'REST API Server',
      description: 'RESTful API providing all backend functionality and data access',
      technology: 'Go',
      responsibilities: [
        'HTTP request handling',
        'Authentication and authorization',
        'Data validation',
        'Business logic coordination',
        'Response formatting'
      ],
      apis: ['HTTP REST', 'WebSocket'],
      dependencies: ['Gin Framework', 'JWT', 'CORS']
    },
    {
      name: 'Scanner Engine',
      description: 'Core security analysis engine for blockchain applications',
      technology: 'Go',
      responsibilities: [
        'File parsing and analysis',
        'Security rule execution',
        'Vulnerability detection',
        'Performance optimization',
        'Result aggregation'
      ],
      dependencies: ['AST Parsers', 'Security Rules', 'Pattern Matching']
    },
    {
      name: 'VS Code Extension',
      description: 'IDE integration providing real-time security feedback',
      technology: 'TypeScript',
      responsibilities: [
        'Real-time code analysis',
        'Inline security decorations',
        'Problems panel integration',
        'CodeLens functionality',
        'Settings management'
      ],
      apis: ['VS Code API', 'SPT REST API'],
      dependencies: ['VS Code SDK', 'WebSocket Client']
    },
    {
      name: 'CLI Tool',
      description: 'Command-line interface for automated security scanning',
      technology: 'Go',
      responsibilities: [
        'Command parsing',
        'File system operations',
        'Batch processing',
        'Output formatting',
        'CI/CD integration'
      ],
      dependencies: ['Cobra CLI', 'File Watchers', 'Output Formatters']
    },
    {
      name: 'Database Layer',
      description: 'Persistent storage for scan results, configurations, and user data',
      technology: 'SQLite/PostgreSQL',
      responsibilities: [
        'Data persistence',
        'Query optimization',
        'Transaction management',
        'Data integrity',
        'Backup and recovery'
      ],
      dependencies: ['GORM', 'Database Drivers', 'Migration Tools']
    }
  ];

  scanFlowSteps = [
    {
      title: 'Scan Request',
      description: 'User initiates scan through web dashboard, CLI, or VS Code extension',
      details: ['File path validation', 'Permission checks', 'Queue management']
    },
    {
      title: 'File Discovery',
      description: 'System discovers and categorizes files for analysis',
      details: ['Recursive directory traversal', 'File type detection', 'Exclusion filtering']
    },
    {
      title: 'Security Analysis',
      description: 'Scanner engine analyzes files using blockchain-specific rules',
      details: ['AST parsing', 'Pattern matching', 'Vulnerability detection', 'Performance analysis']
    },
    {
      title: 'Result Processing',
      description: 'Analysis results are processed and stored',
      details: ['Result aggregation', 'Severity classification', 'Database storage', 'Cache updates']
    },
    {
      title: 'Notification',
      description: 'Results are delivered to requesting clients',
      details: ['WebSocket notifications', 'REST API responses', 'Real-time updates']
    }
  ];

  realtimeFlows: DataFlow[] = [
    {
      from: 'Scanner Engine',
      to: 'WebSocket Manager',
      description: 'Scan progress and results are pushed to WebSocket manager',
      protocol: 'Internal API'
    },
    {
      from: 'WebSocket Manager',
      to: 'Angular Dashboard',
      description: 'Real-time scan updates are broadcast to web clients',
      protocol: 'WebSocket'
    },
    {
      from: 'WebSocket Manager',
      to: 'VS Code Extension',
      description: 'Live security feedback is sent to IDE clients',
      protocol: 'WebSocket'
    }
  ];

  securitySections = [
    {
      title: 'Authentication & Authorization',
      description: 'User identity and access control',
      icon: 'person',
      color: '#1976d2',
      measures: [
        'JWT-based authentication',
        'Role-based access control',
        'Session management',
        'API key authentication',
        'Secure password policies'
      ]
    },
    {
      title: 'Data Protection',
      description: 'Data security and privacy measures',
      icon: 'shield',
      color: '#4caf50',
      measures: [
        'Data encryption at rest',
        'TLS/HTTPS for data in transit',
        'Input validation and sanitization',
        'SQL injection prevention',
        'XSS protection'
      ]
    },
    {
      title: 'Infrastructure Security',
      description: 'System and network security',
      icon: 'cloud_circle',
      color: '#ff9800',
      measures: [
        'CORS configuration',
        'Rate limiting',
        'Firewall rules',
        'Security headers',
        'Audit logging'
      ]
    }
  ];

  deploymentOptions = [
    {
      title: 'Local Development',
      description: 'Single-machine deployment for development and testing',
      icon: 'computer',
      color: '#1976d2',
      pros: [
        'Easy setup and configuration',
        'Fast development cycles',
        'Full control over environment',
        'No network dependencies'
      ],
      cons: [
        'Limited scalability',
        'Single point of failure',
        'Resource constraints',
        'Not suitable for production'
      ],
      useCases: ['Development', 'Testing', 'Proof of Concept']
    },
    {
      title: 'Docker Containers',
      description: 'Containerized deployment for consistency and portability',
      icon: 'inventory_2',
      color: '#4caf50',
      pros: [
        'Environment consistency',
        'Easy deployment',
        'Resource isolation',
        'Scalable architecture'
      ],
      cons: [
        'Container orchestration complexity',
        'Resource overhead',
        'Learning curve',
        'Storage persistence challenges'
      ],
      useCases: ['Production', 'CI/CD', 'Multi-environment']
    },
    {
      title: 'Cloud Deployment',
      description: 'Cloud-native deployment with managed services',
      icon: 'cloud',
      color: '#ff9800',
      pros: [
        'High availability',
        'Auto-scaling',
        'Managed infrastructure',
        'Global distribution'
      ],
      cons: [
        'Vendor lock-in',
        'Cost considerations',
        'Network latency',
        'Compliance requirements'
      ],
      useCases: ['Enterprise', 'High Availability', 'Global Teams']
    }
  ];

  getComponentColor(technology: string): string {
    const colors: { [key: string]: string } = {
      'Angular': '#dd0031',
      'Go': '#00add8',
      'TypeScript': '#3178c6',
      'SQLite/PostgreSQL': '#336791'
    };
    return colors[technology] || '#1976d2';
  }

  getComponentIcon(technology: string): string {
    const icons: { [key: string]: string } = {
      'Angular': 'web',
      'Go': 'code',
      'TypeScript': 'extension',
      'SQLite/PostgreSQL': 'storage'
    };
    return icons[technology] || 'code';
  }

  getFlowIcon(component: string): string {
    const icons: { [key: string]: string } = {
      'Scanner Engine': 'security',
      'WebSocket Manager': 'swap_horiz',
      'Angular Dashboard': 'web',
      'VS Code Extension': 'extension'
    };
    return icons[component] || 'device_unknown';
  }
}
