package scanner

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/bitcoin"
	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/database"
	"blockchain-spt/backend/pkg/dependencies"
	"blockchain-spt/backend/pkg/ethereum"
	"blockchain-spt/backend/pkg/models"
	"blockchain-spt/backend/pkg/storage"

	"github.com/sirupsen/logrus"
)

// StorageInterface defines the storage operations
type StorageInterface interface {
	CreateScanResult(result *models.ScanResult) error
	GetScanResult(id string) (*models.ScanResult, error)
	UpdateScanResult(result *models.ScanResult) error
	GetScanHistory(limit int) ([]*models.ScanResult, error)
	Health() error
	Close() error
}

// DatabaseStorageAdapter adapts database.Database to StorageInterface
type DatabaseStorageAdapter struct {
	db *database.Database
}

func (d *DatabaseStorageAdapter) CreateScanResult(result *models.ScanResult) error {
	return d.db.CreateScanResult(result)
}

func (d *DatabaseStorageAdapter) GetScanResult(id string) (*models.ScanResult, error) {
	return d.db.GetScanResult(id)
}

func (d *DatabaseStorageAdapter) UpdateScanResult(result *models.ScanResult) error {
	return d.db.UpdateScanResult(result)
}

func (d *DatabaseStorageAdapter) GetScanHistory(limit int) ([]*models.ScanResult, error) {
	return d.db.GetScanHistory(limit)
}

func (d *DatabaseStorageAdapter) Health() error {
	return d.db.Health()
}

func (d *DatabaseStorageAdapter) Close() error {
	return d.db.Close()
}

// Engine represents the main scanning engine
type Engine struct {
	config            *config.Config
	storage           StorageInterface
	ethereumScanner   *ethereum.Scanner
	bitcoinScanner    *bitcoin.Scanner
	dependencyScanner *dependencies.Scanner
	logger            *logrus.Logger
}

// NewEngine creates a new scanner engine
func NewEngine(cfg *config.Config) (*Engine, error) {
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})

	// Try to initialize database first, fallback to memory storage
	var storageImpl StorageInterface

	db, err := database.New(cfg)
	if err != nil || !db.IsConnected() {
		logger.WithError(err).Warn("Database not available, using memory storage")
		storageImpl = storage.NewMemoryStorage()
	} else {
		logger.Info("Database connected, using database storage")
		storageImpl = &DatabaseStorageAdapter{db: db}
	}

	// Initialize individual scanners
	ethereumScanner, err := ethereum.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create ethereum scanner: %w", err)
	}

	bitcoinScanner, err := bitcoin.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create bitcoin scanner: %w", err)
	}

	dependencyScanner, err := dependencies.NewScanner(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create dependency scanner: %w", err)
	}

	return &Engine{
		config:            cfg,
		storage:           storageImpl,
		ethereumScanner:   ethereumScanner,
		bitcoinScanner:    bitcoinScanner,
		dependencyScanner: dependencyScanner,
		logger:            logger,
	}, nil
}

// StartScan starts a new security scan
func (e *Engine) StartScan(request *models.ScanRequest) (*models.ScanResponse, error) {
	scanID := generateScanID()

	e.logger.WithFields(logrus.Fields{
		"scan_id":      scanID,
		"project_path": request.ProjectPath,
		"chains":       request.Chains,
		"scan_type":    request.ScanType,
	}).Info("Starting security scan")

	// Create initial scan result in database
	now := time.Now()
	result := &models.ScanResult{
		ID:          scanID,
		ProjectPath: request.ProjectPath,
		Chains:      request.Chains,
		Status:      models.ScanStatusRunning,
		StartTime:   &now,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// Store initial result
	if err := e.storage.CreateScanResult(result); err != nil {
		e.logger.WithError(err).Error("Failed to create scan result in storage")
		return nil, fmt.Errorf("failed to create scan result: %w", err)
	}

	// Start scan in background
	go func() {
		// Perform the actual scan
		issues, err := e.performScan(request)
		if err != nil {
			result.Status = models.ScanStatusFailed
			result.Error = err.Error()
			e.logger.WithError(err).WithField("scan_id", scanID).Error("Scan failed")
		} else {
			result.Status = models.ScanStatusCompleted
			result.Issues = issues
			result.SeverityCounts = e.calculateSeverityCounts(issues)
			result.FilesScanned = len(issues)      // Placeholder
			result.LinesScanned = len(issues) * 50 // Placeholder
		}

		endTime := time.Now()
		result.EndTime = &endTime
		if result.StartTime != nil {
			duration := endTime.Sub(*result.StartTime)
			result.Duration = &duration
		}
		result.UpdatedAt = endTime

		// Update result in storage
		if err := e.storage.UpdateScanResult(result); err != nil {
			e.logger.WithError(err).Error("Failed to update scan result in storage")
		}

		e.logger.WithFields(logrus.Fields{
			"scan_id":  scanID,
			"status":   result.Status,
			"issues":   len(result.Issues),
			"duration": result.Duration,
		}).Info("Scan completed")
	}()

	return &models.ScanResponse{
		ScanID:  scanID,
		Status:  models.ScanStatusRunning,
		Message: "Scan started successfully",
	}, nil
}

// GetScanResult retrieves scan results by ID
func (e *Engine) GetScanResult(scanID string) (*models.ScanResult, error) {
	return e.storage.GetScanResult(scanID)
}

// GetScanHistory retrieves scan history
func (e *Engine) GetScanHistory() ([]*models.ScanResult, error) {
	return e.storage.GetScanHistory(50) // Limit to 50 most recent scans
}

// ScanFile scans a specific file
func (e *Engine) ScanFile(filePath string, chains []string) (*models.ScanResult, error) {
	scanID := generateScanID()
	ctx := context.Background()

	e.logger.WithFields(logrus.Fields{
		"scan_id":   scanID,
		"file_path": filePath,
		"chains":    chains,
	}).Info("Starting file scan")

	startTime := time.Now()
	result := &models.ScanResult{
		ID:          scanID,
		ProjectPath: filePath,
		Chains:      chains,
		Status:      models.ScanStatusRunning,
		StartTime:   &startTime,
	}

	// Determine file type and scan accordingly
	ext := strings.ToLower(filepath.Ext(filePath))
	var issues []models.SecurityIssue
	var err error

	switch ext {
	case ".sol":
		if contains(chains, "ethereum") {
			issues, err = e.ethereumScanner.ScanFile(ctx, filePath)
		}
	case ".js", ".ts":
		// Could be Bitcoin scripts or general JavaScript
		if contains(chains, "bitcoin") {
			issues, err = e.bitcoinScanner.ScanFile(ctx, filePath)
		}
	case ".json":
		// Could be package.json or other config files
		depIssues, depErr := e.dependencyScanner.ScanFile(ctx, filePath)
		if depErr == nil {
			issues = append(issues, depIssues...)
		}
	default:
		// Try all scanners for unknown file types
		for _, chain := range chains {
			switch chain {
			case "ethereum":
				ethIssues, _ := e.ethereumScanner.ScanFile(ctx, filePath)
				issues = append(issues, ethIssues...)
			case "bitcoin":
				btcIssues, _ := e.bitcoinScanner.ScanFile(ctx, filePath)
				issues = append(issues, btcIssues...)
			}
		}
		depIssues, _ := e.dependencyScanner.ScanFile(ctx, filePath)
		issues = append(issues, depIssues...)
	}

	if err != nil {
		result.Status = models.ScanStatusFailed
		result.Error = err.Error()
	} else {
		result.Status = models.ScanStatusCompleted
		result.Issues = issues
		result.SeverityCounts = e.calculateSeverityCounts(issues)
	}

	endTime := time.Now()
	result.EndTime = &endTime
	if result.StartTime != nil {
		duration := endTime.Sub(*result.StartTime)
		result.Duration = &duration
	}
	result.FilesScanned = 1
	result.LinesScanned = 100 // Placeholder

	return result, err
}

// performScan performs the actual scanning logic
func (e *Engine) performScan(request *models.ScanRequest) ([]models.SecurityIssue, error) {
	var allIssues []models.SecurityIssue
	ctx := context.Background()

	// Scan based on requested chains
	for _, chain := range request.Chains {
		switch strings.ToLower(chain) {
		case "ethereum":
			issues, err := e.ethereumScanner.ScanProject(ctx, request.ProjectPath)
			if err != nil {
				e.logger.WithError(err).Warn("Ethereum scan failed")
			} else {
				allIssues = append(allIssues, issues...)
			}
		case "bitcoin":
			issues, err := e.bitcoinScanner.ScanProject(ctx, request.ProjectPath)
			if err != nil {
				e.logger.WithError(err).Warn("Bitcoin scan failed")
			} else {
				allIssues = append(allIssues, issues...)
			}
		}
	}

	// Always scan for dependency issues
	depIssues, err := e.dependencyScanner.ScanProject(ctx, request.ProjectPath)
	if err != nil {
		e.logger.WithError(err).Warn("Dependency scan failed")
	} else {
		allIssues = append(allIssues, depIssues...)
	}

	return allIssues, nil
}

// calculateSeverityCounts calculates severity distribution
func (e *Engine) calculateSeverityCounts(issues []models.SecurityIssue) map[string]int {
	counts := map[string]int{
		"critical": 0,
		"high":     0,
		"medium":   0,
		"low":      0,
		"info":     0,
	}

	for _, issue := range issues {
		if count, exists := counts[strings.ToLower(issue.Severity)]; exists {
			counts[strings.ToLower(issue.Severity)] = count + 1
		}
	}

	return counts
}

// Helper functions
func generateScanID() string {
	return fmt.Sprintf("scan-%d", time.Now().UnixNano())
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if strings.EqualFold(s, item) {
			return true
		}
	}
	return false
}
