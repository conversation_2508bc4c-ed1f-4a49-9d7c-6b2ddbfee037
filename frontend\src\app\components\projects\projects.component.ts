import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router } from '@angular/router';
import { ProjectDialogComponent, ProjectDialogData } from './project-dialog/project-dialog.component';
import { ConfirmDialogComponent, ConfirmDialogData } from '../../shared/confirm-dialog/confirm-dialog.component';
import { Project } from '../../models/project.model';

@Component({
  selector: 'app-projects',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatChipsModule,
    MatSnackBarModule,
    MatProgressSpinnerModule
  ],
  template: `
    <div class="projects-container">
      <div class="header">
        <h1>🗂️ Project Management</h1>
        <button mat-raised-button color="primary" (click)="openCreateDialog()">
          <mat-icon>add</mat-icon>
          New Project
        </button>
      </div>

      <!-- Project Stats -->
      <div class="stats-grid">
        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-number">{{ projects.length }}</div>
            <div class="stat-label">Total Projects</div>
          </mat-card-content>
        </mat-card>
        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-number">{{ getActiveProjects() }}</div>
            <div class="stat-label">Active Projects</div>
          </mat-card-content>
        </mat-card>
        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-number">{{ getTotalScans() }}</div>
            <div class="stat-label">Total Scans</div>
          </mat-card-content>
        </mat-card>
        <mat-card class="stat-card">
          <mat-card-content>
            <div class="stat-number">{{ getTotalIssues() }}</div>
            <div class="stat-label">Total Issues</div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Projects Table -->
      <mat-card class="projects-table-card">
        <mat-card-header>
          <mat-card-title>Projects</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="projects" class="projects-table">
              <ng-container matColumnDef="name">
                <th mat-header-cell *matHeaderCellDef>Name</th>
                <td mat-cell *matCellDef="let project">
                  <div class="project-name">
                    <strong>{{ project.name }}</strong>
                    <small>{{ project.description }}</small>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="type">
                <th mat-header-cell *matHeaderCellDef>Type</th>
                <td mat-cell *matCellDef="let project">
                  <mat-chip [class]="'type-' + project.blockchain_type">
                    {{ project.blockchain_type | titlecase }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let project">
                  <mat-chip [class]="'status-' + project.status">
                    {{ project.status | titlecase }}
                  </mat-chip>
                </td>
              </ng-container>

              <ng-container matColumnDef="scans">
                <th mat-header-cell *matHeaderCellDef>Scans</th>
                <td mat-cell *matCellDef="let project">{{ project.scan_count }}</td>
              </ng-container>

              <ng-container matColumnDef="issues">
                <th mat-header-cell *matHeaderCellDef>Issues</th>
                <td mat-cell *matCellDef="let project">
                  <span [class]="getIssueClass(project.issue_count)">
                    {{ project.issue_count }}
                  </span>
                </td>
              </ng-container>

              <ng-container matColumnDef="lastScan">
                <th mat-header-cell *matHeaderCellDef>Last Scan</th>
                <td mat-cell *matCellDef="let project">
                  {{ project.last_scan ? (project.last_scan | date:'short') : 'Never' }}
                </td>
              </ng-container>

              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let project">
                  <button mat-icon-button (click)="scanProject(project)" 
                          matTooltip="Start Scan">
                    <mat-icon>security</mat-icon>
                  </button>
                  <button mat-icon-button (click)="viewProject(project)" 
                          matTooltip="View Details">
                    <mat-icon>visibility</mat-icon>
                  </button>
                  <button mat-icon-button (click)="editProject(project)" 
                          matTooltip="Edit Project">
                    <mat-icon>edit</mat-icon>
                  </button>
                  <button mat-icon-button (click)="deleteProject(project)" 
                          matTooltip="Delete Project" 
                          color="warn">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .projects-container {
      padding: 20px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
    }

    .header h1 {
      margin: 0;
      color: #333;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      text-align: center;
      padding: 20px;
    }

    .stat-number {
      font-size: 36px;
      font-weight: bold;
      color: #667eea;
      margin-bottom: 8px;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .projects-table-card {
      margin-bottom: 30px;
    }

    .table-container {
      overflow-x: auto;
    }

    .projects-table {
      width: 100%;
    }

    .project-name strong {
      display: block;
      font-size: 16px;
      margin-bottom: 4px;
    }

    .project-name small {
      color: #666;
      font-size: 12px;
    }

    .type-ethereum {
      background-color: #627eea;
      color: white;
    }

    .type-bitcoin {
      background-color: #f7931a;
      color: white;
    }

    .type-multi-chain {
      background-color: #9c27b0;
      color: white;
    }

    .status-active {
      background-color: #4caf50;
      color: white;
    }

    .status-inactive {
      background-color: #ff9800;
      color: white;
    }

    .status-archived {
      background-color: #757575;
      color: white;
    }

    .issue-high {
      color: #f44336;
      font-weight: bold;
    }

    .issue-medium {
      color: #ff9800;
      font-weight: bold;
    }

    .issue-low {
      color: #4caf50;
    }

    .issue-none {
      color: #666;
    }

    mat-chip {
      font-size: 12px;
      min-height: 24px;
    }
  `]
})
export class ProjectsComponent implements OnInit {
  projects: Project[] = [];
  displayedColumns: string[] = ['name', 'type', 'status', 'scans', 'issues', 'lastScan', 'actions'];
  isLoading = false;

  constructor(
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadProjects();
  }

  loadProjects(): void {
    this.isLoading = true;
    // Mock data for development
    setTimeout(() => {
      this.projects = this.generateMockProjects();
      this.isLoading = false;
    }, 1000);
  }

  getActiveProjects(): number {
    return this.projects.filter(p => p.status === 'active').length;
  }

  getTotalScans(): number {
    return this.projects.reduce((total, p) => total + (p.scan_count || 0), 0);
  }

  getTotalIssues(): number {
    // Since issue_count is not in the Project model, we'll return 0 for now
    return 0;
  }

  getIssueClass(count: number): string {
    if (count === 0) return 'issue-none';
    if (count <= 5) return 'issue-low';
    if (count <= 15) return 'issue-medium';
    return 'issue-high';
  }

  openCreateDialog(): void {
    const dialogRef = this.dialog.open(ProjectDialogComponent, {
      width: '600px',
      data: { mode: 'create' } as ProjectDialogData
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Add the new project to the list
        this.projects.unshift(result as Project);
        this.snackBar.open('Project created successfully!', 'Close', { duration: 3000 });
      }
    });
  }

  scanProject(project: Project): void {
    this.router.navigate(['/scan'], { queryParams: { project: project.id } });
  }

  viewProject(project: Project): void {
    this.router.navigate(['/projects', project.id]);
  }

  editProject(project: Project): void {
    const dialogRef = this.dialog.open(ProjectDialogComponent, {
      width: '600px',
      data: { mode: 'edit', project: project } as ProjectDialogData
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Update the project in the list
        const index = this.projects.findIndex(p => p.id === project.id);
        if (index !== -1) {
          this.projects[index] = result as Project;
          this.snackBar.open('Project updated successfully!', 'Close', { duration: 3000 });
        }
      }
    });
  }

  deleteProject(project: Project): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '450px',
      data: {
        title: 'Delete Project',
        message: `Are you sure you want to delete "${project.name}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger'
      } as ConfirmDialogData
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Remove the project from the list
        const index = this.projects.findIndex(p => p.id === project.id);
        if (index !== -1) {
          this.projects.splice(index, 1);
          this.snackBar.open('Project deleted successfully!', 'Close', { duration: 3000 });
        }
      }
    });
  }

  private generateMockProjects(): Project[] {
    return [
      {
        id: '1',
        name: 'DeFi Protocol',
        description: 'Decentralized finance smart contracts',
        blockchain_type: 'ethereum',
        status: 'active',
        repository_url: 'https://github.com/example/defi-protocol',
        created_at: new Date('2024-01-15T10:00:00Z'),
        updated_at: new Date('2024-07-20T15:30:00Z'),
        scan_count: 15,
        last_scan_date: new Date('2024-07-28T09:15:00Z')
      },
      {
        id: '2',
        name: 'Bitcoin Wallet',
        description: 'Multi-signature Bitcoin wallet implementation',
        blockchain_type: 'bitcoin',
        status: 'active',
        repository_url: 'https://github.com/example/bitcoin-wallet',
        created_at: new Date('2024-02-01T14:00:00Z'),
        updated_at: new Date('2024-07-25T11:20:00Z'),
        scan_count: 8,
        last_scan_date: new Date('2024-07-27T16:45:00Z')
      },
      {
        id: '3',
        name: 'Cross-Chain Bridge',
        description: 'Multi-chain asset bridge protocol',
        blockchain_type: 'ethereum',
        status: 'inactive',
        repository_url: 'https://github.com/example/cross-chain-bridge',
        created_at: new Date('2024-03-10T09:30:00Z'),
        updated_at: new Date('2024-06-15T13:45:00Z'),
        scan_count: 22
      }
    ];
  }
}
