package api

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// Handler represents the API handler
type Handler struct {
	scannerEngine ScannerEngine
	storage       StorageInterface
	config        *config.Config
	logger        *logrus.Logger
	wsManager     *WebSocketManager
}

// StorageInterface defines storage operations
type StorageInterface interface {
	GetScanResult(id string) (*models.ScanResult, error)
	CreateScanResult(result *models.ScanResult) error
	UpdateScanResult(result *models.ScanResult) error
	GetScanHistory(limit int) ([]*models.ScanResult, error)
}

// ScannerEngine interface for the scanner engine
type ScannerEngine interface {
	StartScan(request *models.ScanRequest) (*models.ScanResponse, error)
	GetScanResult(scanID string) (*models.ScanResult, error)
	GetScanHistory() ([]*models.ScanResult, error)
	ScanFile(filePath string, chains []string) (*models.ScanResult, error)
}

// NewHandler creates a new API handler
func NewHandler(scannerEngine ScannerEngine, storage StorageInterface, cfg *config.Config) *Handler {
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})

	return &Handler{
		scannerEngine: scannerEngine,
		storage:       storage,
		config:        cfg,
		logger:        logger,
	}
}

// HealthCheck handles health check requests
func (h *Handler) HealthCheck(c *gin.Context) {
	health := &models.HealthCheck{
		Status:    "healthy",
		Version:   "1.0.0",
		Timestamp: time.Now(),
		Services: map[string]string{
			"api":      "running",
			"scanner":  "ready",
			"database": "connected",
		},
	}

	c.JSON(http.StatusOK, health)
}

// StartScan handles scan start requests
func (h *Handler) StartScan(c *gin.Context) {
	var request models.ScanRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.WithError(err).Error("Invalid scan request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate request
	if request.ProjectPath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "project_path is required",
		})
		return
	}

	if len(request.Chains) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "at least one chain must be specified",
		})
		return
	}

	// Start scan
	response, err := h.scannerEngine.StartScan(&request)
	if err != nil {
		h.logger.WithError(err).Error("Failed to start scan")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to start scan",
			"details": err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"scan_id":      response.ScanID,
		"project_path": request.ProjectPath,
		"chains":       request.Chains,
	}).Info("Scan started successfully")

	c.JSON(http.StatusOK, response)
}

// GetScanResult handles scan result requests
func (h *Handler) GetScanResult(c *gin.Context) {
	scanID := c.Param("id")
	if scanID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "scan_id is required",
		})
		return
	}

	result, err := h.scannerEngine.GetScanResult(scanID)
	if err != nil {
		h.logger.WithError(err).WithField("scan_id", scanID).Error("Failed to get scan result")
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Scan result not found",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetScanHistory handles scan history requests
func (h *Handler) GetScanHistory(c *gin.Context) {
	// Parse query parameters
	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 50
	}

	history, err := h.scannerEngine.GetScanHistory()
	if err != nil {
		h.logger.WithError(err).Error("Failed to get scan history")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get scan history",
			"details": err.Error(),
		})
		return
	}

	// Apply limit
	if len(history) > limit {
		history = history[:limit]
	}

	c.JSON(http.StatusOK, gin.H{
		"scans": history,
		"total": len(history),
	})
}

// ScanFile handles single file scan requests
func (h *Handler) ScanFile(c *gin.Context) {
	filePath := c.Query("file")
	if filePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "file parameter is required",
		})
		return
	}

	chainsParam := c.DefaultQuery("chains", "ethereum,bitcoin")
	chains := []string{}
	if chainsParam != "" {
		// Simple split by comma
		for _, chain := range []string{"ethereum", "bitcoin"} {
			chains = append(chains, chain)
		}
	}

	result, err := h.scannerEngine.ScanFile(filePath, chains)
	if err != nil {
		h.logger.WithError(err).WithField("file_path", filePath).Error("Failed to scan file")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to scan file",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GenerateReport handles report generation requests
func (h *Handler) GenerateReport(c *gin.Context) {
	var request models.ReportRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.WithError(err).Error("Invalid report request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Generate actual security report
	scanResult, err := h.storage.GetScanResult(request.ScanID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Scan result not found",
			"details": err.Error(),
		})
		return
	}

	content := h.generateReportContent(scanResult, request.Format)
	report := &models.SecurityReport{
		ID:           "report-" + request.ScanID,
		ScanResultID: request.ScanID,
		Title:        fmt.Sprintf("Security Report - %s", scanResult.ProjectPath),
		Format:       request.Format,
		Content:      &content,
		GeneratedAt:  time.Now(),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	c.JSON(http.StatusOK, report)
}

// generateReportContent generates report content based on scan results and format
func (h *Handler) generateReportContent(scanResult *models.ScanResult, format string) string {
	switch format {
	case "json":
		return h.generateJSONReport(scanResult)
	case "markdown":
		return h.generateMarkdownReport(scanResult)
	case "html":
		return h.generateHTMLReport(scanResult)
	default:
		return h.generateMarkdownReport(scanResult)
	}
}

// generateMarkdownReport generates a markdown report
func (h *Handler) generateMarkdownReport(scanResult *models.ScanResult) string {
	var report strings.Builder

	report.WriteString("# 🛡️ Blockchain Security Protocol Report\n\n")
	report.WriteString(fmt.Sprintf("**Project:** %s\n", scanResult.ProjectPath))
	report.WriteString(fmt.Sprintf("**Scan ID:** %s\n", scanResult.ID))
	report.WriteString(fmt.Sprintf("**Status:** %s\n", scanResult.Status))

	if scanResult.StartTime != nil {
		report.WriteString(fmt.Sprintf("**Start Time:** %s\n", scanResult.StartTime.Format(time.RFC3339)))
	}
	if scanResult.EndTime != nil {
		report.WriteString(fmt.Sprintf("**End Time:** %s\n", scanResult.EndTime.Format(time.RFC3339)))
	}
	if scanResult.Duration != nil {
		report.WriteString(fmt.Sprintf("**Duration:** %s\n", scanResult.Duration.String()))
	}

	report.WriteString(fmt.Sprintf("**Files Scanned:** %d\n", scanResult.FilesScanned))
	report.WriteString(fmt.Sprintf("**Lines Scanned:** %d\n", scanResult.LinesScanned))
	report.WriteString(fmt.Sprintf("**Chains:** %s\n\n", strings.Join(scanResult.Chains, ", ")))

	// Summary
	report.WriteString("## 📊 Summary\n\n")
	totalIssues := len(scanResult.Issues)
	report.WriteString(fmt.Sprintf("**Total Issues Found:** %d\n\n", totalIssues))

	// Severity breakdown
	severityCounts := make(map[string]int)
	for _, issue := range scanResult.Issues {
		severityCounts[issue.Severity]++
	}

	report.WriteString("### Severity Breakdown\n\n")
	for severity, count := range severityCounts {
		emoji := getSeverityEmoji(severity)
		report.WriteString(fmt.Sprintf("- %s **%s:** %d\n", emoji, strings.Title(severity), count))
	}
	report.WriteString("\n")

	// Issues by category
	report.WriteString("## 🔍 Security Issues\n\n")

	categoryGroups := make(map[string][]models.SecurityIssue)
	for _, issue := range scanResult.Issues {
		categoryGroups[issue.Category] = append(categoryGroups[issue.Category], issue)
	}

	for category, issues := range categoryGroups {
		report.WriteString(fmt.Sprintf("### %s (%d issues)\n\n", strings.Title(category), len(issues)))

		for _, issue := range issues {
			emoji := getSeverityEmoji(issue.Severity)
			report.WriteString(fmt.Sprintf("#### %s %s\n\n", emoji, issue.Title))
			report.WriteString(fmt.Sprintf("**Severity:** %s\n", strings.Title(issue.Severity)))
			report.WriteString(fmt.Sprintf("**File:** %s:%d\n", issue.File, issue.Line))
			report.WriteString(fmt.Sprintf("**Description:** %s\n\n", issue.Description))

			if issue.Code != "" {
				report.WriteString("**Code:**\n```\n")
				report.WriteString(issue.Code)
				report.WriteString("\n```\n\n")
			}

			if issue.Suggestion != "" {
				report.WriteString(fmt.Sprintf("**Recommendation:** %s\n\n", issue.Suggestion))
			}

			if len(issue.References) > 0 {
				report.WriteString("**References:**\n")
				for _, ref := range issue.References {
					report.WriteString(fmt.Sprintf("- %s\n", ref))
				}
				report.WriteString("\n")
			}

			report.WriteString("---\n\n")
		}
	}

	// Footer
	report.WriteString("## 📝 Notes\n\n")
	report.WriteString("This report was generated by the Blockchain Security Protocol Tool.\n")
	report.WriteString("For more information, visit: https://github.com/blockchain-spt\n\n")
	report.WriteString(fmt.Sprintf("*Generated on: %s*\n", time.Now().Format(time.RFC3339)))

	return report.String()
}

// generateJSONReport generates a JSON report
func (h *Handler) generateJSONReport(scanResult *models.ScanResult) string {
	// Return the scan result as JSON
	return fmt.Sprintf(`{
		"scan_id": "%s",
		"project_path": "%s",
		"status": "%s",
		"total_issues": %d,
		"files_scanned": %d,
		"lines_scanned": %d,
		"chains": %v,
		"severity_counts": %v,
		"issues": %v
	}`, scanResult.ID, scanResult.ProjectPath, scanResult.Status,
		len(scanResult.Issues), scanResult.FilesScanned, scanResult.LinesScanned,
		scanResult.Chains, scanResult.SeverityCounts, scanResult.Issues)
}

// generateHTMLReport generates an HTML report
func (h *Handler) generateHTMLReport(scanResult *models.ScanResult) string {
	var report strings.Builder

	report.WriteString(`<!DOCTYPE html>
<html>
<head>
    <title>SPT Security Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .issue { margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }
        .critical { border-color: #ff6b6b; }
        .high { border-color: #ffa500; }
        .medium { border-color: #ffeb3b; }
        .low { border-color: #4caf50; }
        .code { background: #f5f5f5; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>`)

	report.WriteString(fmt.Sprintf(`
    <div class="header">
        <h1>🛡️ Blockchain Security Report</h1>
        <p><strong>Project:</strong> %s</p>
        <p><strong>Scan ID:</strong> %s</p>
        <p><strong>Total Issues:</strong> %d</p>
    </div>`, scanResult.ProjectPath, scanResult.ID, len(scanResult.Issues)))

	for _, issue := range scanResult.Issues {
		report.WriteString(fmt.Sprintf(`
    <div class="issue %s">
        <h3>%s</h3>
        <p><strong>Severity:</strong> %s</p>
        <p><strong>File:</strong> %s:%d</p>
        <p><strong>Description:</strong> %s</p>
        <div class="code">%s</div>
    </div>`, issue.Severity, issue.Title, issue.Severity, issue.File, issue.Line, issue.Description, issue.Code))
	}

	report.WriteString(`
</body>
</html>`)

	return report.String()
}

// getSeverityEmoji returns emoji for severity level
func getSeverityEmoji(severity string) string {
	switch strings.ToLower(severity) {
	case "critical":
		return "🚨"
	case "high":
		return "⚠️"
	case "medium":
		return "⚡"
	case "low":
		return "ℹ️"
	default:
		return "📝"
	}
}

// generateSecurityChecklist generates a comprehensive security checklist
func (h *Handler) generateSecurityChecklist(chain string) []models.SecurityChecklist {
	var checklist []models.SecurityChecklist

	switch strings.ToLower(chain) {
	case "ethereum":
		checklist = append(checklist, h.getEthereumChecklist()...)
	case "bitcoin":
		checklist = append(checklist, h.getBitcoinChecklist()...)
	case "general":
		checklist = append(checklist, h.getGeneralChecklist()...)
	default:
		// Return all checklists
		checklist = append(checklist, h.getEthereumChecklist()...)
		checklist = append(checklist, h.getBitcoinChecklist()...)
		checklist = append(checklist, h.getGeneralChecklist()...)
	}

	return checklist
}

// getEthereumChecklist returns Ethereum-specific security checklist
func (h *Handler) getEthereumChecklist() []models.SecurityChecklist {
	items := []struct {
		category    string
		title       string
		description string
		priority    int
	}{
		{"Smart Contracts", "Reentrancy Protection", "Ensure all external calls are protected against reentrancy attacks", 1},
		{"Smart Contracts", "Integer Overflow/Underflow", "Use SafeMath or Solidity 0.8+ for arithmetic operations", 1},
		{"Smart Contracts", "Access Control", "Implement proper access control mechanisms", 1},
		{"Smart Contracts", "Gas Optimization", "Optimize gas usage to prevent DoS attacks", 2},
		{"Smart Contracts", "External Call Safety", "Validate all external contract calls", 1},
		{"Deployment", "Constructor Security", "Ensure constructor is properly secured", 2},
		{"Deployment", "Proxy Pattern Security", "If using proxies, ensure upgrade security", 2},
		{"Testing", "Unit Test Coverage", "Ensure comprehensive unit test coverage", 2},
		{"Testing", "Integration Testing", "Test contract interactions thoroughly", 2},
		{"Auditing", "Code Review", "Conduct thorough code reviews", 1},
		{"Auditing", "External Audit", "Consider external security audit for critical contracts", 1},
	}

	var checklist []models.SecurityChecklist
	for i, item := range items {
		desc := item.description
		checklist = append(checklist, models.SecurityChecklist{
			ID:          fmt.Sprintf("eth-%d", i+1),
			Chain:       "ethereum",
			Category:    item.category,
			Title:       item.title,
			Description: &desc,
			Priority:    item.priority,
			Status:      "pending",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return checklist
}

// getBitcoinChecklist returns Bitcoin-specific security checklist
func (h *Handler) getBitcoinChecklist() []models.SecurityChecklist {
	items := []struct {
		category    string
		title       string
		description string
		priority    int
	}{
		{"Scripts", "Script Validation", "Validate all Bitcoin scripts for security issues", 1},
		{"Scripts", "Multisig Security", "Ensure multisig implementations are secure", 1},
		{"Wallet", "Private Key Management", "Secure private key storage and handling", 1},
		{"Wallet", "Address Validation", "Validate Bitcoin addresses properly", 2},
		{"Transaction", "UTXO Management", "Proper UTXO selection and management", 2},
		{"Transaction", "Fee Calculation", "Implement proper fee calculation", 2},
		{"Network", "Node Security", "Secure Bitcoin node configuration", 2},
		{"Network", "P2P Communication", "Secure peer-to-peer communication", 3},
	}

	var checklist []models.SecurityChecklist
	for i, item := range items {
		desc := item.description
		checklist = append(checklist, models.SecurityChecklist{
			ID:          fmt.Sprintf("btc-%d", i+1),
			Chain:       "bitcoin",
			Category:    item.category,
			Title:       item.title,
			Description: &desc,
			Priority:    item.priority,
			Status:      "pending",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return checklist
}

// getGeneralChecklist returns general security checklist
func (h *Handler) getGeneralChecklist() []models.SecurityChecklist {
	items := []struct {
		category    string
		title       string
		description string
		priority    int
	}{
		{"Environment", "Environment Variables", "Secure handling of environment variables", 1},
		{"Environment", "Docker Security", "Secure Docker configuration", 2},
		{"Dependencies", "Vulnerability Scanning", "Regular dependency vulnerability scanning", 1},
		{"Dependencies", "License Compliance", "Ensure dependency license compliance", 3},
		{"Code Quality", "Code Review", "Mandatory code review process", 2},
		{"Code Quality", "Static Analysis", "Regular static code analysis", 2},
		{"Deployment", "CI/CD Security", "Secure CI/CD pipeline configuration", 2},
		{"Deployment", "Secret Management", "Proper secret management in deployment", 1},
		{"Monitoring", "Security Monitoring", "Implement security monitoring and alerting", 2},
		{"Monitoring", "Audit Logging", "Comprehensive audit logging", 2},
	}

	var checklist []models.SecurityChecklist
	for i, item := range items {
		desc := item.description
		checklist = append(checklist, models.SecurityChecklist{
			ID:          fmt.Sprintf("gen-%d", i+1),
			Chain:       "general",
			Category:    item.category,
			Title:       item.title,
			Description: &desc,
			Priority:    item.priority,
			Status:      "pending",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return checklist
}

// GetSecurityChecklist handles security checklist requests
func (h *Handler) GetSecurityChecklist(c *gin.Context) {
	chain := c.DefaultQuery("chain", "general")

	// Generate comprehensive security checklist based on chain
	checklist := h.generateSecurityChecklist(chain)

	c.JSON(http.StatusOK, gin.H{
		"checklist": checklist,
		"total":     len(checklist),
	})
}

// GetConfig handles configuration retrieval requests
func (h *Handler) GetConfig(c *gin.Context) {
	c.JSON(http.StatusOK, h.config)
}

// UpdateConfig handles configuration update requests
func (h *Handler) UpdateConfig(c *gin.Context) {
	var newConfig config.Config
	if err := c.ShouldBindJSON(&newConfig); err != nil {
		h.logger.WithError(err).Error("Invalid config update request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate configuration
	if err := h.validateConfig(&newConfig); err != nil {
		h.logger.WithError(err).Error("Invalid configuration")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid configuration",
			"details": err.Error(),
		})
		return
	}

	// Update configuration (in memory for now)
	// In a production system, this would persist to file/database
	h.config = &newConfig

	h.logger.Info("Configuration updated successfully")
	c.JSON(http.StatusOK, gin.H{
		"message": "Configuration updated successfully",
		"config":  newConfig,
	})
}

// validateConfig validates configuration parameters
func (h *Handler) validateConfig(cfg *config.Config) error {
	// Validate server configuration
	if cfg.Server.Port <= 0 || cfg.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", cfg.Server.Port)
	}

	if cfg.Server.Host == "" {
		return fmt.Errorf("server host cannot be empty")
	}

	// Validate database configuration
	if cfg.Database.Type != "" {
		validTypes := []string{"sqlite", "postgres", "mysql"}
		isValid := false
		for _, validType := range validTypes {
			if cfg.Database.Type == validType {
				isValid = true
				break
			}
		}
		if !isValid {
			return fmt.Errorf("invalid database type: %s", cfg.Database.Type)
		}
	}

	// Validate scanning configuration
	if len(cfg.Scanning.FileTypes) == 0 {
		return fmt.Errorf("at least one file type must be specified for scanning")
	}

	// Validate logging configuration
	validLevels := []string{"debug", "info", "warn", "error", "fatal", "panic"}
	isValidLevel := false
	for _, level := range validLevels {
		if cfg.Logging.Level == level {
			isValidLevel = true
			break
		}
	}
	if !isValidLevel {
		return fmt.Errorf("invalid logging level: %s", cfg.Logging.Level)
	}

	return nil
}
