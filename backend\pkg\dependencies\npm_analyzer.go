package dependencies

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// NPMAnalyzer analyzes NPM package files
type NPMAnalyzer struct {
	logger *logrus.Logger
}

// YarnAnalyzer analyzes Yarn package files
type YarnAnalyzer struct {
	logger *logrus.Logger
}

// PackageJSON represents the structure of package.json
type PackageJSON struct {
	Name            string                 `json:"name"`
	Version         string                 `json:"version"`
	Dependencies    map[string]string      `json:"dependencies"`
	DevDependencies map[string]string      `json:"devDependencies"`
	Scripts         map[string]string      `json:"scripts"`
	Engines         map[string]string      `json:"engines"`
	Repository      interface{}            `json:"repository"`
	License         string                 `json:"license"`
	Private         bool                   `json:"private"`
	Workspaces      interface{}            `json:"workspaces"`
	Config          map[string]interface{} `json:"config"`
}

// PackageLock represents the structure of package-lock.json
type PackageLock struct {
	Name            string                        `json:"name"`
	Version         string                        `json:"version"`
	LockfileVersion int                           `json:"lockfileVersion"`
	Dependencies    map[string]PackageLockEntry   `json:"dependencies"`
	Packages        map[string]PackageLockPackage `json:"packages"`
}

// PackageLockEntry represents a dependency entry in package-lock.json
type PackageLockEntry struct {
	Version      string                      `json:"version"`
	Resolved     string                      `json:"resolved"`
	Integrity    string                      `json:"integrity"`
	Dev          bool                        `json:"dev"`
	Dependencies map[string]PackageLockEntry `json:"dependencies"`
}

// PackageLockPackage represents a package entry in package-lock.json v2+
type PackageLockPackage struct {
	Version      string            `json:"version"`
	Resolved     string            `json:"resolved"`
	Integrity    string            `json:"integrity"`
	Dev          bool              `json:"dev"`
	Dependencies map[string]string `json:"dependencies"`
	Engines      map[string]string `json:"engines"`
}

// NewNPMAnalyzer creates a new NPM analyzer
func NewNPMAnalyzer() *NPMAnalyzer {
	return &NPMAnalyzer{
		logger: logrus.New(),
	}
}

// NewYarnAnalyzer creates a new Yarn analyzer
func NewYarnAnalyzer() *YarnAnalyzer {
	return &YarnAnalyzer{
		logger: logrus.New(),
	}
}

// AnalyzePackageFile analyzes NPM package files
func (npm *NPMAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	fileName := strings.ToLower(filePath)

	if strings.Contains(fileName, "package.json") {
		packageIssues, err := npm.analyzePackageJSON(filePath, content)
		if err != nil {
			return nil, err
		}
		issues = append(issues, packageIssues...)
	}

	if strings.Contains(fileName, "package-lock.json") {
		lockIssues, err := npm.analyzePackageLock(filePath, content)
		if err != nil {
			return nil, err
		}
		issues = append(issues, lockIssues...)
	}

	return issues, nil
}

// analyzePackageJSON analyzes package.json for security issues
func (npm *NPMAnalyzer) analyzePackageJSON(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// Validate JSON content
	if strings.TrimSpace(content) == "" {
		return nil, fmt.Errorf("package.json is empty")
	}

	var pkg PackageJSON
	if err := json.Unmarshal([]byte(content), &pkg); err != nil {
		npm.logger.Debugf("Failed to parse package.json at %s: %v", filePath, err)
		return nil, fmt.Errorf("failed to parse package.json: %w", err)
	}

	npm.logger.Debugf("Analyzing package.json: %s (name: %s, version: %s)", filePath, pkg.Name, pkg.Version)

	// Check for security issues in scripts
	if len(pkg.Scripts) > 0 {
		scriptIssues := npm.checkScripts(filePath, pkg.Scripts)
		issues = append(issues, scriptIssues...)
		npm.logger.Debugf("Found %d script issues", len(scriptIssues))
	}

	// Check for dependency issues
	if len(pkg.Dependencies) > 0 {
		depIssues := npm.checkDependencies(filePath, pkg.Dependencies, false)
		issues = append(issues, depIssues...)
		npm.logger.Debugf("Found %d dependency issues", len(depIssues))
	}

	if len(pkg.DevDependencies) > 0 {
		devDepIssues := npm.checkDependencies(filePath, pkg.DevDependencies, true)
		issues = append(issues, devDepIssues...)
		npm.logger.Debugf("Found %d dev dependency issues", len(devDepIssues))
	}

	// Check for configuration issues
	configIssues := npm.checkConfiguration(filePath, &pkg)
	issues = append(issues, configIssues...)

	// Check for known vulnerable packages
	vulnIssues := npm.checkKnownVulnerabilities(filePath, pkg.Dependencies)
	vulnIssues = append(vulnIssues, npm.checkKnownVulnerabilities(filePath, pkg.DevDependencies)...)
	issues = append(issues, vulnIssues...)
	npm.logger.Debugf("Found %d vulnerability issues", len(vulnIssues))

	return issues, nil
}

// analyzePackageLock analyzes package-lock.json for security issues
func (npm *NPMAnalyzer) analyzePackageLock(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	var lock PackageLock
	if err := json.Unmarshal([]byte(content), &lock); err != nil {
		return nil, fmt.Errorf("failed to parse package-lock.json: %w", err)
	}

	// Check for integrity issues
	issues = append(issues, npm.checkIntegrity(filePath, &lock)...)

	// Check for insecure protocols
	issues = append(issues, npm.checkProtocols(filePath, &lock)...)

	return issues, nil
}

// checkScripts checks npm scripts for security issues
func (npm *NPMAnalyzer) checkScripts(filePath string, scripts map[string]string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	dangerousPatterns := []struct {
		pattern     string
		description string
		severity    string
	}{
		{`curl\s+.*\|\s*sh`, "Piping curl output to shell", "high"},
		{`wget\s+.*\|\s*sh`, "Piping wget output to shell", "high"},
		{`eval\s*\(`, "Using eval() function", "medium"},
		{`rm\s+-rf\s+/`, "Dangerous file deletion", "critical"},
		{`sudo\s+`, "Using sudo in scripts", "medium"},
		{`chmod\s+777`, "Setting overly permissive file permissions", "medium"},
		{`\$\{.*\}`, "Unvalidated variable expansion", "low"},
		{`>\s*/dev/null\s+2>&1`, "Suppressing error output", "low"},
	}

	for scriptName, scriptContent := range scripts {
		for _, pattern := range dangerousPatterns {
			if matched, _ := regexp.MatchString(pattern.pattern, scriptContent); matched {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("npm_script_%s_%s", scriptName, pattern.pattern),
					Type:        "dangerous_script",
					Severity:    pattern.severity,
					Title:       "Dangerous NPM Script",
					Description: fmt.Sprintf("Script '%s' contains dangerous pattern: %s", scriptName, pattern.description),
					File:        filePath,
					Line:        1,
					Code:        fmt.Sprintf("\"%s\": \"%s\"", scriptName, scriptContent),
					Chain:       "general",
					Category:    "dependency",
					Suggestion:  "Review and sanitize the script command",
					References:  []string{"https://docs.npmjs.com/cli/v7/using-npm/scripts"},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkDependencies checks dependencies for security issues
func (npm *NPMAnalyzer) checkDependencies(filePath string, deps map[string]string, isDev bool) []models.SecurityIssue {
	var issues []models.SecurityIssue

	for name, version := range deps {
		// Check for wildcard versions
		if strings.Contains(version, "*") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("npm_wildcard_%s", name),
				Type:        "wildcard_dependency",
				Severity:    "medium",
				Title:       "Wildcard Dependency Version",
				Description: fmt.Sprintf("Package '%s' uses wildcard version '%s'", name, version),
				File:        filePath,
				Line:        1,
				Code:        fmt.Sprintf("\"%s\": \"%s\"", name, version),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Pin to specific version to ensure reproducible builds",
				References:  []string{"https://docs.npmjs.com/cli/v7/configuring-npm/package-json"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for git dependencies
		if strings.Contains(version, "git+") || strings.Contains(version, "github:") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("npm_git_dep_%s", name),
				Type:        "git_dependency",
				Severity:    "low",
				Title:       "Git Dependency",
				Description: fmt.Sprintf("Package '%s' is installed from git repository", name),
				File:        filePath,
				Line:        1,
				Code:        fmt.Sprintf("\"%s\": \"%s\"", name, version),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Consider using published npm packages for better security",
				References:  []string{"https://docs.npmjs.com/cli/v7/configuring-npm/package-json"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for HTTP URLs
		if strings.HasPrefix(version, "http://") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("npm_http_dep_%s", name),
				Type:        "insecure_dependency",
				Severity:    "high",
				Title:       "Insecure HTTP Dependency",
				Description: fmt.Sprintf("Package '%s' is downloaded over insecure HTTP", name),
				File:        filePath,
				Line:        1,
				Code:        fmt.Sprintf("\"%s\": \"%s\"", name, version),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS URLs for secure package downloads",
				References:  []string{"https://docs.npmjs.com/cli/v7/configuring-npm/package-json"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for deprecated packages
		if npm.isDeprecatedPackage(name) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("npm_deprecated_%s", name),
				Type:        "deprecated_dependency",
				Severity:    "medium",
				Title:       "Deprecated Package",
				Description: fmt.Sprintf("Package '%s' is deprecated and should be replaced", name),
				File:        filePath,
				Line:        1,
				Code:        fmt.Sprintf("\"%s\": \"%s\"", name, version),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  npm.getDeprecationSuggestion(name),
				References:  []string{"https://docs.npmjs.com/cli/v7/commands/npm-deprecate"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// checkConfiguration checks package.json configuration for security issues
func (npm *NPMAnalyzer) checkConfiguration(filePath string, pkg *PackageJSON) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check if package is not private but has no license
	if !pkg.Private && pkg.License == "" {
		issues = append(issues, models.SecurityIssue{
			ID:          "npm_no_license",
			Type:        "missing_license",
			Severity:    "low",
			Title:       "Missing License",
			Description: "Public package has no license specified",
			File:        filePath,
			Line:        1,
			Code:        "\"license\": \"\"",
			Chain:       "general",
			Category:    "dependency",
			Suggestion:  "Add appropriate license to package.json",
			References:  []string{"https://docs.npmjs.com/cli/v7/configuring-npm/package-json#license"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// Check for engines specification
	if len(pkg.Engines) == 0 {
		issues = append(issues, models.SecurityIssue{
			ID:          "npm_no_engines",
			Type:        "missing_engines",
			Severity:    "low",
			Title:       "Missing Engines Specification",
			Description: "Package does not specify required Node.js version",
			File:        filePath,
			Line:        1,
			Code:        "\"engines\": {}",
			Chain:       "general",
			Category:    "dependency",
			Suggestion:  "Specify required Node.js and npm versions in engines field",
			References:  []string{"https://docs.npmjs.com/cli/v7/configuring-npm/package-json#engines"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return issues
}

// checkKnownVulnerabilities checks for known vulnerable packages
func (npm *NPMAnalyzer) checkKnownVulnerabilities(filePath string, deps map[string]string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	vulnerablePackages := map[string]struct {
		versions    []string
		description string
		severity    string
		cve         string
	}{
		"lodash": {
			versions:    []string{"<4.17.12"},
			description: "Prototype pollution vulnerability",
			severity:    "high",
			cve:         "CVE-2019-10744",
		},
		"axios": {
			versions:    []string{"<0.21.1"},
			description: "Server-Side Request Forgery vulnerability",
			severity:    "medium",
			cve:         "CVE-2020-28168",
		},
		"minimist": {
			versions:    []string{"<1.2.2"},
			description: "Prototype pollution vulnerability",
			severity:    "medium",
			cve:         "CVE-2020-7598",
		},
		"yargs-parser": {
			versions:    []string{"<18.1.2"},
			description: "Prototype pollution vulnerability",
			severity:    "low",
			cve:         "CVE-2020-7608",
		},
	}

	for name, version := range deps {
		if vuln, exists := vulnerablePackages[name]; exists {
			// Simple version check (in real implementation, use semver)
			if npm.isVulnerableVersion(version, vuln.versions) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("npm_vuln_%s", name),
					Type:        "vulnerable_dependency",
					Severity:    vuln.severity,
					Title:       "Vulnerable NPM Package",
					Description: fmt.Sprintf("Package '%s' version '%s' has known vulnerability: %s", name, version, vuln.description),
					File:        filePath,
					Line:        1,
					Code:        fmt.Sprintf("\"%s\": \"%s\"", name, version),
					Chain:       "general",
					Category:    "dependency",
					CWE:         "CWE-1035",
					Suggestion:  "Update to latest secure version",
					References:  []string{fmt.Sprintf("https://nvd.nist.gov/vuln/detail/%s", vuln.cve)},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkIntegrity checks package-lock.json for integrity issues
func (npm *NPMAnalyzer) checkIntegrity(filePath string, lock *PackageLock) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check for missing integrity hashes
	for name, entry := range lock.Dependencies {
		if entry.Integrity == "" && entry.Resolved != "" {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("npm_no_integrity_%s", name),
				Type:        "missing_integrity",
				Severity:    "medium",
				Title:       "Missing Package Integrity",
				Description: fmt.Sprintf("Package '%s' has no integrity hash", name),
				File:        filePath,
				Line:        1,
				Code:        fmt.Sprintf("\"%s\": {\"version\": \"%s\"}", name, entry.Version),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Regenerate package-lock.json to include integrity hashes",
				References:  []string{"https://docs.npmjs.com/cli/v7/configuring-npm/package-lock-json"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// checkProtocols checks for insecure protocols in package-lock.json
func (npm *NPMAnalyzer) checkProtocols(filePath string, lock *PackageLock) []models.SecurityIssue {
	var issues []models.SecurityIssue

	for name, entry := range lock.Dependencies {
		if strings.HasPrefix(entry.Resolved, "http://") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("npm_http_resolved_%s", name),
				Type:        "insecure_protocol",
				Severity:    "high",
				Title:       "Insecure Package Download",
				Description: fmt.Sprintf("Package '%s' is downloaded over insecure HTTP", name),
				File:        filePath,
				Line:        1,
				Code:        fmt.Sprintf("\"resolved\": \"%s\"", entry.Resolved),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS registry or update npm configuration",
				References:  []string{"https://docs.npmjs.com/cli/v7/using-npm/registry"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// Helper functions

func (npm *NPMAnalyzer) isDeprecatedPackage(name string) bool {
	deprecated := []string{
		"request", "bower", "gulp-util", "minimatch", "graceful-fs",
		"natives", "coffee-script", "node-uuid", "jade",
	}

	for _, dep := range deprecated {
		if name == dep {
			return true
		}
	}
	return false
}

func (npm *NPMAnalyzer) getDeprecationSuggestion(name string) string {
	suggestions := map[string]string{
		"request":       "Use axios, node-fetch, or built-in fetch",
		"bower":         "Use npm or yarn instead",
		"gulp-util":     "Use individual utility packages",
		"coffee-script": "Use coffeescript package",
		"node-uuid":     "Use uuid package",
		"jade":          "Use pug package",
	}

	if suggestion, exists := suggestions[name]; exists {
		return suggestion
	}
	return "Find alternative package or remove if unused"
}

func (npm *NPMAnalyzer) isVulnerableVersion(version string, vulnerableVersions []string) bool {
	// Simplified version check - in real implementation, use semver library
	for _, vulnVersion := range vulnerableVersions {
		if strings.HasPrefix(vulnVersion, "<") {
			// This is a simplified check
			return true
		}
	}
	return false
}

// GetSupportedFiles returns supported file patterns
func (npm *NPMAnalyzer) GetSupportedFiles() []string {
	return []string{"package.json", "package-lock.json"}
}

// GetPackageManager returns the package manager name
func (npm *NPMAnalyzer) GetPackageManager() string {
	return "npm"
}

// Yarn analyzer methods (similar to NPM)

// AnalyzePackageFile analyzes Yarn package files
func (yarn *YarnAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	// Yarn uses the same package.json format as npm
	npm := NewNPMAnalyzer()

	if strings.Contains(filePath, "package.json") {
		return npm.analyzePackageJSON(filePath, content)
	}

	if strings.Contains(filePath, "yarn.lock") {
		return yarn.analyzeYarnLock(filePath, content)
	}

	return []models.SecurityIssue{}, nil
}

// analyzeYarnLock analyzes yarn.lock for security issues
func (yarn *YarnAnalyzer) analyzeYarnLock(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// Check for HTTP URLs in yarn.lock
	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if strings.Contains(line, "resolved") && strings.Contains(line, "http://") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("yarn_http_resolved_%d", i),
				Type:        "insecure_protocol",
				Severity:    "high",
				Title:       "Insecure Package Download",
				Description: "Package is downloaded over insecure HTTP",
				File:        filePath,
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS registry or update yarn configuration",
				References:  []string{"https://yarnpkg.com/configuration/yarnrc"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues, nil
}

// GetSupportedFiles returns supported file patterns
func (yarn *YarnAnalyzer) GetSupportedFiles() []string {
	return []string{"package.json", "yarn.lock"}
}

// GetPackageManager returns the package manager name
func (yarn *YarnAnalyzer) GetPackageManager() string {
	return "yarn"
}
