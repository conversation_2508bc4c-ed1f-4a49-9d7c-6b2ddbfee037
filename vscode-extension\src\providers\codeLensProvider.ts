import * as vscode from 'vscode';
import { SecurityAnalyzer } from '../security/analyzer';

export class CodeLensProvider implements vscode.CodeLensProvider {
    constructor(private securityAnalyzer: SecurityAnalyzer) {}

    provideCodeLenses(document: vscode.TextDocument, token: vscode.CancellationToken): vscode.CodeLens[] | Thenable<vscode.CodeLens[]> {
        const codeLenses: vscode.CodeLens[] = [];
        
        // Add security scan CodeLens at the top of the file
        const range = new vscode.Range(0, 0, 0, 0);
        const scanCommand: vscode.Command = {
            title: '🛡️ Scan for Security Issues',
            command: 'spt.scanFile',
            arguments: [document.uri.fsPath]
        };
        
        codeLenses.push(new vscode.CodeLens(range, scanCommand));
        
        return codeLenses;
    }
}
