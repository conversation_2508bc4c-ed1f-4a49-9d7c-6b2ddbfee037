import * as vscode from 'vscode';

export class CompletionProvider implements vscode.CompletionItemProvider {
    provideCompletionItems(document: vscode.TextDocument, position: vscode.Position, token: vscode.CancellationToken, context: vscode.CompletionContext): vscode.ProviderResult<vscode.CompletionItem[] | vscode.CompletionList<vscode.CompletionItem>> {
        const completionItems: vscode.CompletionItem[] = [];
        
        // Add security-related completions for Solidity
        if (document.languageId === 'solidity') {
            // Safe math operations
            const safeMathCompletion = new vscode.CompletionItem('SafeMath', vscode.CompletionItemKind.Snippet);
            safeMathCompletion.insertText = new vscode.SnippetString('using SafeMath for uint256;');
            safeMathCompletion.documentation = new vscode.MarkdownString('Use SafeMath library to prevent integer overflow/underflow');
            completionItems.push(safeMathCompletion);
            
            // Reentrancy guard
            const reentrancyGuardCompletion = new vscode.CompletionItem('ReentrancyGuard', vscode.CompletionItemKind.Snippet);
            reentrancyGuardCompletion.insertText = new vscode.SnippetString('modifier nonReentrant() {\n    require(!_locked, "ReentrancyGuard: reentrant call");\n    _locked = true;\n    _;\n    _locked = false;\n}');
            reentrancyGuardCompletion.documentation = new vscode.MarkdownString('Add reentrancy protection to functions');
            completionItems.push(reentrancyGuardCompletion);
        }
        
        return completionItems;
    }
}
