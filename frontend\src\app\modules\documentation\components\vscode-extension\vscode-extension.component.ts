import { Component } from '@angular/core';

interface ExtensionFeature {
  title: string;
  description: string;
  icon: string;
  screenshot?: string;
  benefits: string[];
}

interface ConfigSetting {
  key: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-vscode-extension',
  template: `
    <div class="vscode-extension-container">
      <div class="page-header">
        <h1>
          <mat-icon>extension</mat-icon>
          VS Code Extension
        </h1>
        <p class="page-subtitle">
          Real-time security analysis directly in your code editor
        </p>
      </div>

      <div class="extension-overview">
        <mat-card class="overview-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>info</mat-icon>
            <mat-card-title>SPT VS Code Extension</mat-card-title>
            <mat-card-subtitle>Integrated security scanning for blockchain development</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <p>The SPT VS Code extension brings powerful security analysis directly into your development environment, providing real-time feedback and suggestions as you code.</p>
            <div class="quick-stats">
              <div class="stat">
                <mat-icon>security</mat-icon>
                <div>
                  <strong>Real-time Scanning</strong>
                  <p>Instant security feedback</p>
                </div>
              </div>
              <div class="stat">
                <mat-icon>lightbulb</mat-icon>
                <div>
                  <strong>Smart Suggestions</strong>
                  <p>Contextual security recommendations</p>
                </div>
              </div>
              <div class="stat">
                <mat-icon>integration_instructions</mat-icon>
                <div>
                  <strong>Seamless Integration</strong>
                  <p>Works with existing workflow</p>
                </div>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-raised-button color="primary">
              <mat-icon>download</mat-icon>
              Install Extension
            </button>
            <button mat-stroked-button>
              <mat-icon>code</mat-icon>
              View Source
            </button>
          </mat-card-actions>
        </mat-card>
      </div>

      <mat-tab-group class="extension-tabs" animationDuration="300ms">
        <!-- Features Tab -->
        <mat-tab label="Features">
          <div class="tab-content">
            <h2>Key Features</h2>
            <p>Discover the powerful features that make SPT extension essential for secure blockchain development.</p>
            
            <div class="features-grid">
              <mat-card class="feature-card" *ngFor="let feature of extensionFeatures">
                <mat-card-header>
                  <mat-icon mat-card-avatar [style.background-color]="getFeatureColor(feature.icon)">
                    {{ feature.icon }}
                  </mat-icon>
                  <mat-card-title>{{ feature.title }}</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <p>{{ feature.description }}</p>
                  <div class="benefits-list" *ngIf="feature.benefits.length > 0">
                    <h4>Benefits:</h4>
                    <ul>
                      <li *ngFor="let benefit of feature.benefits">{{ benefit }}</li>
                    </ul>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Installation Tab -->
        <mat-tab label="Installation">
          <div class="tab-content">
            <h2>Installation Guide</h2>
            <p>Get the SPT extension installed and configured in VS Code.</p>
            
            <div class="installation-steps">
              <mat-card class="step-card" *ngFor="let step of installationSteps; let i = index">
                <mat-card-header>
                  <div mat-card-avatar class="step-number">{{ i + 1 }}</div>
                  <mat-card-title>{{ step.title }}</mat-card-title>
                  <mat-card-subtitle>{{ step.subtitle }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <p>{{ step.description }}</p>
                  <div class="step-content">
                    <div class="code-block" *ngIf="step.command">
                      <div class="code-header">
                        <mat-icon>terminal</mat-icon>
                        <span>Command</span>
                      </div>
                      <pre><code>{{ step.command }}</code></pre>
                    </div>
                    <div class="step-image" *ngIf="step.image">
                      <img [src]="step.image" [alt]="step.title" />
                    </div>
                  </div>
                  <div class="step-notes" *ngIf="step.notes">
                    <mat-icon>info</mat-icon>
                    <span>{{ step.notes }}</span>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Configuration Tab -->
        <mat-tab label="Configuration">
          <div class="tab-content">
            <h2>Extension Configuration</h2>
            <p>Customize the SPT extension to fit your development workflow and preferences.</p>
            
            <div class="config-sections">
              <mat-card class="config-section">
                <mat-card-header>
                  <mat-icon mat-card-avatar>settings</mat-icon>
                  <mat-card-title>Settings Overview</mat-card-title>
                  <mat-card-subtitle>Configure extension behavior</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <p>Access extension settings through VS Code preferences:</p>
                  <div class="code-block">
                    <div class="code-header">
                      <mat-icon>keyboard</mat-icon>
                      <span>Keyboard Shortcut</span>
                    </div>
                    <pre><code>Ctrl+Shift+P → "Preferences: Open Settings (JSON)"</code></pre>
                  </div>
                </mat-card-content>
              </mat-card>

              <mat-card class="config-section">
                <mat-card-header>
                  <mat-icon mat-card-avatar>code</mat-icon>
                  <mat-card-title>Configuration Example</mat-card-title>
                  <mat-card-subtitle>settings.json</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="code-block">
                    <div class="code-header">
                      <mat-icon>code</mat-icon>
                      <span>JSON Configuration</span>
                    </div>
                    <pre><code>{{ configExample }}</code></pre>
                  </div>
                </mat-card-content>
              </mat-card>

              <mat-card class="config-section">
                <mat-card-header>
                  <mat-icon mat-card-avatar>list</mat-icon>
                  <mat-card-title>Available Settings</mat-card-title>
                  <mat-card-subtitle>Complete settings reference</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <table mat-table [dataSource]="configSettings" class="settings-table">
                    <ng-container matColumnDef="key">
                      <th mat-header-cell *matHeaderCellDef>Setting</th>
                      <td mat-cell *matCellDef="let setting">
                        <code>{{ setting.key }}</code>
                      </td>
                    </ng-container>
                    <ng-container matColumnDef="type">
                      <th mat-header-cell *matHeaderCellDef>Type</th>
                      <td mat-cell *matCellDef="let setting">
                        <mat-chip>{{ setting.type }}</mat-chip>
                      </td>
                    </ng-container>
                    <ng-container matColumnDef="default">
                      <th mat-header-cell *matHeaderCellDef>Default</th>
                      <td mat-cell *matCellDef="let setting">
                        <code>{{ setting.default }}</code>
                      </td>
                    </ng-container>
                    <ng-container matColumnDef="description">
                      <th mat-header-cell *matHeaderCellDef>Description</th>
                      <td mat-cell *matCellDef="let setting">{{ setting.description }}</td>
                    </ng-container>
                    <tr mat-header-row *matHeaderRowDef="settingsColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: settingsColumns;"></tr>
                  </table>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Usage Tab -->
        <mat-tab label="Usage">
          <div class="tab-content">
            <h2>Using the Extension</h2>
            <p>Learn how to effectively use SPT extension features in your daily development workflow.</p>
            
            <div class="usage-sections">
              <mat-expansion-panel-group class="usage-panels">
                <mat-expansion-panel *ngFor="let usage of usageExamples" [expanded]="usage.expanded">
                  <mat-expansion-panel-header>
                    <mat-panel-title>
                      <mat-icon>{{ usage.icon }}</mat-icon>
                      {{ usage.title }}
                    </mat-panel-title>
                    <mat-panel-description>
                      {{ usage.description }}
                    </mat-panel-description>
                  </mat-expansion-panel-header>
                  
                  <div class="usage-content">
                    <p>{{ usage.details }}</p>
                    <div class="usage-steps" *ngIf="usage.steps">
                      <h4>Steps:</h4>
                      <ol>
                        <li *ngFor="let step of usage.steps">{{ step }}</li>
                      </ol>
                    </div>
                    <div class="usage-tips" *ngIf="usage.tips">
                      <h4>Tips:</h4>
                      <ul class="tips-list">
                        <li *ngFor="let tip of usage.tips">
                          <mat-icon>lightbulb</mat-icon>
                          <span>{{ tip }}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </mat-expansion-panel>
              </mat-expansion-panel-group>
            </div>
          </div>
        </mat-tab>

        <!-- Troubleshooting Tab -->
        <mat-tab label="Troubleshooting">
          <div class="tab-content">
            <h2>Troubleshooting</h2>
            <p>Common issues and solutions for the SPT VS Code extension.</p>
            
            <div class="troubleshooting-sections">
              <mat-card class="troubleshooting-card" *ngFor="let issue of troubleshootingIssues">
                <mat-card-header>
                  <mat-icon mat-card-avatar [style.background-color]="issue.severity === 'high' ? '#f44336' : '#ff9800'">
                    {{ issue.icon }}
                  </mat-icon>
                  <mat-card-title>{{ issue.problem }}</mat-card-title>
                  <mat-card-subtitle>{{ issue.description }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="solution-steps">
                    <h4>Solution:</h4>
                    <ol>
                      <li *ngFor="let step of issue.solution">{{ step }}</li>
                    </ol>
                  </div>
                  <div class="prevention-tips" *ngIf="issue.prevention">
                    <h4>Prevention:</h4>
                    <p>{{ issue.prevention }}</p>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styles: [`
    .vscode-extension-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      text-align: center;
      margin-bottom: 32px;
    }

    .page-header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      color: #1976d2;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      color: #666;
      font-size: 1.1em;
      margin: 0;
    }

    .extension-overview {
      margin-bottom: 32px;
    }

    .overview-card {
      border: 1px solid #e0e0e0;
    }

    .quick-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }

    .stat {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .stat mat-icon {
      color: #1976d2;
    }

    .stat strong {
      display: block;
      margin-bottom: 4px;
    }

    .stat p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    .extension-tabs {
      margin-bottom: 32px;
    }

    .tab-content {
      padding: 24px 0;
    }

    .tab-content h2 {
      color: #1976d2;
      margin-bottom: 8px;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .feature-card {
      height: 100%;
    }

    .benefits-list {
      margin-top: 16px;
    }

    .benefits-list h4 {
      margin: 0 0 8px 0;
      color: #1976d2;
      font-size: 0.9em;
    }

    .benefits-list ul {
      margin: 0;
      padding-left: 20px;
    }

    .benefits-list li {
      margin-bottom: 4px;
      color: #666;
      font-size: 0.9em;
    }

    .installation-steps {
      margin-top: 24px;
    }

    .step-card {
      margin-bottom: 24px;
    }

    .step-number {
      background: #1976d2;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }

    .step-content {
      margin-top: 16px;
    }

    .code-block {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
      margin: 16px 0;
    }

    .code-header {
      background: #f5f5f5;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      border-bottom: 1px solid #e0e0e0;
    }

    .code-block pre {
      margin: 0;
      padding: 16px;
      background: #fafafa;
      overflow-x: auto;
    }

    .code-block code {
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }

    .step-image img {
      max-width: 100%;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
    }

    .step-notes {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      padding: 8px 12px;
      background: #e3f2fd;
      border-radius: 4px;
      color: #1976d2;
    }

    .config-sections {
      margin-top: 24px;
    }

    .config-section {
      margin-bottom: 24px;
    }

    .settings-table {
      width: 100%;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .settings-table code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.9em;
    }

    .usage-sections {
      margin-top: 24px;
    }

    .usage-panels {
      margin-top: 16px;
    }

    .usage-content {
      padding: 16px 0;
    }

    .usage-steps,
    .usage-tips {
      margin-top: 16px;
    }

    .usage-steps h4,
    .usage-tips h4 {
      margin: 0 0 8px 0;
      color: #1976d2;
    }

    .tips-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .tips-list li {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 8px;
      padding: 8px;
      background: #fff3e0;
      border-radius: 4px;
    }

    .tips-list mat-icon {
      color: #f57c00;
      margin-top: 2px;
    }

    .troubleshooting-sections {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .troubleshooting-card {
      height: fit-content;
    }

    .solution-steps,
    .prevention-tips {
      margin-top: 16px;
    }

    .solution-steps h4,
    .prevention-tips h4 {
      margin: 0 0 8px 0;
      color: #1976d2;
    }

    .solution-steps ol {
      margin: 0;
      padding-left: 20px;
    }

    .solution-steps li {
      margin-bottom: 8px;
      color: #666;
    }

    .prevention-tips p {
      margin: 0;
      color: #666;
    }

    @media (max-width: 768px) {
      .quick-stats {
        grid-template-columns: 1fr;
      }
      
      .features-grid {
        grid-template-columns: 1fr;
      }
      
      .troubleshooting-sections {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class VscodeExtensionComponent {
  settingsColumns: string[] = ['key', 'type', 'default', 'description'];

  configExample = `{
  "spt.enabled": true,
  "spt.serverUrl": "http://localhost:8080",
  "spt.apiKey": "your-api-key-here",
  "spt.autoScan": true,
  "spt.scanOnOpen": false,
  "spt.chains": ["ethereum", "bitcoin", "general"],
  "spt.severity": "medium",
  "spt.showInlineDecorations": true,
  "spt.showProblems": true,
  "spt.enableCodeLens": true,
  "spt.enableHover": true
}`;

  extensionFeatures: ExtensionFeature[] = [
    {
      title: 'Real-time Security Scanning',
      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities.',
      icon: 'security',
      benefits: [
        'Immediate vulnerability detection',
        'Reduced security debt',
        'Faster development cycles',
        'Proactive security measures'
      ]
    },
    {
      title: 'Inline Decorations',
      description: 'Visual indicators directly in your code highlighting security issues and suggestions.',
      icon: 'visibility',
      benefits: [
        'Clear visual feedback',
        'Context-aware highlighting',
        'Severity-based color coding',
        'Non-intrusive indicators'
      ]
    },
    {
      title: 'Problems Panel Integration',
      description: 'Security issues appear in VS Code\'s Problems panel with detailed descriptions and fixes.',
      icon: 'bug_report',
      benefits: [
        'Centralized issue tracking',
        'Detailed error descriptions',
        'Quick navigation to issues',
        'Integration with existing workflow'
      ]
    },
    {
      title: 'CodeLens Integration',
      description: 'Actionable security suggestions and metrics displayed directly above your code.',
      icon: 'lens',
      benefits: [
        'Contextual security metrics',
        'One-click security actions',
        'Code quality insights',
        'Performance recommendations'
      ]
    },
    {
      title: 'Hover Information',
      description: 'Detailed security information and recommendations on hover over code elements.',
      icon: 'info',
      benefits: [
        'Instant security documentation',
        'Best practice suggestions',
        'Vulnerability explanations',
        'Quick reference access'
      ]
    },
    {
      title: 'Multi-chain Support',
      description: 'Support for Ethereum, Bitcoin, and general blockchain security patterns.',
      icon: 'link',
      benefits: [
        'Comprehensive blockchain coverage',
        'Chain-specific security rules',
        'Unified security approach',
        'Extensible architecture'
      ]
    }
  ];

  installationSteps = [
    {
      title: 'Install from VS Code Marketplace',
      subtitle: 'Recommended method',
      description: 'Search for "SPT Security" in the VS Code Extensions marketplace and install.',
      command: 'ext install blockchain-spt.vscode-spt',
      notes: 'Extension will be automatically activated after installation'
    },
    {
      title: 'Configure Backend Connection',
      subtitle: 'Connect to SPT server',
      description: 'Configure the extension to connect to your SPT backend server.',
      command: 'Configure spt.serverUrl in VS Code settings',
      notes: 'Default server URL is http://localhost:8080'
    },
    {
      title: 'Verify Installation',
      subtitle: 'Test the connection',
      description: 'Open a blockchain project and verify that security scanning is working.',
      notes: 'Check the status bar for SPT connection indicator'
    }
  ];

  configSettings: ConfigSetting[] = [
    { key: 'spt.enabled', type: 'boolean', default: 'true', description: 'Enable/disable SPT security analysis' },
    { key: 'spt.serverUrl', type: 'string', default: 'http://localhost:8080', description: 'SPT backend server URL' },
    { key: 'spt.apiKey', type: 'string', default: '""', description: 'API key for authentication' },
    { key: 'spt.autoScan', type: 'boolean', default: 'true', description: 'Automatically scan files on save' },
    { key: 'spt.scanOnOpen', type: 'boolean', default: 'false', description: 'Automatically scan files when opened' },
    { key: 'spt.chains', type: 'array', default: '["ethereum", "bitcoin", "general"]', description: 'Blockchain chains to analyze' },
    { key: 'spt.severity', type: 'string', default: '"medium"', description: 'Minimum severity level to show' },
    { key: 'spt.showInlineDecorations', type: 'boolean', default: 'true', description: 'Show inline security decorations' },
    { key: 'spt.showProblems', type: 'boolean', default: 'true', description: 'Show security issues in Problems panel' },
    { key: 'spt.enableCodeLens', type: 'boolean', default: 'true', description: 'Enable security-related CodeLens' },
    { key: 'spt.enableHover', type: 'boolean', default: 'true', description: 'Enable security information on hover' }
  ];

  usageExamples = [
    {
      title: 'Scanning Files',
      description: 'How to scan files for security issues',
      icon: 'scanner',
      expanded: true,
      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',
      steps: [
        'Open a blockchain project in VS Code',
        'Save a file to trigger automatic scanning',
        'Or use Ctrl+Shift+P → "SPT: Scan Current File"',
        'View results in Problems panel or inline decorations'
      ],
      tips: [
        'Enable auto-scan for continuous security monitoring',
        'Use the Problems panel to navigate between issues',
        'Check the status bar for scan progress'
      ]
    },
    {
      title: 'Understanding Security Issues',
      description: 'How to interpret and resolve security findings',
      icon: 'help',
      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',
      steps: [
        'Hover over highlighted code to see issue details',
        'Click on issues in Problems panel for more information',
        'Use CodeLens actions for quick fixes',
        'Follow the recommended solutions'
      ],
      tips: [
        'Start with critical and high severity issues',
        'Use hover information for quick context',
        'Check references for additional learning'
      ]
    },
    {
      title: 'Configuring Scan Settings',
      description: 'Customize scanning behavior for your project',
      icon: 'tune',
      details: 'Adjust scan settings to match your project requirements and development workflow.',
      steps: [
        'Open VS Code settings (Ctrl+,)',
        'Search for "SPT" to find extension settings',
        'Adjust chains, severity, and scan triggers',
        'Save settings and restart if needed'
      ],
      tips: [
        'Use workspace settings for project-specific configuration',
        'Adjust severity threshold based on project maturity',
        'Enable scan-on-open for comprehensive coverage'
      ]
    }
  ];

  troubleshootingIssues = [
    {
      problem: 'Extension Not Connecting to Server',
      description: 'SPT extension cannot connect to the backend server',
      icon: 'cloud_off',
      severity: 'high',
      solution: [
        'Verify SPT backend server is running on configured port',
        'Check spt.serverUrl setting in VS Code preferences',
        'Ensure firewall is not blocking the connection',
        'Try restarting VS Code and the SPT server'
      ],
      prevention: 'Always start the SPT backend server before using the extension'
    },
    {
      problem: 'No Security Issues Detected',
      description: 'Extension is running but not finding any security issues',
      icon: 'search_off',
      severity: 'medium',
      solution: [
        'Check if the file type is supported (Solidity, JavaScript, etc.)',
        'Verify the correct blockchain chains are selected',
        'Lower the severity threshold in settings',
        'Ensure the file contains actual security-relevant code'
      ],
      prevention: 'Review supported file types and ensure proper project structure'
    },
    {
      problem: 'Performance Issues',
      description: 'Extension is causing VS Code to slow down',
      icon: 'speed',
      severity: 'medium',
      solution: [
        'Disable auto-scan and use manual scanning',
        'Increase scan timeout in settings',
        'Exclude large files or directories from scanning',
        'Reduce the number of enabled blockchain chains'
      ],
      prevention: 'Configure appropriate scan settings for your project size'
    }
  ];

  getFeatureColor(icon: string): string {
    const colors: { [key: string]: string } = {
      'security': '#1976d2',
      'visibility': '#4caf50',
      'bug_report': '#f44336',
      'lens': '#ff9800',
      'info': '#9c27b0',
      'link': '#00bcd4'
    };
    return colors[icon] || '#1976d2';
  }
}
