import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatTableModule } from '@angular/material/table';

interface ExtensionFeature {
  title: string;
  description: string;
  icon: string;
  color: string;
  screenshot?: string;
  benefits: string[];
}

interface ConfigSetting {
  key: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-vscode-extension',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatChipsModule,
    MatExpansionModule,
    MatTableModule
  ],
  template: `
    <div class="spt-vscode-container">
      <!-- Hero Section -->
      <header class="spt-hero-section">
        <div class="spt-hero-content">
          <div class="spt-hero-icon">
            <mat-icon>extension</mat-icon>
          </div>
          <div class="spt-hero-text">
            <h1 class="spt-hero-title">VS Code Extension</h1>
            <p class="spt-hero-subtitle">
              Real-time security analysis directly in your code editor with intelligent suggestions and seamless workflow integration.
            </p>
          </div>
        </div>
        <div class="spt-hero-actions">
          <button mat-raised-button color="primary" class="spt-primary-btn">
            <mat-icon>download</mat-icon>
            <span>Install Extension</span>
          </button>
          <button mat-stroked-button class="spt-secondary-btn">
            <mat-icon>code</mat-icon>
            <span>View Source</span>
          </button>
        </div>
      </header>

      <!-- Key Features Overview -->
      <section class="spt-features-overview">
        <div class="spt-section-header">
          <h2 class="spt-section-title">Key Features</h2>
          <p class="spt-section-subtitle">
            Discover the powerful features that make SPT extension essential for secure blockchain development.
          </p>
        </div>

        <div class="spt-features-grid">
          <div class="spt-feature-card spt-feature-primary" *ngFor="let feature of primaryFeatures">
            <div class="spt-feature-icon" [style.background]="feature.color">
              <mat-icon>{{ feature.icon }}</mat-icon>
            </div>
            <div class="spt-feature-content">
              <h3 class="spt-feature-title">{{ feature.title }}</h3>
              <p class="spt-feature-description">{{ feature.description }}</p>
              <div class="spt-feature-benefits" *ngIf="feature.benefits.length > 0">
                <ul class="spt-benefits-list">
                  <li *ngFor="let benefit of feature.benefits" class="spt-benefit-item">
                    <mat-icon class="spt-benefit-icon">check_circle</mat-icon>
                    <span>{{ benefit }}</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Detailed Information Tabs -->
      <section class="spt-details-section">
        <mat-tab-group class="spt-tab-group" animationDuration="300ms">
          <!-- Installation Tab -->
          <mat-tab label="Installation">
            <div class="spt-tab-content">
              <div class="spt-tab-header">
                <h2 class="spt-tab-title">Installation Guide</h2>
                <p class="spt-tab-subtitle">Get the SPT extension installed and configured in VS Code quickly and easily.</p>
              </div>

              <div class="spt-installation-steps">
                <div class="spt-step-card" *ngFor="let step of installationSteps; let i = index">
                  <div class="spt-step-header">
                    <div class="spt-step-number">{{ i + 1 }}</div>
                    <div class="spt-step-info">
                      <h3 class="spt-step-title">{{ step.title }}</h3>
                      <p class="spt-step-subtitle">{{ step.subtitle }}</p>
                    </div>
                  </div>
                  <div class="spt-step-content">
                    <p class="spt-step-description">{{ step.description }}</p>
                    <div class="spt-code-block" *ngIf="step.command">
                      <div class="spt-code-header">
                        <mat-icon>terminal</mat-icon>
                        <span>Command</span>
                        <button mat-icon-button class="spt-copy-btn" (click)="copyToClipboard(step.command)">
                          <mat-icon>content_copy</mat-icon>
                        </button>
                      </div>
                      <pre class="spt-code-content"><code>{{ step.command }}</code></pre>
                    </div>
                    <div class="spt-step-note" *ngIf="step.notes">
                      <mat-icon>info</mat-icon>
                      <span>{{ step.notes }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Configuration Tab -->
          <mat-tab label="Configuration">
            <div class="spt-tab-content">
              <div class="spt-tab-header">
                <h2 class="spt-tab-title">Extension Configuration</h2>
                <p class="spt-tab-subtitle">Customize the SPT extension to fit your development workflow and preferences.</p>
              </div>

              <div class="spt-config-sections">
                <div class="spt-config-card">
                  <div class="spt-config-header">
                    <mat-icon class="spt-config-icon">settings</mat-icon>
                    <div class="spt-config-info">
                      <h3 class="spt-config-title">Settings Overview</h3>
                      <p class="spt-config-subtitle">Configure extension behavior</p>
                    </div>
                  </div>
                  <div class="spt-config-content">
                    <p>Access extension settings through VS Code preferences:</p>
                    <div class="spt-code-block">
                      <div class="spt-code-header">
                        <mat-icon>keyboard</mat-icon>
                        <span>Keyboard Shortcut</span>
                      </div>
                      <pre class="spt-code-content"><code>Ctrl+Shift+P → "Preferences: Open Settings (JSON)"</code></pre>
                    </div>
                  </div>
                </div>

                <div class="spt-config-card">
                  <div class="spt-config-header">
                    <mat-icon class="spt-config-icon">code</mat-icon>
                    <div class="spt-config-info">
                      <h3 class="spt-config-title">Configuration Example</h3>
                      <p class="spt-config-subtitle">settings.json</p>
                    </div>
                  </div>
                  <div class="spt-config-content">
                    <div class="spt-code-block">
                      <div class="spt-code-header">
                        <mat-icon>code</mat-icon>
                        <span>JSON Configuration</span>
                        <button mat-icon-button class="spt-copy-btn" (click)="copyToClipboard(configExample)">
                          <mat-icon>content_copy</mat-icon>
                        </button>
                      </div>
                      <pre class="spt-code-content"><code>{{ configExample }}</code></pre>
                    </div>
                  </div>
                </div>

                <div class="spt-config-card">
                  <div class="spt-config-header">
                    <mat-icon class="spt-config-icon">list</mat-icon>
                    <div class="spt-config-info">
                      <h3 class="spt-config-title">Available Settings</h3>
                      <p class="spt-config-subtitle">Complete settings reference</p>
                    </div>
                  </div>
                  <div class="spt-config-content">
                    <div class="spt-settings-table">
                      <div class="spt-table-header">
                        <div class="spt-table-cell">Setting</div>
                        <div class="spt-table-cell">Type</div>
                        <div class="spt-table-cell">Default</div>
                        <div class="spt-table-cell">Description</div>
                      </div>
                      <div class="spt-table-row" *ngFor="let setting of configSettings">
                        <div class="spt-table-cell">
                          <code class="spt-setting-key">{{ setting.key }}</code>
                        </div>
                        <div class="spt-table-cell">
                          <span class="spt-badge spt-badge-info">{{ setting.type }}</span>
                        </div>
                        <div class="spt-table-cell">
                          <code class="spt-setting-value">{{ setting.default }}</code>
                        </div>
                        <div class="spt-table-cell">
                          <span class="spt-setting-desc">{{ setting.description }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Usage Tab -->
          <mat-tab label="Usage">
            <div class="spt-tab-content">
              <div class="spt-tab-header">
                <h2 class="spt-tab-title">Using the Extension</h2>
                <p class="spt-tab-subtitle">Learn how to effectively use SPT extension features in your daily development workflow.</p>
              </div>

              <div class="spt-usage-sections">
                <div class="spt-usage-card" *ngFor="let usage of usageExamples">
                  <div class="spt-usage-header" (click)="usage.expanded = !usage.expanded">
                    <div class="spt-usage-icon">
                      <mat-icon>{{ usage.icon }}</mat-icon>
                    </div>
                    <div class="spt-usage-info">
                      <h3 class="spt-usage-title">{{ usage.title }}</h3>
                      <p class="spt-usage-description">{{ usage.description }}</p>
                    </div>
                    <mat-icon class="spt-expand-icon" [class.spt-expanded]="usage.expanded">expand_more</mat-icon>
                  </div>

                  <div class="spt-usage-content" [class.spt-expanded]="usage.expanded">
                    <p class="spt-usage-details">{{ usage.details }}</p>
                    <div class="spt-usage-steps" *ngIf="usage.steps">
                      <h4 class="spt-steps-title">Steps:</h4>
                      <ol class="spt-steps-list">
                        <li *ngFor="let step of usage.steps" class="spt-step-item">{{ step }}</li>
                      </ol>
                    </div>
                    <div class="spt-usage-tips" *ngIf="usage.tips">
                      <h4 class="spt-tips-title">Tips:</h4>
                      <ul class="spt-tips-list">
                        <li *ngFor="let tip of usage.tips" class="spt-tip-item">
                          <mat-icon class="spt-tip-icon">lightbulb</mat-icon>
                          <span>{{ tip }}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>

          <!-- Troubleshooting Tab -->
          <mat-tab label="Troubleshooting">
            <div class="spt-tab-content">
              <div class="spt-tab-header">
                <h2 class="spt-tab-title">Troubleshooting</h2>
                <p class="spt-tab-subtitle">Common issues and solutions for the SPT VS Code extension.</p>
              </div>

              <div class="spt-troubleshooting-grid">
                <div class="spt-issue-card" *ngFor="let issue of troubleshootingIssues">
                  <div class="spt-issue-header">
                    <div class="spt-issue-icon" [class]="'spt-severity-' + issue.severity">
                      <mat-icon>{{ issue.icon }}</mat-icon>
                    </div>
                    <div class="spt-issue-info">
                      <h3 class="spt-issue-title">{{ issue.problem }}</h3>
                      <p class="spt-issue-description">{{ issue.description }}</p>
                    </div>
                  </div>
                  <div class="spt-issue-content">
                    <div class="spt-solution-section">
                      <h4 class="spt-solution-title">Solution:</h4>
                      <ol class="spt-solution-steps">
                        <li *ngFor="let step of issue.solution" class="spt-solution-step">{{ step }}</li>
                      </ol>
                    </div>
                    <div class="spt-prevention-section" *ngIf="issue.prevention">
                      <h4 class="spt-prevention-title">Prevention:</h4>
                      <p class="spt-prevention-text">{{ issue.prevention }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </mat-tab>
        </mat-tab-group>
      </section>
    </div>
  `,

  styles: [`
    /* Main Container */
    .spt-vscode-container {
      max-width: 1200px;
      margin: 0 auto;
      font-family: 'Inter', sans-serif;
    }

    /* Hero Section */
    .spt-hero-section {
      background: linear-gradient(135deg, var(--spt-primary-50) 0%, var(--spt-secondary-50) 100%);
      border-radius: var(--spt-radius-3xl);
      padding: var(--spt-space-12) var(--spt-space-8);
      margin-bottom: var(--spt-space-12);
      border: 1px solid var(--spt-primary-200);
    }

    .spt-hero-content {
      display: flex;
      align-items: center;
      gap: var(--spt-space-6);
      margin-bottom: var(--spt-space-8);
    }

    .spt-hero-icon {
      background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);
      border-radius: var(--spt-radius-3xl);
      padding: var(--spt-space-6);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: var(--spt-shadow-xl);
    }

    .spt-hero-icon mat-icon {
      color: white;
      font-size: 48px;
      width: 48px;
      height: 48px;
    }

    .spt-hero-text {
      flex: 1;
    }

    .spt-hero-title {
      font-size: var(--spt-text-5xl);
      font-weight: var(--spt-font-bold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-4) 0;
      letter-spacing: -0.025em;
    }

    .spt-hero-subtitle {
      font-size: var(--spt-text-xl);
      color: var(--spt-gray-600);
      margin: 0;
      line-height: 1.6;
      font-weight: var(--spt-font-normal);
    }

    .spt-hero-actions {
      display: flex;
      gap: var(--spt-space-4);
      align-items: center;
    }

    .spt-primary-btn {
      background: var(--spt-primary-600) !important;
      color: white !important;
      font-weight: var(--spt-font-semibold) !important;
      padding: var(--spt-space-4) var(--spt-space-6) !important;
      border-radius: var(--spt-radius-xl) !important;
      box-shadow: var(--spt-shadow-md) !important;
      transition: all 0.2s ease !important;
    }

    .spt-primary-btn:hover {
      background: var(--spt-primary-700) !important;
      box-shadow: var(--spt-shadow-lg) !important;
      transform: translateY(-2px);
    }

    .spt-secondary-btn {
      color: var(--spt-gray-700) !important;
      border-color: var(--spt-gray-300) !important;
      font-weight: var(--spt-font-medium) !important;
      padding: var(--spt-space-4) var(--spt-space-6) !important;
      border-radius: var(--spt-radius-xl) !important;
      transition: all 0.2s ease !important;
    }

    .spt-secondary-btn:hover {
      background: var(--spt-gray-100) !important;
      border-color: var(--spt-gray-400) !important;
      transform: translateY(-1px);
    }

    /* Section Headers */
    .spt-section-header {
      text-align: center;
      margin-bottom: var(--spt-space-12);
    }

    .spt-section-title {
      font-size: var(--spt-text-4xl);
      font-weight: var(--spt-font-bold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-4) 0;
      letter-spacing: -0.025em;
    }

    .spt-section-subtitle {
      font-size: var(--spt-text-lg);
      color: var(--spt-gray-600);
      margin: 0;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;
    }

    /* Features Overview */
    .spt-features-overview {
      margin-bottom: var(--spt-space-16);
    }

    .spt-features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spt-space-8);
    }

    .spt-feature-card {
      background: white;
      border-radius: var(--spt-radius-3xl);
      padding: var(--spt-space-8);
      border: 1px solid var(--spt-gray-200);
      box-shadow: var(--spt-shadow-sm);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .spt-feature-card:hover {
      box-shadow: var(--spt-shadow-xl);
      transform: translateY(-4px);
      border-color: var(--spt-primary-300);
    }

    .spt-feature-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--spt-primary-500), var(--spt-secondary-500));
    }

    .spt-feature-icon {
      width: 64px;
      height: 64px;
      border-radius: var(--spt-radius-2xl);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: var(--spt-space-6);
      box-shadow: var(--spt-shadow-md);
    }

    .spt-feature-icon mat-icon {
      color: white;
      font-size: 32px;
      width: 32px;
      height: 32px;
    }

    .spt-feature-content {
      flex: 1;
    }

    .spt-feature-title {
      font-size: var(--spt-text-2xl);
      font-weight: var(--spt-font-bold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-3) 0;
    }

    .spt-feature-description {
      font-size: var(--spt-text-base);
      color: var(--spt-gray-600);
      margin: 0 0 var(--spt-space-6) 0;
      line-height: 1.6;
    }
    .spt-feature-benefits {
      margin-top: var(--spt-space-4);
    }

    .spt-benefits-list {
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .spt-benefit-item {
      display: flex;
      align-items: center;
      gap: var(--spt-space-2);
      margin-bottom: var(--spt-space-2);
      font-size: var(--spt-text-sm);
      color: var(--spt-gray-700);
    }

    .spt-benefit-icon {
      color: var(--spt-success-600);
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    /* Details Section */
    .spt-details-section {
      margin-bottom: var(--spt-space-16);
    }

    .spt-tab-group {
      background: white;
      border-radius: var(--spt-radius-2xl);
      box-shadow: var(--spt-shadow-lg);
      overflow: hidden;
      border: 1px solid var(--spt-gray-200);
    }

    .spt-tab-content {
      padding: var(--spt-space-8);
    }

    .spt-tab-header {
      text-align: center;
      margin-bottom: var(--spt-space-8);
    }

    .spt-tab-title {
      font-size: var(--spt-text-3xl);
      font-weight: var(--spt-font-bold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-3) 0;
    }

    .spt-tab-subtitle {
      font-size: var(--spt-text-lg);
      color: var(--spt-gray-600);
      margin: 0;
      line-height: 1.6;
    }

    /* Installation Steps */
    .spt-installation-steps {
      display: flex;
      flex-direction: column;
      gap: var(--spt-space-6);
    }

    .spt-step-card {
      background: var(--spt-gray-50);
      border-radius: var(--spt-radius-2xl);
      padding: var(--spt-space-6);
      border: 1px solid var(--spt-gray-200);
      transition: all 0.2s ease;
    }

    .spt-step-card:hover {
      box-shadow: var(--spt-shadow-md);
      border-color: var(--spt-primary-300);
    }

    .spt-step-header {
      display: flex;
      align-items: center;
      gap: var(--spt-space-4);
      margin-bottom: var(--spt-space-4);
    }

    .spt-step-number {
      background: linear-gradient(135deg, var(--spt-primary-600), var(--spt-primary-700));
      color: white;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: var(--spt-font-bold);
      font-size: var(--spt-text-lg);
      box-shadow: var(--spt-shadow-md);
    }

    .spt-step-info {
      flex: 1;
    }

    .spt-step-title {
      font-size: var(--spt-text-xl);
      font-weight: var(--spt-font-semibold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-1) 0;
    }

    .spt-step-subtitle {
      font-size: var(--spt-text-sm);
      color: var(--spt-gray-600);
      margin: 0;
      font-weight: var(--spt-font-medium);
    }

    .spt-step-content {
      margin-left: 64px;
    }

    .spt-step-description {
      font-size: var(--spt-text-base);
      color: var(--spt-gray-700);
      margin: 0 0 var(--spt-space-4) 0;
      line-height: 1.6;
    }

    /* Code Blocks - Pastel Theme */
    .spt-code-block {
      background: var(--spt-gray-50);
      border: 1px solid var(--spt-gray-200);
      border-radius: var(--spt-radius-xl);
      overflow: hidden;
      margin: var(--spt-space-4) 0;
      box-shadow: var(--spt-shadow-sm);
    }

    .spt-code-header {
      background: var(--spt-primary-50);
      padding: var(--spt-space-3) var(--spt-space-4);
      display: flex;
      align-items: center;
      gap: var(--spt-space-2);
      border-bottom: 1px solid var(--spt-primary-200);
      justify-content: space-between;
    }

    .spt-code-header mat-icon {
      color: var(--spt-primary-600);
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .spt-code-header span {
      color: var(--spt-primary-700);
      font-size: var(--spt-text-sm);
      font-weight: var(--spt-font-medium);
    }

    .spt-copy-btn {
      color: var(--spt-primary-600) !important;
      transition: all 0.2s ease !important;
      background: var(--spt-primary-100) !important;
      border-radius: var(--spt-radius-md) !important;
    }

    .spt-copy-btn:hover {
      color: var(--spt-primary-700) !important;
      background: var(--spt-primary-200) !important;
    }

    .spt-code-content {
      margin: 0;
      padding: var(--spt-space-4);
      background: white;
      color: var(--spt-gray-800);
      font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
      font-size: var(--spt-text-sm);
      line-height: 1.6;
      overflow-x: auto;
      border: 1px solid var(--spt-gray-100);
      border-top: none;
    }

    .spt-step-note {
      display: flex;
      align-items: center;
      gap: var(--spt-space-2);
      margin-top: var(--spt-space-3);
      padding: var(--spt-space-3);
      background: var(--spt-info-50);
      border-radius: var(--spt-radius-lg);
      color: var(--spt-info-700);
      border: 1px solid var(--spt-info-200);
    }

    .spt-step-note mat-icon {
      color: var(--spt-info-600);
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    /* Configuration Sections */
    .spt-config-sections {
      display: flex;
      flex-direction: column;
      gap: var(--spt-space-8);
    }

    .spt-config-card {
      background: var(--spt-gray-50);
      border-radius: var(--spt-radius-2xl);
      padding: var(--spt-space-6);
      border: 1px solid var(--spt-gray-200);
    }

    .spt-config-header {
      display: flex;
      align-items: center;
      gap: var(--spt-space-4);
      margin-bottom: var(--spt-space-4);
    }

    .spt-config-icon {
      background: var(--spt-primary-600);
      color: white;
      border-radius: var(--spt-radius-xl);
      padding: var(--spt-space-3);
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .spt-config-info {
      flex: 1;
    }

    .spt-config-title {
      font-size: var(--spt-text-xl);
      font-weight: var(--spt-font-semibold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-1) 0;
    }

    .spt-config-subtitle {
      font-size: var(--spt-text-sm);
      color: var(--spt-gray-600);
      margin: 0;
    }

    .spt-config-content {
      margin-left: 56px;
    }

    /* Settings Table */
    .spt-settings-table {
      background: white;
      border-radius: var(--spt-radius-xl);
      overflow: hidden;
      border: 1px solid var(--spt-gray-200);
    }

    .spt-table-header {
      display: grid;
      grid-template-columns: 2fr 1fr 1.5fr 3fr;
      gap: var(--spt-space-4);
      padding: var(--spt-space-4);
      background: var(--spt-gray-100);
      font-weight: var(--spt-font-semibold);
      color: var(--spt-gray-700);
      font-size: var(--spt-text-sm);
    }

    .spt-table-row {
      display: grid;
      grid-template-columns: 2fr 1fr 1.5fr 3fr;
      gap: var(--spt-space-4);
      padding: var(--spt-space-4);
      border-top: 1px solid var(--spt-gray-200);
      transition: background 0.2s ease;
    }

    .spt-table-row:hover {
      background: var(--spt-gray-50);
    }

    .spt-table-cell {
      display: flex;
      align-items: center;
    }

    .spt-setting-key {
      background: var(--spt-gray-100);
      padding: var(--spt-space-1) var(--spt-space-2);
      border-radius: var(--spt-radius-md);
      font-family: 'JetBrains Mono', monospace;
      font-size: var(--spt-text-xs);
      color: var(--spt-gray-800);
    }

    .spt-setting-value {
      background: var(--spt-success-100);
      padding: var(--spt-space-1) var(--spt-space-2);
      border-radius: var(--spt-radius-md);
      font-family: 'JetBrains Mono', monospace;
      font-size: var(--spt-text-xs);
      color: var(--spt-success-800);
    }

    .spt-setting-desc {
      font-size: var(--spt-text-sm);
      color: var(--spt-gray-600);
      line-height: 1.4;
    }

    /* Usage Sections */
    .spt-usage-sections {
      display: flex;
      flex-direction: column;
      gap: var(--spt-space-4);
    }

    .spt-usage-card {
      background: white;
      border-radius: var(--spt-radius-2xl);
      border: 1px solid var(--spt-gray-200);
      overflow: hidden;
      transition: all 0.2s ease;
    }

    .spt-usage-card:hover {
      box-shadow: var(--spt-shadow-md);
      border-color: var(--spt-primary-300);
    }

    .spt-usage-header {
      display: flex;
      align-items: center;
      gap: var(--spt-space-4);
      padding: var(--spt-space-6);
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .spt-usage-header:hover {
      background: var(--spt-gray-50);
    }

    .spt-usage-icon {
      background: var(--spt-primary-100);
      color: var(--spt-primary-600);
      border-radius: var(--spt-radius-xl);
      padding: var(--spt-space-3);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .spt-usage-icon mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .spt-usage-info {
      flex: 1;
    }

    .spt-usage-title {
      font-size: var(--spt-text-xl);
      font-weight: var(--spt-font-semibold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-1) 0;
    }

    .spt-usage-description {
      font-size: var(--spt-text-sm);
      color: var(--spt-gray-600);
      margin: 0;
    }

    .spt-expand-icon {
      color: var(--spt-gray-400);
      transition: transform 0.2s ease;
    }

    .spt-expand-icon.spt-expanded {
      transform: rotate(180deg);
    }

    .spt-usage-content {
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .spt-usage-content.spt-expanded {
      max-height: 1000px;
    }

    .spt-usage-details {
      padding: 0 var(--spt-space-6) var(--spt-space-4);
      color: var(--spt-gray-700);
      line-height: 1.6;
    }

    .spt-usage-steps,
    .spt-usage-tips {
      padding: 0 var(--spt-space-6) var(--spt-space-4);
    }

    .spt-steps-title,
    .spt-tips-title {
      font-size: var(--spt-text-base);
      font-weight: var(--spt-font-semibold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-3) 0;
    }

    .spt-steps-list {
      margin: 0;
      padding-left: var(--spt-space-5);
      color: var(--spt-gray-700);
    }

    .spt-step-item {
      margin-bottom: var(--spt-space-2);
      line-height: 1.5;
    }

    .spt-tips-list {
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .spt-tip-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spt-space-2);
      margin-bottom: var(--spt-space-3);
      padding: var(--spt-space-3);
      background: var(--spt-warning-50);
      border-radius: var(--spt-radius-lg);
      border: 1px solid var(--spt-warning-200);
    }

    .spt-tip-icon {
      color: var(--spt-warning-600);
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin-top: 2px;
    }
    /* Troubleshooting Grid */
    .spt-troubleshooting-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spt-space-6);
    }

    .spt-issue-card {
      background: white;
      border-radius: var(--spt-radius-2xl);
      padding: var(--spt-space-6);
      border: 1px solid var(--spt-gray-200);
      box-shadow: var(--spt-shadow-sm);
      transition: all 0.2s ease;
    }

    .spt-issue-card:hover {
      box-shadow: var(--spt-shadow-md);
      transform: translateY(-2px);
    }

    .spt-issue-header {
      display: flex;
      align-items: center;
      gap: var(--spt-space-4);
      margin-bottom: var(--spt-space-4);
    }

    .spt-issue-icon {
      border-radius: var(--spt-radius-xl);
      padding: var(--spt-space-3);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .spt-issue-icon.spt-severity-high {
      background: var(--spt-error-100);
      color: var(--spt-error-600);
    }

    .spt-issue-icon.spt-severity-medium {
      background: var(--spt-warning-100);
      color: var(--spt-warning-600);
    }

    .spt-issue-icon mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .spt-issue-info {
      flex: 1;
    }

    .spt-issue-title {
      font-size: var(--spt-text-lg);
      font-weight: var(--spt-font-semibold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-1) 0;
    }

    .spt-issue-description {
      font-size: var(--spt-text-sm);
      color: var(--spt-gray-600);
      margin: 0;
    }

    .spt-solution-section,
    .spt-prevention-section {
      margin-bottom: var(--spt-space-4);
    }

    .spt-solution-title,
    .spt-prevention-title {
      font-size: var(--spt-text-base);
      font-weight: var(--spt-font-semibold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-2) 0;
    }

    .spt-solution-steps {
      margin: 0;
      padding-left: var(--spt-space-5);
      color: var(--spt-gray-700);
    }

    .spt-solution-step {
      margin-bottom: var(--spt-space-2);
      line-height: 1.5;
    }

    .spt-prevention-text {
      margin: 0;
      color: var(--spt-gray-700);
      line-height: 1.6;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
      .spt-features-grid {
        grid-template-columns: 1fr;
      }

      .spt-hero-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spt-space-4);
      }

      .spt-hero-actions {
        justify-content: center;
      }
    }

    @media (max-width: 768px) {
      .spt-hero-section {
        padding: var(--spt-space-8) var(--spt-space-4);
      }

      .spt-hero-title {
        font-size: var(--spt-text-4xl);
      }

      .spt-hero-subtitle {
        font-size: var(--spt-text-lg);
      }

      .spt-hero-actions {
        flex-direction: column;
        width: 100%;
      }

      .spt-primary-btn,
      .spt-secondary-btn {
        width: 100%;
        justify-content: center;
      }

      .spt-troubleshooting-grid {
        grid-template-columns: 1fr;
      }

      .spt-table-header,
      .spt-table-row {
        grid-template-columns: 1fr;
        gap: var(--spt-space-2);
      }

      .spt-step-content {
        margin-left: 0;
      }

      .spt-config-content {
        margin-left: 0;
      }
    }

    @media (max-width: 480px) {
      .spt-tab-content {
        padding: var(--spt-space-4);
      }

      .spt-hero-icon {
        padding: var(--spt-space-4);
      }

      .spt-hero-icon mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }

      .spt-feature-card {
        padding: var(--spt-space-4);
      }
    }
  `]
})
export class VscodeExtensionComponent {
  configExample = `{
  "spt.enabled": true,
  "spt.serverUrl": "http://localhost:8080",
  "spt.apiKey": "your-api-key-here",
  "spt.autoScan": true,
  "spt.scanOnOpen": false,
  "spt.chains": ["ethereum", "bitcoin", "general"],
  "spt.severity": "medium",
  "spt.showInlineDecorations": true,
  "spt.showProblems": true,
  "spt.enableCodeLens": true,
  "spt.enableHover": true
}`;

  primaryFeatures: ExtensionFeature[] = [
    {
      title: 'Real-time Security Scanning',
      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities and smart suggestions.',
      icon: 'security',
      color: 'linear-gradient(135deg, var(--spt-primary-500), var(--spt-primary-600))',
      benefits: [
        'Immediate vulnerability detection',
        'Reduced security debt',
        'Faster development cycles',
        'Proactive security measures'
      ]
    },
    {
      title: 'Inline Decorations',
      description: 'Visual indicators directly in your code highlighting security issues with severity-based color coding.',
      icon: 'visibility',
      color: 'linear-gradient(135deg, var(--spt-success-500), var(--spt-success-600))',
      benefits: [
        'Clear visual feedback',
        'Context-aware highlighting',
        'Severity-based color coding',
        'Non-intrusive indicators'
      ]
    },
    {
      title: 'Problems Panel Integration',
      description: 'Security issues appear in VS Code\'s Problems panel with detailed descriptions and quick fixes.',
      icon: 'bug_report',
      color: 'linear-gradient(135deg, var(--spt-error-500), var(--spt-error-600))',
      benefits: [
        'Centralized issue tracking',
        'Detailed error descriptions',
        'Quick navigation to issues',
        'Integration with existing workflow'
      ]
    },
    {
      title: 'CodeLens Integration',
      description: 'Actionable security suggestions and metrics displayed directly above your code for instant access.',
      icon: 'lens',
      color: 'linear-gradient(135deg, var(--spt-warning-500), var(--spt-warning-600))',
      benefits: [
        'Contextual security metrics',
        'One-click security actions',
        'Code quality insights',
        'Performance recommendations'
      ]
    },
    {
      title: 'Hover Information',
      description: 'Detailed security information and recommendations on hover over code elements with rich documentation.',
      icon: 'info',
      color: 'linear-gradient(135deg, var(--spt-info-500), var(--spt-info-600))',
      benefits: [
        'Instant security documentation',
        'Best practice suggestions',
        'Vulnerability explanations',
        'Quick reference access'
      ]
    },
    {
      title: 'Multi-chain Support',
      description: 'Comprehensive support for Ethereum, Bitcoin, and general blockchain security patterns.',
      icon: 'link',
      color: 'linear-gradient(135deg, var(--spt-secondary-500), var(--spt-secondary-600))',
      benefits: [
        'Comprehensive blockchain coverage',
        'Chain-specific security rules',
        'Unified security approach',
        'Extensible architecture'
      ]
    }
  ];

  installationSteps = [
    {
      title: 'Install from VS Code Marketplace',
      subtitle: 'Recommended method',
      description: 'Search for "SPT Security" in the VS Code Extensions marketplace and install.',
      command: 'ext install blockchain-spt.vscode-spt',
      notes: 'Extension will be automatically activated after installation'
    },
    {
      title: 'Configure Backend Connection',
      subtitle: 'Connect to SPT server',
      description: 'Configure the extension to connect to your SPT backend server.',
      command: 'Configure spt.serverUrl in VS Code settings',
      notes: 'Default server URL is http://localhost:8080'
    },
    {
      title: 'Verify Installation',
      subtitle: 'Test the connection',
      description: 'Open a blockchain project and verify that security scanning is working.',
      notes: 'Check the status bar for SPT connection indicator'
    }
  ];

  configSettings: ConfigSetting[] = [
    { key: 'spt.enabled', type: 'boolean', default: 'true', description: 'Enable/disable SPT security analysis' },
    { key: 'spt.serverUrl', type: 'string', default: 'http://localhost:8080', description: 'SPT backend server URL' },
    { key: 'spt.apiKey', type: 'string', default: '""', description: 'API key for authentication' },
    { key: 'spt.autoScan', type: 'boolean', default: 'true', description: 'Automatically scan files on save' },
    { key: 'spt.scanOnOpen', type: 'boolean', default: 'false', description: 'Automatically scan files when opened' },
    { key: 'spt.chains', type: 'array', default: '["ethereum", "bitcoin", "general"]', description: 'Blockchain chains to analyze' },
    { key: 'spt.severity', type: 'string', default: '"medium"', description: 'Minimum severity level to show' },
    { key: 'spt.showInlineDecorations', type: 'boolean', default: 'true', description: 'Show inline security decorations' },
    { key: 'spt.showProblems', type: 'boolean', default: 'true', description: 'Show security issues in Problems panel' },
    { key: 'spt.enableCodeLens', type: 'boolean', default: 'true', description: 'Enable security-related CodeLens' },
    { key: 'spt.enableHover', type: 'boolean', default: 'true', description: 'Enable security information on hover' }
  ];

  usageExamples = [
    {
      title: 'Scanning Files',
      description: 'How to scan files for security issues',
      icon: 'scanner',
      expanded: true,
      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',
      steps: [
        'Open a blockchain project in VS Code',
        'Save a file to trigger automatic scanning',
        'Or use Ctrl+Shift+P → "SPT: Scan Current File"',
        'View results in Problems panel or inline decorations'
      ],
      tips: [
        'Enable auto-scan for continuous security monitoring',
        'Use the Problems panel to navigate between issues',
        'Check the status bar for scan progress'
      ]
    },
    {
      title: 'Understanding Security Issues',
      description: 'How to interpret and resolve security findings',
      icon: 'help',
      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',
      steps: [
        'Hover over highlighted code to see issue details',
        'Click on issues in Problems panel for more information',
        'Use CodeLens actions for quick fixes',
        'Follow the recommended solutions'
      ],
      tips: [
        'Start with critical and high severity issues',
        'Use hover information for quick context',
        'Check references for additional learning'
      ]
    },
    {
      title: 'Configuring Scan Settings',
      description: 'Customize scanning behavior for your project',
      icon: 'tune',
      details: 'Adjust scan settings to match your project requirements and development workflow.',
      steps: [
        'Open VS Code settings (Ctrl+,)',
        'Search for "SPT" to find extension settings',
        'Adjust chains, severity, and scan triggers',
        'Save settings and restart if needed'
      ],
      tips: [
        'Use workspace settings for project-specific configuration',
        'Adjust severity threshold based on project maturity',
        'Enable scan-on-open for comprehensive coverage'
      ]
    }
  ];

  troubleshootingIssues = [
    {
      problem: 'Extension Not Connecting to Server',
      description: 'SPT extension cannot connect to the backend server',
      icon: 'cloud_off',
      severity: 'high',
      solution: [
        'Verify SPT backend server is running on configured port',
        'Check spt.serverUrl setting in VS Code preferences',
        'Ensure firewall is not blocking the connection',
        'Try restarting VS Code and the SPT server'
      ],
      prevention: 'Always start the SPT backend server before using the extension'
    },
    {
      problem: 'No Security Issues Detected',
      description: 'Extension is running but not finding any security issues',
      icon: 'search_off',
      severity: 'medium',
      solution: [
        'Check if the file type is supported (Solidity, JavaScript, etc.)',
        'Verify the correct blockchain chains are selected',
        'Lower the severity threshold in settings',
        'Ensure the file contains actual security-relevant code'
      ],
      prevention: 'Review supported file types and ensure proper project structure'
    },
    {
      problem: 'Performance Issues',
      description: 'Extension is causing VS Code to slow down',
      icon: 'speed',
      severity: 'medium',
      solution: [
        'Disable auto-scan and use manual scanning',
        'Increase scan timeout in settings',
        'Exclude large files or directories from scanning',
        'Reduce the number of enabled blockchain chains'
      ],
      prevention: 'Configure appropriate scan settings for your project size'
    }
  ];

  copyToClipboard(text: string): void {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here
      console.log('Copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy: ', err);
    });
  }
}
