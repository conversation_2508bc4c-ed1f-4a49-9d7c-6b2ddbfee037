{"SafeMath Import": {"prefix": "spt-safemath", "body": ["import \"@openzeppelin/contracts/utils/math/SafeMath.sol\";", "", "using SafeMath for uint256;"], "description": "Import and use SafeMath library for secure arithmetic operations"}, "Reentrancy Guard": {"prefix": "spt-reentrancy", "body": ["import \"@openzeppelin/contracts/security/ReentrancyGuard.sol\";", "", "contract ${1:ContractName} is ReentrancyGuard {", "    function ${2:functionName}() external nonReentrant {", "        ${3:// Your code here}", "    }", "}"], "description": "Add reentrancy protection to contract functions"}, "Access Control": {"prefix": "spt-access", "body": ["import \"@openzeppelin/contracts/access/Ownable.sol\";", "", "contract ${1:ContractName} is Ownable {", "    modifier onlyAuthorized() {", "        require(msg.sender == owner() || ${2:condition}, \"Unauthorized\");", "        _;", "    }", "", "    function ${3:functionName}() external onlyAuthorized {", "        ${4:// Your code here}", "    }", "}"], "description": "Add access control to contract functions"}, "Safe Transfer": {"prefix": "spt-transfer", "body": ["function safeTransfer(address payable _to, uint256 _amount) internal {", "    require(_to != address(0), \"Invalid address\");", "    require(address(this).balance >= _amount, \"Insufficient balance\");", "    ", "    (bool success, ) = _to.call{value: _amount}(\"\");", "    require(success, \"Transfer failed\");", "}"], "description": "Safe Ether transfer function with proper checks"}, "Input Validation": {"prefix": "spt-validate", "body": ["require(${1:condition}, \"${2:Error message}\");", "require(${3:address} != address(0), \"Invalid address\");", "require(${4:amount} > 0, \"Amount must be positive\");"], "description": "Common input validation patterns"}, "Event Logging": {"prefix": "spt-event", "body": ["event ${1:EventName}(${2:parameters});", "", "function ${3:functionName}() external {", "    ${4:// Function logic}", "    emit ${1:EventName}(${5:arguments});", "}"], "description": "Add event logging for transparency"}, "Pausable Contract": {"prefix": "spt-pausable", "body": ["import \"@openzeppelin/contracts/security/Pausable.sol\";", "import \"@openzeppelin/contracts/access/Ownable.sol\";", "", "contract ${1:ContractName} is Pausable, Ownable {", "    function pause() external onlyOwner {", "        _pause();", "    }", "", "    function unpause() external onlyOwner {", "        _unpause();", "    }", "", "    function ${2:functionName}() external whenNotPaused {", "        ${3:// Your code here}", "    }", "}"], "description": "Add pause functionality for emergency stops"}, "Gas Optimization": {"prefix": "spt-gas", "body": ["// Gas optimization: Use uint256 instead of smaller uints", "uint256 private ${1:variableName};", "", "// Gas optimization: Pack structs efficiently", "struct ${2:<PERSON>ruct<PERSON>ame} {", "    uint128 ${3:field1}; // 16 bytes", "    uint128 ${4:field2}; // 16 bytes - fits in one slot", "    address ${5:field3}; // 20 bytes - new slot", "    uint96 ${6:field4};  // 12 bytes - fits with address", "}", "", "// Gas optimization: Use immutable for constants set in constructor", "address public immutable ${7:constantAddress};"], "description": "Gas optimization patterns and best practices"}, "Oracle Security": {"prefix": "spt-oracle", "body": ["import \"@chainlink/contracts/src/v0.8/interfaces/AggregatorV3Interface.sol\";", "", "contract ${1:ContractName} {", "    AggregatorV3Interface internal priceFeed;", "    uint256 private constant PRICE_STALENESS_THRESHOLD = 3600; // 1 hour", "", "    constructor() {", "        priceFeed = AggregatorV3Interface(${2:oracleAddress});", "    }", "", "    function getLatestPrice() public view returns (int256, uint256) {", "        (uint80 roundId, int256 price, , uint256 updatedAt, uint80 answeredInRound) = priceFeed.latestRoundData();", "        ", "        require(price > 0, \"Invalid price\");", "        require(updatedAt > 0, \"Round not complete\");", "        require(block.timestamp - updatedAt <= PRICE_STALENESS_THRESHOLD, \"Price data stale\");", "        require(answeredInRound >= roundId, \"Stale price\");", "        ", "        return (price, updatedAt);", "    }", "}"], "description": "Secure oracle price feed integration with staleness checks"}, "Multi-signature Wallet": {"prefix": "spt-multisig", "body": ["contract MultiSigWallet {", "    address[] public owners;", "    uint256 public required;", "    ", "    mapping(address => bool) public isOwner;", "    mapping(uint256 => mapping(address => bool)) public confirmations;", "    ", "    struct Transaction {", "        address to;", "        uint256 value;", "        bytes data;", "        bool executed;", "    }", "    ", "    Transaction[] public transactions;", "    ", "    modifier onlyOwner() {", "        require(isOwner[msg.sender], \"Not an owner\");", "        _;", "    }", "    ", "    modifier notExecuted(uint256 _txIndex) {", "        require(!transactions[_txIndex].executed, \"Transaction already executed\");", "        _;", "    }", "    ", "    function confirmTransaction(uint256 _txIndex) external onlyOwner notExecuted(_txIndex) {", "        confirmations[_txIndex][msg.sender] = true;", "    }", "}"], "description": "Multi-signature wallet implementation template"}}