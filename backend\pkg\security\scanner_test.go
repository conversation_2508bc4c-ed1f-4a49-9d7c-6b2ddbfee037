package security

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"blockchain-spt/backend/pkg/config"

	"github.com/stretchr/testify/assert"
)

func TestNewScanner(t *testing.T) {
	cfg := &config.Config{
		Scanning: config.ScanningConfig{
			FileTypes: []string{".js", ".ts", ".py"},
		},
	}

	scanner, err := NewScanner(cfg)
	assert.NoError(t, err)
	assert.NotNil(t, scanner)
	assert.Equal(t, cfg, scanner.config)
}

func TestScanContent(t *testing.T) {
	cfg := &config.Config{
		Scanning: config.ScanningConfig{
			FileTypes: []string{".js", ".ts", ".py"},
		},
	}

	scanner, err := NewScanner(cfg)
	assert.NoError(t, err)

	tests := []struct {
		name         string
		content      string
		filePath     string
		expectIssues bool
	}{
		{
			name:         "hardcoded password",
			content:      `const password = "secret123";`,
			filePath:     "test.js",
			expectIssues: true,
		},
		{
			name:         "hardcoded API key",
			content:      `const apiKey = "sk_test_123456789";`,
			filePath:     "test.js",
			expectIssues: true,
		},

		{
			name:         "eval usage",
			content:      `eval(userInput);`,
			filePath:     "test.js",
			expectIssues: true,
		},
		{
			name:         "clean code",
			content:      `const message = "Hello, world!";`,
			filePath:     "test.js",
			expectIssues: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create a temporary file with the test content
			tempFile, err := os.CreateTemp("", "test_*.js")
			assert.NoError(t, err)
			defer os.Remove(tempFile.Name())

			_, err = tempFile.WriteString(tt.content)
			assert.NoError(t, err)
			tempFile.Close()

			issues, err := scanner.ScanFile(context.Background(), tempFile.Name())
			assert.NoError(t, err)

			if tt.expectIssues {
				assert.Greater(t, len(issues), 0, "Expected to find security issues")
			} else {
				assert.Equal(t, 0, len(issues), "Expected no security issues")
			}
		})
	}
}

func TestFindRelevantFiles(t *testing.T) {
	cfg := &config.Config{
		Scanning: config.ScanningConfig{
			FileTypes: []string{"*.js", "*.py", "*.sol", "*.json"},
			Paths: config.PathsConfig{
				Exclude: []string{"node_modules", ".git"},
			},
		},
	}
	scanner, err := NewScanner(cfg)
	assert.NoError(t, err)

	// Create a temporary directory structure
	tempDir, err := os.MkdirTemp("", "scanner_test")
	assert.NoError(t, err)
	defer os.RemoveAll(tempDir)

	// Create test files
	testFiles := []string{
		"app.js",
		"config.json",
		"script.py",
		"style.css",
		"README.md",
		"node_modules/package.js", // Should be ignored
		".git/config",             // Should be ignored
		"test.sol",
	}

	for _, file := range testFiles {
		filePath := filepath.Join(tempDir, file)
		err := os.MkdirAll(filepath.Dir(filePath), 0755)
		assert.NoError(t, err)

		err = os.WriteFile(filePath, []byte("test content"), 0644)
		assert.NoError(t, err)
	}

	files, err := scanner.findRelevantFiles(tempDir)
	assert.NoError(t, err)

	// Debug: print found files
	t.Logf("Found files: %v", files)

	// Should find some files but exclude node_modules and .git
	assert.Greater(t, len(files), 0, "Should find at least some files")

	// Check that node_modules and .git files are excluded
	for _, file := range files {
		assert.NotContains(t, file, "node_modules")
		assert.NotContains(t, file, ".git")
	}
}
