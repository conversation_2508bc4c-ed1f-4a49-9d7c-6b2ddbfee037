package commands

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/dependencies"
	"blockchain-spt/backend/pkg/models"

	"github.com/spf13/cobra"
)

// CheckOptions represents check command options
type CheckOptions struct {
	Path    string
	Quick   bool
	Fix     bool
	Verbose bool
}

// NewCheckCommand creates the check command
func NewCheckCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "check",
		Short: "Quick security checks",
		Long: `Perform quick security checks on specific components.

The check command provides fast, targeted security analysis for:
- Dependencies and packages
- Environment configurations
- Keys and secrets
- CI/CD configurations`,
	}

	// Add subcommands
	cmd.AddCommand(newCheckDepsCommand())
	cmd.AddCommand(newCheckEnvCommand())
	cmd.AddCommand(newCheckKeysCommand())
	cmd.AddCommand(newCheckCICDCommand())

	return cmd
}

// newCheckDepsCommand creates the deps check subcommand
func newCheckDepsCommand() *cobra.Command {
	opts := &CheckOptions{}

	cmd := &cobra.Command{
		Use:   "deps [path]",
		Short: "Check dependencies for vulnerabilities",
		Long: `Check project dependencies for known security vulnerabilities.

This command analyzes:
- Package vulnerabilities in npm, pip, cargo, etc.
- Outdated packages with security fixes
- Insecure package sources
- Dependency configuration issues

Examples:
  spt check deps                              # Check current directory
  spt check deps ./package.json              # Check specific file
  spt check deps --quick ./                  # Quick vulnerability check
  spt check deps --fix ./                    # Show fix suggestions`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			return runCheckDeps(cmd, opts)
		},
	}

	addCheckFlags(cmd, opts)
	return cmd
}

// newCheckEnvCommand creates the env check subcommand
func newCheckEnvCommand() *cobra.Command {
	opts := &CheckOptions{}

	cmd := &cobra.Command{
		Use:   "env [path]",
		Short: "Check environment configurations",
		Long: `Check environment configurations for security issues.

This command analyzes:
- Environment variable security
- Configuration file security
- Docker and container security
- Kubernetes manifest security

Examples:
  spt check env                               # Check current directory
  spt check env .env                         # Check specific env file
  spt check env --quick ./config            # Quick config check`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			return runCheckEnv(cmd, opts)
		},
	}

	addCheckFlags(cmd, opts)
	return cmd
}

// newCheckKeysCommand creates the keys check subcommand
func newCheckKeysCommand() *cobra.Command {
	opts := &CheckOptions{}

	cmd := &cobra.Command{
		Use:   "keys [path]",
		Short: "Check for exposed keys and secrets",
		Long: `Check for exposed private keys, API keys, and other secrets.

This command searches for:
- Blockchain private keys (Ethereum, Bitcoin)
- API keys and tokens
- Database credentials
- SSH keys and certificates
- Other sensitive information

Examples:
  spt check keys                              # Check current directory
  spt check keys ./src                       # Check specific directory
  spt check keys --quick ./                  # Quick secret scan`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			return runCheckKeys(cmd, opts)
		},
	}

	addCheckFlags(cmd, opts)
	return cmd
}

// newCheckCICDCommand creates the cicd check subcommand
func newCheckCICDCommand() *cobra.Command {
	opts := &CheckOptions{}

	cmd := &cobra.Command{
		Use:   "cicd [path]",
		Short: "Check CI/CD configurations",
		Long: `Check CI/CD pipeline configurations for security issues.

This command analyzes:
- GitHub Actions workflows
- GitLab CI configurations
- Jenkins pipelines
- Other CI/CD platforms

Examples:
  spt check cicd                              # Check current directory
  spt check cicd .github/workflows          # Check GitHub Actions
  spt check cicd --quick ./                  # Quick CI/CD check`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Path = args[0]
			} else {
				var err error
				opts.Path, err = os.Getwd()
				if err != nil {
					return fmt.Errorf("failed to get current directory: %w", err)
				}
			}
			return runCheckCICD(cmd, opts)
		},
	}

	addCheckFlags(cmd, opts)
	return cmd
}

// addCheckFlags adds common check flags to a command
func addCheckFlags(cmd *cobra.Command, opts *CheckOptions) {
	cmd.Flags().BoolVar(&opts.Quick, "quick", false, "Quick check (faster, less comprehensive)")
	cmd.Flags().BoolVar(&opts.Fix, "fix", false, "Show fix suggestions")
	cmd.Flags().BoolVarP(&opts.Verbose, "verbose", "v", false, "Verbose output")
}

// runCheckDeps executes dependency checking
func runCheckDeps(cmd *cobra.Command, opts *CheckOptions) error {
	fmt.Printf("🔍 Checking dependencies for vulnerabilities...\n")

	cfg, err := loadConfig(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	scanner, err := dependencies.NewScanner(cfg)
	if err != nil {
		return fmt.Errorf("failed to create dependencies scanner: %w", err)
	}

	startTime := time.Now()
	ctx := context.Background()

	var issues []models.SecurityIssue

	// Check if scanning file or directory
	fileInfo, err := os.Stat(opts.Path)
	if err != nil {
		return fmt.Errorf("failed to stat path: %w", err)
	}

	if fileInfo.IsDir() {
		issues, err = scanner.ScanProject(ctx, opts.Path)
	} else {
		issues, err = scanner.ScanFile(ctx, opts.Path)
	}

	if err != nil {
		return fmt.Errorf("dependency check failed: %w", err)
	}

	duration := time.Since(startTime)

	// Filter for dependency-related issues
	depIssues := filterIssuesByCategory(issues, "dependency")

	fmt.Printf("✅ Dependency check completed in %v\n", duration)
	fmt.Printf("📦 Found %d dependency-related issues\n\n", len(depIssues))

	if len(depIssues) == 0 {
		fmt.Printf("🎉 No dependency vulnerabilities found!\n")
		return nil
	}

	// Display results
	displayOptions := &ScanOptions{
		Format:   "table",
		Severity: "",
	}

	if err := displayResults(depIssues, displayOptions); err != nil {
		return fmt.Errorf("failed to display results: %w", err)
	}

	// Show fix suggestions if requested
	if opts.Fix {
		displayFixSuggestions(depIssues, "dependencies")
	}

	return nil
}

// runCheckEnv executes environment checking
func runCheckEnv(cmd *cobra.Command, opts *CheckOptions) error {
	fmt.Printf("🔍 Checking environment configurations...\n")

	cfg, err := loadConfig(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	scanner, err := dependencies.NewScanner(cfg)
	if err != nil {
		return fmt.Errorf("failed to create scanner: %w", err)
	}

	startTime := time.Now()
	ctx := context.Background()

	var issues []models.SecurityIssue

	// Check if scanning file or directory
	fileInfo, err := os.Stat(opts.Path)
	if err != nil {
		return fmt.Errorf("failed to stat path: %w", err)
	}

	if fileInfo.IsDir() {
		issues, err = scanner.ScanProject(ctx, opts.Path)
	} else {
		issues, err = scanner.ScanFile(ctx, opts.Path)
	}

	if err != nil {
		return fmt.Errorf("environment check failed: %w", err)
	}

	duration := time.Since(startTime)

	// Filter for environment-related issues
	envIssues := filterIssuesByCategory(issues, "environment")

	fmt.Printf("✅ Environment check completed in %v\n", duration)
	fmt.Printf("🌍 Found %d environment-related issues\n\n", len(envIssues))

	if len(envIssues) == 0 {
		fmt.Printf("🎉 No environment security issues found!\n")
		return nil
	}

	// Display results
	displayOptions := &ScanOptions{
		Format:   "table",
		Severity: "",
	}

	if err := displayResults(envIssues, displayOptions); err != nil {
		return fmt.Errorf("failed to display results: %w", err)
	}

	// Show fix suggestions if requested
	if opts.Fix {
		displayFixSuggestions(envIssues, "environment")
	}

	return nil
}

// runCheckKeys executes key and secret checking
func runCheckKeys(cmd *cobra.Command, opts *CheckOptions) error {
	fmt.Printf("🔍 Checking for exposed keys and secrets...\n")

	cfg, err := loadConfig(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// Enable all key leak detection checks
	cfg.Security.Rules.KeyLeakDetection.Enabled = true
	cfg.Security.Rules.SmartContractAudit.Enabled = true
	cfg.Security.Rules.DependencyScanning.Enabled = true

	scanner, err := dependencies.NewScanner(cfg)
	if err != nil {
		return fmt.Errorf("failed to create scanner: %w", err)
	}

	startTime := time.Now()
	ctx := context.Background()

	var issues []models.SecurityIssue

	// Check if scanning file or directory
	fileInfo, err := os.Stat(opts.Path)
	if err != nil {
		return fmt.Errorf("failed to stat path: %w", err)
	}

	if fileInfo.IsDir() {
		issues, err = scanner.ScanProject(ctx, opts.Path)
	} else {
		issues, err = scanner.ScanFile(ctx, opts.Path)
	}

	if err != nil {
		return fmt.Errorf("key check failed: %w", err)
	}

	duration := time.Since(startTime)

	// Filter for key/secret-related issues
	keyIssues := filterIssuesByType(issues, []string{
		"private_key_exposure", "hardcoded_secret", "api_key_exposure",
		"seed_exposure", "mnemonic_exposure", "hardcoded_password",
	})

	fmt.Printf("✅ Key check completed in %v\n", duration)
	fmt.Printf("🔑 Found %d key/secret-related issues\n\n", len(keyIssues))

	if len(keyIssues) == 0 {
		fmt.Printf("🎉 No exposed keys or secrets found!\n")
		return nil
	}

	// Display results
	displayOptions := &ScanOptions{
		Format:   "table",
		Severity: "",
	}

	if err := displayResults(keyIssues, displayOptions); err != nil {
		return fmt.Errorf("failed to display results: %w", err)
	}

	// Show fix suggestions if requested
	if opts.Fix {
		displayFixSuggestions(keyIssues, "keys")
	}

	return nil
}

// runCheckCICD executes CI/CD checking
func runCheckCICD(cmd *cobra.Command, opts *CheckOptions) error {
	fmt.Printf("🔍 Checking CI/CD configurations...\n")

	cfg, err := loadConfig(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	scanner, err := dependencies.NewScanner(cfg)
	if err != nil {
		return fmt.Errorf("failed to create scanner: %w", err)
	}

	startTime := time.Now()
	ctx := context.Background()

	var issues []models.SecurityIssue

	// Check if scanning file or directory
	fileInfo, err := os.Stat(opts.Path)
	if err != nil {
		return fmt.Errorf("failed to stat path: %w", err)
	}

	if fileInfo.IsDir() {
		issues, err = scanner.ScanProject(ctx, opts.Path)
	} else {
		issues, err = scanner.ScanFile(ctx, opts.Path)
	}

	if err != nil {
		return fmt.Errorf("CI/CD check failed: %w", err)
	}

	duration := time.Since(startTime)

	// Filter for CI/CD-related issues
	cicdIssues := filterIssuesByCategory(issues, "cicd")

	fmt.Printf("✅ CI/CD check completed in %v\n", duration)
	fmt.Printf("🔄 Found %d CI/CD-related issues\n\n", len(cicdIssues))

	if len(cicdIssues) == 0 {
		fmt.Printf("🎉 No CI/CD security issues found!\n")
		return nil
	}

	// Display results
	displayOptions := &ScanOptions{
		Format:   "table",
		Severity: "",
	}

	if err := displayResults(cicdIssues, displayOptions); err != nil {
		return fmt.Errorf("failed to display results: %w", err)
	}

	// Show fix suggestions if requested
	if opts.Fix {
		displayFixSuggestions(cicdIssues, "cicd")
	}

	return nil
}

// Helper functions

func filterIssuesByCategory(issues []models.SecurityIssue, category string) []models.SecurityIssue {
	var filtered []models.SecurityIssue
	for _, issue := range issues {
		if strings.ToLower(issue.Category) == strings.ToLower(category) {
			filtered = append(filtered, issue)
		}
	}
	return filtered
}

func filterIssuesByType(issues []models.SecurityIssue, types []string) []models.SecurityIssue {
	typeMap := make(map[string]bool)
	for _, t := range types {
		typeMap[strings.ToLower(t)] = true
	}

	var filtered []models.SecurityIssue
	for _, issue := range issues {
		if typeMap[strings.ToLower(issue.Type)] {
			filtered = append(filtered, issue)
		}
	}
	return filtered
}

func displayFixSuggestions(issues []models.SecurityIssue, category string) {
	fmt.Printf("\n💡 Fix Suggestions for %s:\n", strings.Title(category))
	fmt.Printf("═══════════════════════════════════\n")

	suggestions := make(map[string][]string)

	for _, issue := range issues {
		if issue.Suggestion != "" {
			suggestions[issue.Type] = append(suggestions[issue.Type], issue.Suggestion)
		}
	}

	for issueType, suggestionList := range suggestions {
		fmt.Printf("\n🔧 %s:\n", strings.Title(strings.ReplaceAll(issueType, "_", " ")))

		// Remove duplicates
		seen := make(map[string]bool)
		for _, suggestion := range suggestionList {
			if !seen[suggestion] {
				fmt.Printf("   • %s\n", suggestion)
				seen[suggestion] = true
			}
		}
	}

	fmt.Printf("\n📚 General Best Practices:\n")
	switch category {
	case "dependencies":
		fmt.Printf("   • Regularly update dependencies to latest secure versions\n")
		fmt.Printf("   • Use dependency lock files for reproducible builds\n")
		fmt.Printf("   • Monitor vulnerability databases for new issues\n")
		fmt.Printf("   • Consider using automated dependency update tools\n")
	case "environment":
		fmt.Printf("   • Never commit secrets to version control\n")
		fmt.Printf("   • Use environment variables for configuration\n")
		fmt.Printf("   • Enable SSL/TLS verification in production\n")
		fmt.Printf("   • Disable debug mode in production environments\n")
	case "keys":
		fmt.Printf("   • Use secure key management systems\n")
		fmt.Printf("   • Rotate keys regularly\n")
		fmt.Printf("   • Use hardware security modules for critical keys\n")
		fmt.Printf("   • Implement proper access controls\n")
	case "cicd":
		fmt.Printf("   • Pin third-party actions to specific versions\n")
		fmt.Printf("   • Use minimal required permissions\n")
		fmt.Printf("   • Validate all user inputs in scripts\n")
		fmt.Printf("   • Use secure secret management features\n")
	}
}
