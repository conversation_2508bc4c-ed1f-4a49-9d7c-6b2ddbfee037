/* SPT Design System - Professional Theme */

@import '@angular/material/prebuilt-themes/indigo-pink.css';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Design System Foundation */
:root {
  /* Brand Colors */
  --spt-primary: #2563eb;
  --spt-primary-50: #eff6ff;
  --spt-primary-100: #dbeafe;
  --spt-primary-200: #bfdbfe;
  --spt-primary-300: #93c5fd;
  --spt-primary-400: #60a5fa;
  --spt-primary-500: #3b82f6;
  --spt-primary-600: #2563eb;
  --spt-primary-700: #1d4ed8;
  --spt-primary-800: #1e40af;
  --spt-primary-900: #1e3a8a;

  /* Secondary Colors */
  --spt-secondary: #7c3aed;
  --spt-secondary-50: #f5f3ff;
  --spt-secondary-100: #ede9fe;
  --spt-secondary-200: #ddd6fe;
  --spt-secondary-300: #c4b5fd;
  --spt-secondary-400: #a78bfa;
  --spt-secondary-500: #8b5cf6;
  --spt-secondary-600: #7c3aed;
  --spt-secondary-700: #6d28d9;
  --spt-secondary-800: #5b21b6;
  --spt-secondary-900: #4c1d95;

  /* Accent Colors */
  --spt-accent: #06b6d4;
  --spt-accent-50: #ecfeff;
  --spt-accent-100: #cffafe;
  --spt-accent-200: #a5f3fc;
  --spt-accent-300: #67e8f9;
  --spt-accent-400: #22d3ee;
  --spt-accent-500: #06b6d4;
  --spt-accent-600: #0891b2;
  --spt-accent-700: #0e7490;
  --spt-accent-800: #155e75;
  --spt-accent-900: #164e63;

  /* Neutral Colors */
  --spt-gray-50: #f9fafb;
  --spt-gray-100: #f3f4f6;
  --spt-gray-200: #e5e7eb;
  --spt-gray-300: #d1d5db;
  --spt-gray-400: #9ca3af;
  --spt-gray-500: #6b7280;
  --spt-gray-600: #4b5563;
  --spt-gray-700: #374151;
  --spt-gray-800: #1f2937;
  --spt-gray-900: #111827;

  /* Semantic Colors */
  --spt-success: #10b981;
  --spt-success-50: #ecfdf5;
  --spt-success-100: #d1fae5;
  --spt-success-500: #10b981;
  --spt-success-600: #059669;
  --spt-success-700: #047857;

  --spt-warning: #f59e0b;
  --spt-warning-50: #fffbeb;
  --spt-warning-100: #fef3c7;
  --spt-warning-500: #f59e0b;
  --spt-warning-600: #d97706;
  --spt-warning-700: #b45309;

  --spt-error: #ef4444;
  --spt-error-50: #fef2f2;
  --spt-error-100: #fee2e2;
  --spt-error-500: #ef4444;
  --spt-error-600: #dc2626;
  --spt-error-700: #b91c1c;

  --spt-info: #3b82f6;
  --spt-info-50: #eff6ff;
  --spt-info-100: #dbeafe;
  --spt-info-500: #3b82f6;
  --spt-info-600: #2563eb;
  --spt-info-700: #1d4ed8;

  /* Typography Scale */
  --spt-text-xs: 0.75rem;      /* 12px */
  --spt-text-sm: 0.875rem;     /* 14px */
  --spt-text-base: 1rem;       /* 16px */
  --spt-text-lg: 1.125rem;     /* 18px */
  --spt-text-xl: 1.25rem;      /* 20px */
  --spt-text-2xl: 1.5rem;      /* 24px */
  --spt-text-3xl: 1.875rem;    /* 30px */
  --spt-text-4xl: 2.25rem;     /* 36px */
  --spt-text-5xl: 3rem;        /* 48px */

  /* Font Weights */
  --spt-font-light: 300;
  --spt-font-normal: 400;
  --spt-font-medium: 500;
  --spt-font-semibold: 600;
  --spt-font-bold: 700;

  /* Spacing Scale */
  --spt-space-1: 0.25rem;      /* 4px */
  --spt-space-2: 0.5rem;       /* 8px */
  --spt-space-3: 0.75rem;      /* 12px */
  --spt-space-4: 1rem;         /* 16px */
  --spt-space-5: 1.25rem;      /* 20px */
  --spt-space-6: 1.5rem;       /* 24px */
  --spt-space-8: 2rem;         /* 32px */
  --spt-space-10: 2.5rem;      /* 40px */
  --spt-space-12: 3rem;        /* 48px */
  --spt-space-16: 4rem;        /* 64px */
  --spt-space-20: 5rem;        /* 80px */

  /* Border Radius */
  --spt-radius-sm: 0.25rem;    /* 4px */
  --spt-radius-md: 0.375rem;   /* 6px */
  --spt-radius-lg: 0.5rem;     /* 8px */
  --spt-radius-xl: 0.75rem;    /* 12px */
  --spt-radius-2xl: 1rem;      /* 16px */
  --spt-radius-3xl: 1.5rem;    /* 24px */

  /* Shadows */
  --spt-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --spt-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --spt-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --spt-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --spt-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --spt-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Z-Index Scale */
  --spt-z-dropdown: 1000;
  --spt-z-sticky: 1020;
  --spt-z-fixed: 1030;
  --spt-z-modal-backdrop: 1040;
  --spt-z-modal: 1050;
  --spt-z-popover: 1060;
  --spt-z-tooltip: 1070;

  /* Legacy Variables for Compatibility */
  --primary-color: var(--spt-primary-600);
  --primary-dark: var(--spt-primary-700);
  --secondary-color: var(--spt-secondary-600);
  --accent-color: var(--spt-accent-500);
  --background-color: #ffffff;
  --surface-color: var(--spt-gray-50);
  --text-primary: var(--spt-gray-900);
  --text-secondary: var(--spt-gray-600);
  --border-color: var(--spt-gray-200);
  --shadow-light: var(--spt-shadow-sm);
  --shadow-medium: var(--spt-shadow-md);
  --shadow-large: var(--spt-shadow-xl);
}

/* Base Styles */
html, body {
  height: 100%;
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--spt-gray-900);
  background-color: var(--spt-gray-50);
  line-height: 1.6;
}

/* Typography System */
.spt-text-xs { font-size: var(--spt-text-xs); }
.spt-text-sm { font-size: var(--spt-text-sm); }
.spt-text-base { font-size: var(--spt-text-base); }
.spt-text-lg { font-size: var(--spt-text-lg); }
.spt-text-xl { font-size: var(--spt-text-xl); }
.spt-text-2xl { font-size: var(--spt-text-2xl); }
.spt-text-3xl { font-size: var(--spt-text-3xl); }
.spt-text-4xl { font-size: var(--spt-text-4xl); }
.spt-text-5xl { font-size: var(--spt-text-5xl); }

.spt-font-light { font-weight: var(--spt-font-light); }
.spt-font-normal { font-weight: var(--spt-font-normal); }
.spt-font-medium { font-weight: var(--spt-font-medium); }
.spt-font-semibold { font-weight: var(--spt-font-semibold); }
.spt-font-bold { font-weight: var(--spt-font-bold); }

.spt-text-primary { color: var(--spt-gray-900); }
.spt-text-secondary { color: var(--spt-gray-600); }
.spt-text-muted { color: var(--spt-gray-500); }

/* Heading Styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--spt-font-semibold);
  line-height: 1.25;
  margin: 0 0 var(--spt-space-4) 0;
  color: var(--spt-gray-900);
}

h1 { font-size: var(--spt-text-4xl); }
h2 { font-size: var(--spt-text-3xl); }
h3 { font-size: var(--spt-text-2xl); }
h4 { font-size: var(--spt-text-xl); }
h5 { font-size: var(--spt-text-lg); }
h6 { font-size: var(--spt-text-base); }

/* Spacing Utilities */
.spt-m-0 { margin: 0; }
.spt-m-1 { margin: var(--spt-space-1); }
.spt-m-2 { margin: var(--spt-space-2); }
.spt-m-3 { margin: var(--spt-space-3); }
.spt-m-4 { margin: var(--spt-space-4); }
.spt-m-5 { margin: var(--spt-space-5); }
.spt-m-6 { margin: var(--spt-space-6); }
.spt-m-8 { margin: var(--spt-space-8); }

.spt-p-0 { padding: 0; }
.spt-p-1 { padding: var(--spt-space-1); }
.spt-p-2 { padding: var(--spt-space-2); }
.spt-p-3 { padding: var(--spt-space-3); }
.spt-p-4 { padding: var(--spt-space-4); }
.spt-p-5 { padding: var(--spt-space-5); }
.spt-p-6 { padding: var(--spt-space-6); }
.spt-p-8 { padding: var(--spt-space-8); }

.spt-mb-2 { margin-bottom: var(--spt-space-2); }
.spt-mb-3 { margin-bottom: var(--spt-space-3); }
.spt-mb-4 { margin-bottom: var(--spt-space-4); }
.spt-mb-6 { margin-bottom: var(--spt-space-6); }
.spt-mb-8 { margin-bottom: var(--spt-space-8); }

.spt-mt-2 { margin-top: var(--spt-space-2); }
.spt-mt-3 { margin-top: var(--spt-space-3); }
.spt-mt-4 { margin-top: var(--spt-space-4); }
.spt-mt-6 { margin-top: var(--spt-space-6); }
.spt-mt-8 { margin-top: var(--spt-space-8); }

/* SPT Component System */

/* Card Components */
.spt-card {
  background: white;
  border-radius: var(--spt-radius-xl);
  box-shadow: var(--spt-shadow-sm);
  border: 1px solid var(--spt-gray-200);
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.spt-card:hover {
  box-shadow: var(--spt-shadow-md);
  transform: translateY(-1px);
}

.spt-card-elevated {
  box-shadow: var(--spt-shadow-lg);
}

.spt-card-header {
  padding: var(--spt-space-6);
  border-bottom: 1px solid var(--spt-gray-100);
  background: var(--spt-gray-50);
}

.spt-card-content {
  padding: var(--spt-space-6);
}

.spt-card-footer {
  padding: var(--spt-space-4) var(--spt-space-6);
  border-top: 1px solid var(--spt-gray-100);
  background: var(--spt-gray-50);
}

/* Button Components */
.spt-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spt-space-2);
  padding: var(--spt-space-3) var(--spt-space-4);
  border-radius: var(--spt-radius-lg);
  font-weight: var(--spt-font-medium);
  font-size: var(--spt-text-sm);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
}

.spt-btn-primary {
  background: var(--spt-primary-600);
  color: white;
}

.spt-btn-primary:hover {
  background: var(--spt-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--spt-shadow-md);
}

.spt-btn-secondary {
  background: var(--spt-gray-100);
  color: var(--spt-gray-700);
  border: 1px solid var(--spt-gray-300);
}

.spt-btn-secondary:hover {
  background: var(--spt-gray-200);
  border-color: var(--spt-gray-400);
}

.spt-btn-outline {
  background: transparent;
  color: var(--spt-primary-600);
  border: 1px solid var(--spt-primary-600);
}

.spt-btn-outline:hover {
  background: var(--spt-primary-50);
  border-color: var(--spt-primary-700);
  color: var(--spt-primary-700);
}

.spt-btn-sm {
  padding: var(--spt-space-2) var(--spt-space-3);
  font-size: var(--spt-text-xs);
}

.spt-btn-lg {
  padding: var(--spt-space-4) var(--spt-space-6);
  font-size: var(--spt-text-base);
}

/* Badge Components */
.spt-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spt-space-1) var(--spt-space-3);
  border-radius: var(--spt-radius-xl);
  font-size: var(--spt-text-xs);
  font-weight: var(--spt-font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.spt-badge-primary {
  background: var(--spt-primary-100);
  color: var(--spt-primary-700);
  border-color: var(--spt-primary-200);
}

.spt-badge-success {
  background: var(--spt-success-100);
  color: var(--spt-success-700);
  border-color: var(--spt-success-200);
}

.spt-badge-warning {
  background: var(--spt-warning-100);
  color: var(--spt-warning-700);
  border-color: var(--spt-warning-200);
}

.spt-badge-error {
  background: var(--spt-error-100);
  color: var(--spt-error-700);
  border-color: var(--spt-error-200);
}

.spt-badge-info {
  background: var(--spt-info-100);
  color: var(--spt-info-700);
  border-color: var(--spt-info-200);
}

.spt-badge-critical {
  background: var(--spt-error-50);
  color: var(--spt-error-800);
  border-color: var(--spt-error-300);
  font-weight: var(--spt-font-bold);
}

.spt-badge-high {
  background: var(--spt-warning-50);
  color: var(--spt-warning-800);
  border-color: var(--spt-warning-300);
}

.spt-badge-medium {
  background: var(--spt-info-50);
  color: var(--spt-info-800);
  border-color: var(--spt-info-300);
}

.spt-badge-low {
  background: var(--spt-success-50);
  color: var(--spt-success-800);
  border-color: var(--spt-success-300);
}

/* Global Code Block Components */
.spt-code-block {
  background: var(--spt-gray-50);
  border: 1px solid var(--spt-gray-200);
  border-radius: var(--spt-radius-xl);
  overflow: hidden;
  margin: var(--spt-space-4) 0;
  box-shadow: var(--spt-shadow-sm);
}

.spt-code-header {
  background: var(--spt-primary-50);
  padding: var(--spt-space-3) var(--spt-space-4);
  display: flex;
  align-items: center;
  gap: var(--spt-space-2);
  border-bottom: 1px solid var(--spt-primary-200);
  justify-content: space-between;
}

.spt-code-header mat-icon {
  color: var(--spt-primary-600);
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.spt-code-header span {
  color: var(--spt-primary-700);
  font-size: var(--spt-text-sm);
  font-weight: var(--spt-font-medium);
}

.spt-copy-btn {
  color: var(--spt-primary-600) !important;
  transition: all 0.2s ease !important;
  background: var(--spt-primary-100) !important;
  border-radius: var(--spt-radius-md) !important;
}

.spt-copy-btn:hover {
  color: var(--spt-primary-700) !important;
  background: var(--spt-primary-200) !important;
}

.spt-code-content {
  margin: 0;
  padding: var(--spt-space-4);
  background: white;
  color: var(--spt-gray-800);
  font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
  font-size: var(--spt-text-sm);
  line-height: 1.6;
  overflow-x: auto;
  border: 1px solid var(--spt-gray-100);
  border-top: none;
}

/* Enhanced Material Components */
.mat-mdc-card {
  border-radius: var(--spt-radius-xl) !important;
  box-shadow: var(--spt-shadow-sm) !important;
  border: 1px solid var(--spt-gray-200) !important;
  transition: all 0.2s ease-in-out !important;
}

.mat-mdc-card:hover {
  box-shadow: var(--spt-shadow-md) !important;
  transform: translateY(-1px) !important;
}

.mat-mdc-button {
  border-radius: var(--spt-radius-lg) !important;
  font-weight: var(--spt-font-medium) !important;
  text-transform: none !important;
  letter-spacing: 0 !important;
  font-family: 'Inter', sans-serif !important;
}

.mat-mdc-raised-button {
  border-radius: var(--spt-radius-lg) !important;
  box-shadow: var(--spt-shadow-sm) !important;
}

.mat-mdc-raised-button:hover {
  box-shadow: var(--spt-shadow-md) !important;
  transform: translateY(-1px) !important;
}

.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: var(--spt-radius-lg) !important;
  }
}

.mat-mdc-tab-group {
  .mat-mdc-tab-header {
    border-bottom: 1px solid var(--spt-gray-200) !important;
    background: white !important;
  }

  .mat-mdc-tab {
    font-weight: var(--spt-font-medium) !important;
    text-transform: none !important;
    font-family: 'Inter', sans-serif !important;
  }

  .mat-mdc-tab.mdc-tab--active {
    color: var(--spt-primary-600) !important;
  }
}

/* Comprehensive Icon Fixes */
mat-icon,
.mat-icon,
.material-icons,
.material-icons-outlined,
.material-icons-round,
.material-icons-sharp,
.material-icons-two-tone {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  line-height: 1 !important;
  font-feature-settings: 'liga' !important;
  -webkit-font-feature-settings: 'liga' !important;
  -moz-font-feature-settings: 'liga' !important;
  font-variation-settings: normal !important;
}

/* Material Card Avatar Icons */
.mat-mdc-card-avatar mat-icon,
.mat-card-avatar mat-icon,
[mat-card-avatar] mat-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  line-height: 1 !important;
}

/* Button Icons */
.mat-mdc-button mat-icon,
.mat-mdc-raised-button mat-icon,
.mat-mdc-stroked-button mat-icon,
.mat-mdc-flat-button mat-icon,
.mat-mdc-fab mat-icon,
.mat-mdc-mini-fab mat-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  line-height: 1 !important;
}

/* List Item Icons */
.mat-mdc-list-item mat-icon,
.mat-list-item mat-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  line-height: 1 !important;
}

/* Tab Icons */
.mat-mdc-tab mat-icon,
.mat-tab mat-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  vertical-align: middle !important;
  line-height: 1 !important;
  margin-right: var(--spt-space-2) !important;
}

/* Global utility classes */
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.margin-bottom {
  margin-bottom: 20px;
}

.padding {
  padding: 20px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Material Design customizations */
.mat-mdc-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
}

.mat-mdc-button {
  border-radius: 6px !important;
}

.mat-mdc-raised-button {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Security severity colors */
.severity-critical {
  color: #f44336 !important;
}

.severity-high {
  color: #ff9800 !important;
}

.severity-medium {
  color: #ffeb3b !important;
}

.severity-low {
  color: #4caf50 !important;
}

.severity-info {
  color: #2196f3 !important;
}

/* Chain-specific colors */
.chain-ethereum {
  color: #627eea !important;
}

.chain-bitcoin {
  color: #f7931a !important;
}

.chain-general {
  color: #666 !important;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
  
  .mat-mdc-card {
    margin: 10px !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}
