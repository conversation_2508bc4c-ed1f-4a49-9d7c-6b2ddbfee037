/* Professional SPT Theme */

@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Custom Material Theme Variables */
:root {
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --secondary-color: #764ba2;
  --accent-color: #0ea5e9;
  --background-color: #ffffff;
  --surface-color: #f8fafc;
  --text-primary: #1a202c;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 10px 25px rgba(0, 0, 0, 0.1), 0 20px 40px rgba(0, 0, 0, 0.1);
}

html, body {
  height: 100%;
  margin: 0;
  font-family: 'Inter', 'Segoe <PERSON>I', Tahoma, Geneva, Verdana, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Material Components */
.mat-mdc-card {
  border-radius: 16px !important;
  box-shadow: var(--shadow-medium) !important;
  border: 1px solid var(--border-color) !important;
}

.mat-mdc-button {
  border-radius: 8px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  letter-spacing: 0 !important;
}

.mat-mdc-raised-button {
  border-radius: 12px !important;
  box-shadow: var(--shadow-light) !important;
}

.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: 8px !important;
  }
}

.mat-mdc-tab-group {
  .mat-mdc-tab-header {
    border-bottom: 1px solid var(--border-color) !important;
  }

  .mat-mdc-tab {
    font-weight: 600 !important;
    text-transform: none !important;
  }
}

/* Global utility classes */
.full-width {
  width: 100%;
}

.text-center {
  text-align: center;
}

.margin-bottom {
  margin-bottom: 20px;
}

.padding {
  padding: 20px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Material Design customizations */
.mat-mdc-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
}

.mat-mdc-button {
  border-radius: 6px !important;
}

.mat-mdc-raised-button {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Security severity colors */
.severity-critical {
  color: #f44336 !important;
}

.severity-high {
  color: #ff9800 !important;
}

.severity-medium {
  color: #ffeb3b !important;
}

.severity-low {
  color: #4caf50 !important;
}

.severity-info {
  color: #2196f3 !important;
}

/* Chain-specific colors */
.chain-ethereum {
  color: #627eea !important;
}

.chain-bitcoin {
  color: #f7931a !important;
}

.chain-general {
  color: #666 !important;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
  
  .mat-mdc-card {
    margin: 10px !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}
