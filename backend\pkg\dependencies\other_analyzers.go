package dependencies

import (
	"blockchain-spt/backend/pkg/models"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// CargoAnalyzer analyzes Rust Cargo.toml files
type CargoAnalyzer struct {
	logger *logrus.Logger
}

// GoAnalyzer analyzes Go go.mod files
type GoAnalyzer struct {
	logger *logrus.Logger
}

// ComposerAnalyzer analyzes PHP composer.json files
type ComposerAnalyzer struct {
	logger *logrus.Logger
}

// MavenAnalyzer analyzes Java Maven pom.xml files
type MavenAnalyzer struct {
	logger *logrus.Logger
}

// GradleAnalyzer analyzes Java/Kotlin Gradle build files
type GradleAnalyzer struct {
	logger *logrus.Logger
}

// NuGetAnalyzer analyzes .NET NuGet package files
type NuGetAnalyzer struct {
	logger *logrus.Logger
}

// NewCargoAnalyzer creates a new Cargo analyzer
func NewCargoAnalyzer() *CargoAnalyzer {
	return &CargoAnalyzer{
		logger: logrus.New(),
	}
}

// NewGoAnalyzer creates a new Go analyzer
func NewGoAnalyzer() *GoAnalyzer {
	return &GoAnalyzer{
		logger: logrus.New(),
	}
}

// NewComposerAnalyzer creates a new Composer analyzer
func NewComposerAnalyzer() *ComposerAnalyzer {
	return &ComposerAnalyzer{
		logger: logrus.New(),
	}
}

// NewMavenAnalyzer creates a new Maven analyzer
func NewMavenAnalyzer() *MavenAnalyzer {
	return &MavenAnalyzer{
		logger: logrus.New(),
	}
}

// NewGradleAnalyzer creates a new Gradle analyzer
func NewGradleAnalyzer() *GradleAnalyzer {
	return &GradleAnalyzer{
		logger: logrus.New(),
	}
}

// NewNuGetAnalyzer creates a new NuGet analyzer
func NewNuGetAnalyzer() *NuGetAnalyzer {
	return &NuGetAnalyzer{
		logger: logrus.New(),
	}
}

// Cargo analyzer methods

// AnalyzePackageFile analyzes Cargo.toml files
func (cargo *CargoAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		lineNum := i + 1

		// Check for wildcard versions
		if strings.Contains(line, "version = \"*\"") || strings.Contains(line, "version = '*'") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("cargo_wildcard_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "wildcard_version",
				Severity:    "medium",
				Title:       "Wildcard Version in Cargo.toml",
				Description: "Using wildcard versions can lead to unexpected updates and security vulnerabilities",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Pin dependencies to specific versions",
				References:  []string{"https://doc.rust-lang.org/cargo/reference/specifying-dependencies.html"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for git dependencies without commit pinning
		if strings.Contains(line, "git = ") && !strings.Contains(line, "rev = ") && !strings.Contains(line, "tag = ") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("cargo_git_unpin_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "unpinned_git_dependency",
				Severity:    "high",
				Title:       "Unpinned Git Dependency",
				Description: "Git dependencies should be pinned to specific commits or tags",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Add 'rev = \"commit-hash\"' or 'tag = \"version\"' to pin the dependency",
				References:  []string{"https://doc.rust-lang.org/cargo/reference/specifying-dependencies.html#specifying-dependencies-from-git-repositories"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for insecure HTTP git URLs
		if strings.Contains(line, "git = \"http://") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("cargo_insecure_git_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "insecure_git_url",
				Severity:    "high",
				Title:       "Insecure Git URL",
				Description: "Git dependencies should use HTTPS instead of HTTP",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS URLs for git dependencies",
				References:  []string{"https://doc.rust-lang.org/cargo/reference/specifying-dependencies.html"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues, nil
}

// GetSupportedFiles returns supported file patterns
func (cargo *CargoAnalyzer) GetSupportedFiles() []string {
	return []string{"Cargo.toml", "Cargo.lock"}
}

// GetPackageManager returns the package manager name
func (cargo *CargoAnalyzer) GetPackageManager() string {
	return "cargo"
}

// Go analyzer methods

// AnalyzePackageFile analyzes go.mod files
func (goAnalyzer *GoAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		lineNum := i + 1
		trimmedLine := strings.TrimSpace(line)

		// Check for replace directives pointing to local paths
		if strings.HasPrefix(trimmedLine, "replace ") && strings.Contains(trimmedLine, " => ./") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("go_local_replace_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "local_replace_directive",
				Severity:    "medium",
				Title:       "Local Replace Directive",
				Description: "Replace directive points to local path which may not be available in production",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use versioned modules instead of local paths for production builds",
				References:  []string{"https://golang.org/ref/mod#go-mod-file-replace"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for insecure HTTP module sources
		if strings.Contains(trimmedLine, "http://") && !strings.Contains(trimmedLine, "//") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("go_insecure_http_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "insecure_module_source",
				Severity:    "high",
				Title:       "Insecure HTTP Module Source",
				Description: "Module source uses HTTP instead of HTTPS",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS URLs for module sources",
				References:  []string{"https://golang.org/ref/mod#module-path"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for known vulnerable modules (simplified check)
		vulnerableModules := []string{
			"github.com/dgrijalva/jwt-go", // Use golang-jwt/jwt instead
			"github.com/satori/go.uuid",   // Use google/uuid instead
		}

		for _, vulnModule := range vulnerableModules {
			if strings.Contains(trimmedLine, vulnModule) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("go_vulnerable_module_%s_%d", filepath.Base(filePath), lineNum),
					Type:        "vulnerable_module",
					Severity:    "high",
					Title:       "Known Vulnerable Module",
					Description: fmt.Sprintf("Module %s has known security vulnerabilities", vulnModule),
					File:        filePath,
					Line:        lineNum,
					Code:        strings.TrimSpace(line),
					Chain:       "general",
					Category:    "dependency",
					Suggestion:  "Replace with a secure alternative module",
					References:  []string{"https://pkg.go.dev/vuln/"},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
			}
		}
	}

	return issues, nil
}

// GetSupportedFiles returns supported file patterns
func (goAnalyzer *GoAnalyzer) GetSupportedFiles() []string {
	return []string{"go.mod", "go.sum"}
}

// GetPackageManager returns the package manager name
func (goAnalyzer *GoAnalyzer) GetPackageManager() string {
	return "go"
}

// Composer analyzer methods

// AnalyzePackageFile analyzes composer.json files
func (composer *ComposerAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		lineNum := i + 1
		trimmedLine := strings.TrimSpace(line)

		// Check for wildcard versions
		if strings.Contains(trimmedLine, `"*"`) && strings.Contains(trimmedLine, ":") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("composer_wildcard_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "wildcard_version",
				Severity:    "medium",
				Title:       "Wildcard Version in Composer",
				Description: "Using wildcard versions can lead to unexpected updates and security vulnerabilities",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Pin dependencies to specific versions",
				References:  []string{"https://getcomposer.org/doc/articles/versions.md"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for insecure repositories
		if strings.Contains(trimmedLine, `"type": "vcs"`) && strings.Contains(content, `"url": "http://`) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("composer_insecure_repo_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "insecure_repository",
				Severity:    "high",
				Title:       "Insecure Repository URL",
				Description: "Repository uses HTTP instead of HTTPS",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS URLs for repositories",
				References:  []string{"https://getcomposer.org/doc/05-repositories.md"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for known vulnerable packages
		vulnerablePackages := []string{
			"monolog/monolog",   // Check for old versions
			"symfony/symfony",   // Check for old versions
			"laravel/framework", // Check for old versions
		}

		for _, vulnPkg := range vulnerablePackages {
			if strings.Contains(trimmedLine, fmt.Sprintf(`"%s"`, vulnPkg)) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("composer_vulnerable_%s_%d", filepath.Base(filePath), lineNum),
					Type:        "potentially_vulnerable_package",
					Severity:    "medium",
					Title:       "Potentially Vulnerable Package",
					Description: fmt.Sprintf("Package %s may have known vulnerabilities in older versions", vulnPkg),
					File:        filePath,
					Line:        lineNum,
					Code:        strings.TrimSpace(line),
					Chain:       "general",
					Category:    "dependency",
					Suggestion:  "Check for security advisories and update to latest secure version",
					References:  []string{"https://security.symfony.com/", "https://packagist.org/"},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
			}
		}

		// Check for dev dependencies in production context
		if strings.Contains(trimmedLine, `"require-dev"`) && strings.Contains(content, `"minimum-stability": "dev"`) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("composer_dev_deps_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "dev_dependencies_in_production",
				Severity:    "low",
				Title:       "Development Dependencies Risk",
				Description: "Development dependencies with dev stability may introduce security risks",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use --no-dev flag in production deployments",
				References:  []string{"https://getcomposer.org/doc/03-cli.md#install-i"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues, nil
}

// GetSupportedFiles returns supported file patterns
func (composer *ComposerAnalyzer) GetSupportedFiles() []string {
	return []string{"composer.json", "composer.lock"}
}

// GetPackageManager returns the package manager name
func (composer *ComposerAnalyzer) GetPackageManager() string {
	return "composer"
}

// Maven analyzer methods

// AnalyzePackageFile analyzes pom.xml files
func (maven *MavenAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		lineNum := i + 1
		trimmedLine := strings.TrimSpace(line)

		// Check for SNAPSHOT versions in production
		if strings.Contains(trimmedLine, "-SNAPSHOT") && strings.Contains(trimmedLine, "<version>") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("maven_snapshot_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "snapshot_version",
				Severity:    "medium",
				Title:       "SNAPSHOT Version in Production",
				Description: "SNAPSHOT versions are unstable and should not be used in production",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use stable release versions instead of SNAPSHOT versions",
				References:  []string{"https://maven.apache.org/guides/getting-started/index.html#What_is_a_SNAPSHOT_version"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for insecure repositories
		if strings.Contains(trimmedLine, "<url>http://") && strings.Contains(content, "<repository>") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("maven_insecure_repo_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "insecure_repository",
				Severity:    "high",
				Title:       "Insecure Repository URL",
				Description: "Repository uses HTTP instead of HTTPS",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS URLs for repositories",
				References:  []string{"https://maven.apache.org/guides/mini/guide-multiple-repositories.html"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for overly broad version ranges
		if strings.Contains(trimmedLine, "<version>[") && (strings.Contains(trimmedLine, ",)") || strings.Contains(trimmedLine, ",]")) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("maven_broad_version_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "broad_version_range",
				Severity:    "medium",
				Title:       "Overly Broad Version Range",
				Description: "Version range is too broad and may include vulnerable versions",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use specific version ranges or pin to exact versions",
				References:  []string{"https://maven.apache.org/enforcer/enforcer-rules/versionRanges.html"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for known vulnerable dependencies
		vulnerableDependencies := []string{
			"log4j-core",
			"spring-core",
			"jackson-databind",
			"commons-collections",
		}

		for _, vulnDep := range vulnerableDependencies {
			if strings.Contains(trimmedLine, fmt.Sprintf("<artifactId>%s</artifactId>", vulnDep)) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("maven_vulnerable_%s_%d", filepath.Base(filePath), lineNum),
					Type:        "potentially_vulnerable_dependency",
					Severity:    "high",
					Title:       "Potentially Vulnerable Dependency",
					Description: fmt.Sprintf("Dependency %s has known security vulnerabilities in certain versions", vulnDep),
					File:        filePath,
					Line:        lineNum,
					Code:        strings.TrimSpace(line),
					Chain:       "general",
					Category:    "dependency",
					Suggestion:  "Check CVE databases and update to latest secure version",
					References:  []string{"https://nvd.nist.gov/", "https://cve.mitre.org/"},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
			}
		}
	}

	return issues, nil
}

// GetSupportedFiles returns supported file patterns
func (maven *MavenAnalyzer) GetSupportedFiles() []string {
	return []string{"pom.xml"}
}

// GetPackageManager returns the package manager name
func (maven *MavenAnalyzer) GetPackageManager() string {
	return "maven"
}

// Gradle analyzer methods

// AnalyzePackageFile analyzes build.gradle files
func (gradle *GradleAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		lineNum := i + 1
		trimmedLine := strings.TrimSpace(line)

		// Check for dynamic versions (+ notation)
		if strings.Contains(trimmedLine, "'+") || strings.Contains(trimmedLine, `"+"`) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("gradle_dynamic_version_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "dynamic_version",
				Severity:    "medium",
				Title:       "Dynamic Version in Gradle",
				Description: "Dynamic versions (+ notation) can lead to unexpected updates and security vulnerabilities",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Pin dependencies to specific versions",
				References:  []string{"https://docs.gradle.org/current/userguide/single_versions.html"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for insecure repositories
		if strings.Contains(trimmedLine, "http://") && (strings.Contains(content, "repositories") || strings.Contains(trimmedLine, "url")) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("gradle_insecure_repo_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "insecure_repository",
				Severity:    "high",
				Title:       "Insecure Repository URL",
				Description: "Repository uses HTTP instead of HTTPS",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS URLs for repositories",
				References:  []string{"https://docs.gradle.org/current/userguide/declaring_repositories.html"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for insecure plugin sources
		if strings.Contains(trimmedLine, "apply plugin:") && strings.Contains(content, "http://") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("gradle_insecure_plugin_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "insecure_plugin_source",
				Severity:    "high",
				Title:       "Insecure Plugin Source",
				Description: "Plugin source uses HTTP instead of HTTPS",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS URLs for plugin sources",
				References:  []string{"https://docs.gradle.org/current/userguide/plugins.html"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for known vulnerable dependencies
		vulnerableDependencies := []string{
			"log4j-core",
			"spring-core",
			"jackson-databind",
			"commons-collections",
			"junit:junit:4.", // Old JUnit versions
		}

		for _, vulnDep := range vulnerableDependencies {
			if strings.Contains(trimmedLine, vulnDep) && (strings.Contains(trimmedLine, "implementation") || strings.Contains(trimmedLine, "compile")) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("gradle_vulnerable_%s_%d", filepath.Base(filePath), lineNum),
					Type:        "potentially_vulnerable_dependency",
					Severity:    "high",
					Title:       "Potentially Vulnerable Dependency",
					Description: fmt.Sprintf("Dependency %s has known security vulnerabilities in certain versions", vulnDep),
					File:        filePath,
					Line:        lineNum,
					Code:        strings.TrimSpace(line),
					Chain:       "general",
					Category:    "dependency",
					Suggestion:  "Check CVE databases and update to latest secure version",
					References:  []string{"https://nvd.nist.gov/", "https://cve.mitre.org/"},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
			}
		}
	}

	return issues, nil
}

// GetSupportedFiles returns supported file patterns
func (gradle *GradleAnalyzer) GetSupportedFiles() []string {
	return []string{"build.gradle", "build.gradle.kts", "gradle.lockfile"}
}

// GetPackageManager returns the package manager name
func (gradle *GradleAnalyzer) GetPackageManager() string {
	return "gradle"
}

// NuGet analyzer methods

// AnalyzePackageFile analyzes NuGet package files
func (nuget *NuGetAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		lineNum := i + 1
		trimmedLine := strings.TrimSpace(line)

		// Check for wildcard versions
		if strings.Contains(trimmedLine, `Version="*"`) || strings.Contains(trimmedLine, `version="*"`) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("nuget_wildcard_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "wildcard_version",
				Severity:    "medium",
				Title:       "Wildcard Version in NuGet",
				Description: "Using wildcard versions can lead to unexpected updates and security vulnerabilities",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Pin dependencies to specific versions",
				References:  []string{"https://docs.microsoft.com/en-us/nuget/concepts/package-versioning"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for prerelease packages in production
		if strings.Contains(trimmedLine, "-alpha") || strings.Contains(trimmedLine, "-beta") || strings.Contains(trimmedLine, "-rc") || strings.Contains(trimmedLine, "-preview") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("nuget_prerelease_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "prerelease_package",
				Severity:    "medium",
				Title:       "Prerelease Package in Production",
				Description: "Prerelease packages may be unstable and should not be used in production",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use stable release versions instead of prerelease packages",
				References:  []string{"https://docs.microsoft.com/en-us/nuget/create-packages/prerelease-packages"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for insecure package sources
		if strings.Contains(trimmedLine, "http://") && (strings.Contains(trimmedLine, "packageSources") || strings.Contains(trimmedLine, "source")) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("nuget_insecure_source_%s_%d", filepath.Base(filePath), lineNum),
				Type:        "insecure_package_source",
				Severity:    "high",
				Title:       "Insecure Package Source",
				Description: "Package source uses HTTP instead of HTTPS",
				File:        filePath,
				Line:        lineNum,
				Code:        strings.TrimSpace(line),
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS URLs for package sources",
				References:  []string{"https://docs.microsoft.com/en-us/nuget/consume-packages/configuring-nuget-behavior"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for known vulnerable packages
		vulnerablePackages := []string{
			"Newtonsoft.Json",      // Check for old versions
			"System.Text.Json",     // Check for old versions
			"Microsoft.AspNetCore", // Check for old versions
			"EntityFramework",      // Check for old versions
		}

		for _, vulnPkg := range vulnerablePackages {
			if strings.Contains(trimmedLine, fmt.Sprintf(`Include="%s"`, vulnPkg)) || strings.Contains(trimmedLine, fmt.Sprintf(`id="%s"`, vulnPkg)) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("nuget_vulnerable_%s_%d", filepath.Base(filePath), lineNum),
					Type:        "potentially_vulnerable_package",
					Severity:    "medium",
					Title:       "Potentially Vulnerable Package",
					Description: fmt.Sprintf("Package %s may have known vulnerabilities in older versions", vulnPkg),
					File:        filePath,
					Line:        lineNum,
					Code:        strings.TrimSpace(line),
					Chain:       "general",
					Category:    "dependency",
					Suggestion:  "Check for security advisories and update to latest secure version",
					References:  []string{"https://github.com/advisories", "https://nvd.nist.gov/"},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
			}
		}
	}

	return issues, nil
}

// GetSupportedFiles returns supported file patterns
func (nuget *NuGetAnalyzer) GetSupportedFiles() []string {
	return []string{"packages.config", "*.csproj", "*.fsproj", "*.vbproj", "Directory.Packages.props"}
}

// GetPackageManager returns the package manager name
func (nuget *NuGetAnalyzer) GetPackageManager() string {
	return "nuget"
}
