import * as vscode from 'vscode';
import * as path from 'path';
import { SPTApiClient } from '../api/client';
import { ConfigurationManager } from '../config/manager';

export interface SecurityIssue {
    id: string;
    type: string;
    severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
    title: string;
    description: string;
    file: string;
    line: number;
    column: number;
    code: string;
    chain: string;
    category: string;
    cwe?: string;
    owasp?: string;
    references?: string[];
    suggestion: string;
    metadata?: { [key: string]: string };
}

export interface ScanResult {
    id: string;
    projectPath: string;
    chains: string[];
    status: string;
    startTime: string;
    endTime?: string;
    duration?: string;
    issues: SecurityIssue[];
    severityCounts: { [key: string]: number };
    filesScanned: number;
    linesScanned: number;
}

export class SecurityAnalyzer {
    private diagnosticCollection: vscode.DiagnosticCollection;
    private currentIssues: Map<string, SecurityIssue[]> = new Map();
    private isScanning: boolean = false;

    constructor(
        private apiClient: SPTApiClient,
        private configManager: ConfigurationManager
    ) {
        this.diagnosticCollection = vscode.languages.createDiagnosticCollection('spt-security');
    }

    async scanProject(projectPath: string): Promise<ScanResult | null> {
        if (this.isScanning) {
            vscode.window.showWarningMessage('Scan already in progress');
            return null;
        }

        this.isScanning = true;
        
        try {
            // Show progress
            return await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Scanning project for security issues...',
                cancellable: true
            }, async (progress, token) => {
                progress.report({ increment: 0, message: 'Starting scan...' });

                const chains = this.configManager.getConfig('chains') as string[];
                
                try {
                    // Call SPT backend API
                    const result = await this.apiClient.startScan({
                        project_path: projectPath,
                        chains: chains,
                        scan_type: 'full'
                    });

                    if (token.isCancellationRequested) {
                        return null;
                    }

                    progress.report({ increment: 50, message: 'Processing results...' });

                    // Get scan results
                    const scanResult = await this.waitForScanCompletion(result.scan_id, progress, token);
                    
                    if (scanResult) {
                        await this.processScanResults(scanResult);
                        vscode.window.showInformationMessage(
                            `Scan completed: ${scanResult.issues.length} issues found`
                        );
                    }

                    return scanResult;
                } catch (error) {
                    vscode.window.showErrorMessage(`Scan failed: ${error}`);
                    return null;
                }
            });
        } finally {
            this.isScanning = false;
        }
    }

    async scanFile(filePath: string): Promise<SecurityIssue[]> {
        try {
            const chains = this.configManager.getConfig('chains') as string[];
            
            // Call SPT backend API for file scan
            const result = await this.apiClient.scanFile(filePath, chains);
            
            if (result && result.issues) {
                await this.processFileIssues(filePath, result.issues);
                return result.issues;
            }
            
            return [];
        } catch (error) {
            console.error('File scan failed:', error);
            return [];
        }
    }

    private async waitForScanCompletion(
        scanId: string, 
        progress: vscode.Progress<{ increment?: number; message?: string }>,
        token: vscode.CancellationToken
    ): Promise<ScanResult | null> {
        const maxAttempts = 60; // 5 minutes max
        let attempts = 0;

        while (attempts < maxAttempts && !token.isCancellationRequested) {
            try {
                const result = await this.apiClient.getScanResult(scanId);
                
                if (result.status === 'completed') {
                    progress.report({ increment: 100, message: 'Scan completed!' });
                    return result;
                } else if (result.status === 'failed') {
                    throw new Error('Scan failed on server');
                }

                // Update progress
                const progressPercent = Math.min(90, (attempts / maxAttempts) * 90);
                progress.report({ 
                    increment: progressPercent - (attempts > 0 ? ((attempts - 1) / maxAttempts) * 90 : 0),
                    message: `Scanning... (${result.status})` 
                });

                // Wait 5 seconds before next check
                await new Promise(resolve => setTimeout(resolve, 5000));
                attempts++;
            } catch (error) {
                console.error('Error checking scan status:', error);
                attempts++;
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }

        if (token.isCancellationRequested) {
            return null;
        }

        throw new Error('Scan timeout - please check the SPT backend');
    }

    private async processScanResults(scanResult: ScanResult): Promise<void> {
        // Clear previous diagnostics
        this.diagnosticCollection.clear();
        this.currentIssues.clear();

        // Group issues by file
        const issuesByFile = new Map<string, SecurityIssue[]>();
        
        for (const issue of scanResult.issues) {
            const filePath = issue.file;
            if (!issuesByFile.has(filePath)) {
                issuesByFile.set(filePath, []);
            }
            issuesByFile.get(filePath)!.push(issue);
        }

        // Process each file
        for (const [filePath, issues] of issuesByFile) {
            await this.processFileIssues(filePath, issues);
        }

        // Update context for commands
        vscode.commands.executeCommand('setContext', 'spt.hasSecurityIssues', scanResult.issues.length > 0);
    }

    private async processFileIssues(filePath: string, issues: SecurityIssue[]): Promise<void> {
        this.currentIssues.set(filePath, issues);

        if (this.configManager.getConfig('showProblems')) {
            const diagnostics: vscode.Diagnostic[] = issues.map(issue => {
                const range = new vscode.Range(
                    Math.max(0, issue.line - 1),
                    Math.max(0, issue.column - 1),
                    Math.max(0, issue.line - 1),
                    Math.max(0, issue.column + 10)
                );

                const diagnostic = new vscode.Diagnostic(
                    range,
                    `[${issue.severity.toUpperCase()}] ${issue.title}: ${issue.description}`,
                    this.getSeverityLevel(issue.severity)
                );

                diagnostic.source = 'SPT';
                diagnostic.code = issue.type;
                
                return diagnostic;
            });

            const uri = vscode.Uri.file(filePath);
            this.diagnosticCollection.set(uri, diagnostics);
        }

        // Update decorations if enabled
        if (this.configManager.getConfig('showInlineDecorations')) {
            await this.updateDecorations(filePath, issues);
        }
    }

    private getSeverityLevel(severity: string): vscode.DiagnosticSeverity {
        switch (severity) {
            case 'critical':
            case 'high':
                return vscode.DiagnosticSeverity.Error;
            case 'medium':
                return vscode.DiagnosticSeverity.Warning;
            case 'low':
                return vscode.DiagnosticSeverity.Information;
            case 'info':
                return vscode.DiagnosticSeverity.Hint;
            default:
                return vscode.DiagnosticSeverity.Warning;
        }
    }

    private async updateDecorations(filePath: string, issues: SecurityIssue[]): Promise<void> {
        const editors = vscode.window.visibleTextEditors.filter(
            editor => editor.document.uri.fsPath === filePath
        );

        for (const editor of editors) {
            // Create decoration types for different severities
            const decorationTypes = this.getDecorationTypes();
            
            const decorationsByType = new Map<string, vscode.DecorationOptions[]>();
            
            for (const issue of issues) {
                const decorationType = decorationTypes[issue.severity];
                if (!decorationType) {
                    continue;
                }

                if (!decorationsByType.has(issue.severity)) {
                    decorationsByType.set(issue.severity, []);
                }

                const range = new vscode.Range(
                    Math.max(0, issue.line - 1),
                    Math.max(0, issue.column - 1),
                    Math.max(0, issue.line - 1),
                    Math.max(0, issue.column + 10)
                );

                decorationsByType.get(issue.severity)!.push({
                    range,
                    hoverMessage: `**${issue.title}**\n\n${issue.description}\n\n*Suggestion: ${issue.suggestion}*`
                });
            }

            // Apply decorations
            for (const [severity, decorations] of decorationsByType) {
                const decorationType = decorationTypes[severity];
                editor.setDecorations(decorationType, decorations);
            }
        }
    }

    private getDecorationTypes(): { [key: string]: vscode.TextEditorDecorationType } {
        return {
            critical: vscode.window.createTextEditorDecorationType({
                backgroundColor: 'rgba(255, 0, 0, 0.2)',
                border: '1px solid red',
                borderRadius: '2px'
            }),
            high: vscode.window.createTextEditorDecorationType({
                backgroundColor: 'rgba(255, 165, 0, 0.2)',
                border: '1px solid orange',
                borderRadius: '2px'
            }),
            medium: vscode.window.createTextEditorDecorationType({
                backgroundColor: 'rgba(255, 255, 0, 0.2)',
                border: '1px solid yellow',
                borderRadius: '2px'
            }),
            low: vscode.window.createTextEditorDecorationType({
                backgroundColor: 'rgba(0, 255, 0, 0.1)',
                border: '1px solid green',
                borderRadius: '2px'
            })
        };
    }

    async fixIssue(issue: SecurityIssue): Promise<void> {
        // Implement automatic fix suggestions
        vscode.window.showInformationMessage(`Fix suggestion for ${issue.title}: ${issue.suggestion}`);
    }

    async ignoreIssue(issue: SecurityIssue): Promise<void> {
        // Implement issue ignoring
        vscode.window.showInformationMessage(`Issue ${issue.title} has been ignored`);
    }

    getIssuesForFile(filePath: string): SecurityIssue[] {
        return this.currentIssues.get(filePath) || [];
    }

    dispose(): void {
        this.diagnosticCollection.dispose();
    }
}
