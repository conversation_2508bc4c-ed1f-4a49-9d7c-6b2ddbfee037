import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';

@Component({
  selector: 'app-getting-started',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTabsModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatExpansionModule
  ],
  template: `
    <div class="getting-started-container">
      <div class="page-header">
        <h1>
          <mat-icon>play_arrow</mat-icon>
          Getting Started
        </h1>
        <p class="page-subtitle">
          Get SPT up and running in your development environment
        </p>
      </div>

      <mat-tab-group class="content-tabs" animationDuration="300ms">
        <!-- Prerequisites Tab -->
        <mat-tab label="Prerequisites">
          <div class="tab-content">
            <h2>System Requirements</h2>
            <p>Before installing SPT, ensure you have the following prerequisites:</p>
            
            <div class="requirements-grid">
              <mat-card class="requirement-card" *ngFor="let req of prerequisites">
                <mat-card-header>
                  <mat-icon mat-card-avatar [style.background-color]="req.color">
                    {{ req.icon }}
                  </mat-icon>
                  <mat-card-title>{{ req.name }}</mat-card-title>
                  <mat-card-subtitle>{{ req.version }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <p>{{ req.description }}</p>
                  <div class="install-command" *ngIf="req.installCommand">
                    <strong>Installation:</strong>
                    <code>{{ req.installCommand }}</code>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Installation Tab -->
        <mat-tab label="Installation">
          <div class="tab-content">
            <h2>Installation Steps</h2>
            
            <mat-accordion class="installation-steps">
              <mat-expansion-panel *ngFor="let step of installationSteps; let i = index" [expanded]="i === 0">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <span class="step-number">{{ i + 1 }}</span>
                    {{ step.title }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ step.description }}
                  </mat-panel-description>
                </mat-expansion-panel-header>
                
                <div class="step-content">
                  <p>{{ step.details }}</p>
                  <div class="code-block" *ngIf="step.commands">
                    <div class="code-header">
                      <mat-icon>terminal</mat-icon>
                      <span>Commands</span>
                    </div>
                    <pre><code>{{ step.commands }}</code></pre>
                  </div>
                  <div class="step-notes" *ngIf="step.notes">
                    <mat-icon>info</mat-icon>
                    <span>{{ step.notes }}</span>
                  </div>
                </div>
              </mat-expansion-panel>
            </mat-accordion>
          </div>
        </mat-tab>

        <!-- Configuration Tab -->
        <mat-tab label="Configuration">
          <div class="tab-content">
            <h2>Configuration</h2>
            <p>Configure SPT for your development environment:</p>

            <mat-card class="config-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>settings</mat-icon>
                <mat-card-title>Backend Configuration</mat-card-title>
                <mat-card-subtitle>spt.config.json</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="code-block">
                  <div class="code-header">
                    <mat-icon>code</mat-icon>
                    <span>Configuration File</span>
                  </div>
                  <pre><code>{{ backendConfig }}</code></pre>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="config-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>web</mat-icon>
                <mat-card-title>Frontend Configuration</mat-card-title>
                <mat-card-subtitle>Environment Variables</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="code-block">
                  <div class="code-header">
                    <mat-icon>code</mat-icon>
                    <span>Environment File</span>
                  </div>
                  <pre><code>{{ frontendConfig }}</code></pre>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>

        <!-- Quick Start Tab -->
        <mat-tab label="Quick Start">
          <div class="tab-content">
            <h2>Quick Start Guide</h2>
            <p>Get SPT running with these simple commands:</p>

            <div class="quick-start-flow">
              <div class="flow-step" *ngFor="let step of quickStartFlow; let i = index">
                <div class="flow-step-header">
                  <div class="flow-step-number">{{ i + 1 }}</div>
                  <h3>{{ step.title }}</h3>
                </div>
                <p>{{ step.description }}</p>
                <div class="code-block" *ngIf="step.command">
                  <pre><code>{{ step.command }}</code></pre>
                </div>
                <div class="flow-arrow" *ngIf="i < quickStartFlow.length - 1">
                  <mat-icon>arrow_downward</mat-icon>
                </div>
              </div>
            </div>

            <mat-card class="success-card">
              <mat-card-header>
                <mat-icon mat-card-avatar style="background-color: #4caf50;">check_circle</mat-icon>
                <mat-card-title>Success!</mat-card-title>
                <mat-card-subtitle>SPT is now running</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <p>You can now access:</p>
                <ul>
                  <li><strong>Web Dashboard:</strong> <a href="http://localhost:4200" target="_blank">http://localhost:4200</a></li>
                  <li><strong>API Server:</strong> <a href="http://localhost:8080" target="_blank">http://localhost:8080</a></li>
                  <li><strong>Health Check:</strong> <a href="http://localhost:8080/health" target="_blank">http://localhost:8080/health</a></li>
                </ul>
              </mat-card-content>
              <mat-card-actions>
                <button mat-raised-button color="primary" routerLink="/doc/api-reference">
                  <mat-icon>api</mat-icon>
                  Explore API
                </button>
                <button mat-stroked-button routerLink="/doc/security-practices">
                  <mat-icon>security</mat-icon>
                  Security Guide
                </button>
              </mat-card-actions>
            </mat-card>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styles: [`
    .getting-started-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 48px;
      background: #ffffff;
    }

    .page-header {
      text-align: center;
      margin-bottom: 48px;
      padding: 32px 0;
    }

    .page-header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      color: #1a202c;
      margin: 0 0 12px 0;
      font-size: 2.5em;
      font-weight: 800;
      letter-spacing: -1px;
    }

    .page-header h1 mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #667eea;
    }

    .page-subtitle {
      color: #64748b;
      font-size: 1.25em;
      margin: 0;
      font-weight: 500;
    }

    .content-tabs {
      margin-bottom: 32px;
    }

    .tab-content {
      padding: 24px 0;
    }

    .requirements-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 32px;
      margin-top: 32px;
    }

    .install-command {
      margin-top: 12px;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
    }

    .installation-steps {
      margin-top: 24px;
    }

    .step-number {
      background: #1976d2;
      color: white;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8em;
      font-weight: bold;
      margin-right: 12px;
    }

    .step-content {
      padding: 16px 0;
    }

    .code-block {
      margin: 16px 0;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }

    .code-header {
      background: #f5f5f5;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      border-bottom: 1px solid #e0e0e0;
    }

    .code-block pre {
      margin: 0;
      padding: 16px;
      background: #fafafa;
      overflow-x: auto;
    }

    .code-block code {
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }

    .step-notes {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      padding: 8px 12px;
      background: #e3f2fd;
      border-radius: 4px;
      color: #1976d2;
    }

    .config-card {
      margin-bottom: 24px;
    }

    .quick-start-flow {
      margin: 24px 0;
    }

    .flow-step {
      margin-bottom: 32px;
      position: relative;
    }

    .flow-step-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 12px;
    }

    .flow-step-number {
      background: #1976d2;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      font-size: 1.1em;
    }

    .flow-step h3 {
      margin: 0;
      color: #1976d2;
    }

    .flow-arrow {
      text-align: center;
      margin: 16px 0;
      color: #1976d2;
    }

    .success-card {
      margin-top: 32px;
      border: 2px solid #4caf50;
    }

    .success-card ul {
      margin: 16px 0;
    }

    .success-card a {
      color: #1976d2;
      text-decoration: none;
    }

    .success-card a:hover {
      text-decoration: underline;
    }

    /* Icon fixes for proper display */
    mat-icon {
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      vertical-align: middle !important;
      line-height: 1 !important;
    }

    .code-header {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .code-header mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .step-notes {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-top: 12px;
      padding: 12px;
      background: #f0f9ff;
      border-radius: 8px;
      border-left: 4px solid #0ea5e9;
    }

    .step-notes mat-icon {
      color: #0ea5e9;
      font-size: 18px;
      width: 18px;
      height: 18px;
      margin-top: 2px;
      flex-shrink: 0;
    }

    .flow-arrow {
      text-align: center;
      margin: 16px 0;
    }

    .flow-arrow mat-icon {
      color: #667eea;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    mat-card-header .mat-mdc-card-avatar mat-icon {
      font-size: 20px !important;
      width: 20px !important;
      height: 20px !important;
      line-height: 1 !important;
    }

    @media (max-width: 768px) {
      .requirements-grid {
        grid-template-columns: 1fr;
      }

      .flow-step-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
      }
    }
  `]
})
export class GettingStartedComponent {
  prerequisites = [
    {
      name: 'Go',
      version: '1.21+',
      description: 'Required for the backend services and CLI tool',
      icon: 'code',
      color: '#00ADD8',
      installCommand: 'https://golang.org/doc/install'
    },
    {
      name: 'Node.js',
      version: '18+',
      description: 'Required for the frontend dashboard and development tools',
      icon: 'javascript',
      color: '#339933',
      installCommand: 'https://nodejs.org/'
    },
    {
      name: 'Angular CLI',
      version: 'Latest',
      description: 'Required for frontend development and building',
      icon: 'web',
      color: '#DD0031',
      installCommand: 'npm install -g @angular/cli'
    },
    {
      name: 'Git',
      version: 'Latest',
      description: 'Required for version control and repository management',
      icon: 'source',
      color: '#F05032',
      installCommand: 'https://git-scm.com/downloads'
    }
  ];

  installationSteps = [
    {
      title: 'Clone Repository',
      description: 'Get the SPT source code',
      details: 'Clone the SPT repository from GitHub to your local development environment.',
      commands: 'git clone https://github.com/blockchain-spt/spt.git\ncd spt',
      notes: 'Make sure you have Git installed and configured'
    },
    {
      title: 'Backend Setup',
      description: 'Install Go dependencies and start backend',
      details: 'Navigate to the backend directory, install dependencies, and start the Go server.',
      commands: 'cd backend\ngo mod tidy\ngo run cmd/main.go',
      notes: 'Backend will start on port 8080 by default'
    },
    {
      title: 'Frontend Setup',
      description: 'Install Node.js dependencies and start frontend',
      details: 'Navigate to the frontend directory, install npm packages, and start the Angular development server.',
      commands: 'cd frontend\nnpm install\nnpm start',
      notes: 'Frontend will start on port 4200 by default'
    },
    {
      title: 'CLI Tool (Optional)',
      description: 'Build the command-line interface',
      details: 'Build the SPT CLI tool for command-line security scanning.',
      commands: 'make cli\n# or\ngo build -o spt cmd/main.go',
      notes: 'CLI tool will be available as ./spt'
    }
  ];

  quickStartFlow = [
    {
      title: 'Clone & Navigate',
      description: 'Get the source code and navigate to the project directory',
      command: 'git clone https://github.com/blockchain-spt/spt.git\ncd spt'
    },
    {
      title: 'Start Backend',
      description: 'Launch the Go backend server with all APIs',
      command: 'cd backend && go run cmd/main.go'
    },
    {
      title: 'Start Frontend',
      description: 'Launch the Angular development server in a new terminal',
      command: 'cd frontend && npm install && npm start'
    },
    {
      title: 'Access Application',
      description: 'Open your browser and start using SPT',
      command: 'Open http://localhost:4200 in your browser'
    }
  ];

  backendConfig = `{
  "environment": "development",
  "server": {
    "port": 8080,
    "host": "localhost"
  },
  "database": {
    "type": "sqlite",
    "database": "spt.db"
  },
  "security": {
    "jwt_secret": "your-secret-key",
    "cors_enabled": true
  },
  "scanning": {
    "max_concurrent_scans": 5,
    "timeout_seconds": 300
  }
}`;

  frontendConfig = `# Frontend Environment Variables
API_URL=http://localhost:8080
WS_URL=ws://localhost:8080/ws
ENVIRONMENT=development
DEBUG=true`;
}
