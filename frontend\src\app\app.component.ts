import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterModule, Router, NavigationEnd } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { Observable } from 'rxjs';
import { filter, map, startWith } from 'rxjs/operators';
import { AuthService, User } from './services/auth.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    RouterModule,
    MatToolbarModule,
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatBadgeModule
  ],
  template: `
    <!-- Documentation Route - No Authentication Required -->
    <div *ngIf="isDocumentationRoute$ | async">
      <router-outlet></router-outlet>
    </div>

    <!-- Main App - Authentication Required -->
    <div *ngIf="!(isDocumentationRoute$ | async)">
      <div *ngIf="currentUser$ | async as user; else loginView">
      <mat-sidenav-container class="sidenav-container">
        <mat-sidenav #drawer class="sidenav" fixedInViewport
            [attr.role]="'navigation'"
            [mode]="'side'"
            [opened]="true">
          <mat-toolbar>SPT Menu</mat-toolbar>
          <mat-nav-list>
            <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
              <mat-icon>dashboard</mat-icon>
              <span>Dashboard</span>
            </a>
            <a mat-list-item routerLink="/scan" routerLinkActive="active">
              <mat-icon>security</mat-icon>
              <span>Security Scan</span>
            </a>
            <a mat-list-item routerLink="/checklist" routerLinkActive="active">
              <mat-icon>checklist</mat-icon>
              <span>Security Checklist</span>
            </a>
            <a mat-list-item routerLink="/reports" routerLinkActive="active">
              <mat-icon>assessment</mat-icon>
              <span>Reports</span>
            </a>
            <a mat-list-item routerLink="/projects" routerLinkActive="active">
              <mat-icon>folder</mat-icon>
              <span>Projects</span>
            </a>
            <a mat-list-item routerLink="/settings" routerLinkActive="active">
              <mat-icon>settings</mat-icon>
              <span>Settings</span>
            </a>
            <mat-divider></mat-divider>
            <a mat-list-item routerLink="/doc" routerLinkActive="active">
              <mat-icon>description</mat-icon>
              <span>Documentation</span>
            </a>
          </mat-nav-list>
        </mat-sidenav>
        <mat-sidenav-content>
          <mat-toolbar color="primary">
            <button
              type="button"
              aria-label="Toggle sidenav"
              mat-icon-button>
              <mat-icon aria-label="Side nav toggle icon">menu</mat-icon>
            </button>
            <span>🛡️ Blockchain Security Protocol Tool</span>
            <span class="spacer"></span>

            <!-- Notifications -->
            <button mat-icon-button>
              <mat-icon>notifications</mat-icon>
            </button>


            <!-- User Menu -->
            <button mat-icon-button (click)="logout()">
              <mat-icon>logout</mat-icon>
            </button>

          </mat-toolbar>
          <div class="content">
            <router-outlet></router-outlet>
          </div>
        </mat-sidenav-content>
      </mat-sidenav-container>
      </div>
    </div>

    <ng-template #loginView>
      <router-outlet></router-outlet>
    </ng-template>
  `,
  styles: [`
    .sidenav-container {
      height: 100vh;
    }

    .sidenav {
      width: 250px;
    }

    .sidenav .mat-toolbar {
      background: inherit;
    }

    .mat-toolbar.mat-primary {
      position: sticky;
      top: 0;
      z-index: 1;
    }

    .spacer {
      flex: 1 1 auto;
    }

    .content {
      padding: 20px;
      height: calc(100vh - 64px);
      overflow-y: auto;
    }

    .active {
      background-color: rgba(0, 0, 0, 0.04);
    }

    .user-info {
      padding: 16px;
      text-align: center;
      background-color: #f5f5f5;
    }

    .user-name {
      font-weight: 500;
      font-size: 16px;
      color: #333;
    }

    .user-role {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }

    mat-nav-list a {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    mat-nav-list mat-icon {
      margin-right: 0;
    }

    .login-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .login-toolbar {
      z-index: 2;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
  `]
})
export class AppComponent implements OnInit {
  title = 'SPT - Blockchain Security Protocol Tool';
  currentUser$: Observable<User | null>;
  isDocumentationRoute$: Observable<boolean>;
  user: User = {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    created_at: new Date().toISOString()
  };

  constructor(private authService: AuthService, private router: Router) {
    this.currentUser$ = this.authService.currentUser$;

    // Check if current route is documentation - include initial route
    this.isDocumentationRoute$ = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map((event: NavigationEnd) => event.url.startsWith('/doc')),
      // Start with current URL check
      startWith(this.router.url.startsWith('/doc'))
    );
  }

  ngOnInit(): void {
    // Check if user is already logged in
    // Auto-login removed for proper testing
  }

  hasRole(roles: string[]): boolean {
    return this.authService.hasAnyRole(roles);
  }

  logout(): void {
    this.authService.logout();
  }
}
