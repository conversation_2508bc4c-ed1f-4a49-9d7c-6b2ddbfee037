.confirm-dialog {
  min-width: 400px;
  max-width: 500px;

  .dialog-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    
    mat-icon {
      font-size: 24px;
      width: 24px;
      height: 24px;
    }
  }

  .dialog-content {
    padding: 16px 0;
    
    p {
      margin: 0;
      font-size: 16px;
      line-height: 1.5;
      color: rgba(0, 0, 0, 0.87);
    }
  }

  .dialog-actions {
    padding: 16px 0 8px 0;
    gap: 8px;

    button {
      display: flex;
      align-items: center;
      gap: 4px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    button[mat-raised-button] {
      min-width: 100px;
    }
  }
}

// Responsive design
@media (max-width: 500px) {
  .confirm-dialog {
    min-width: 90vw;
    max-width: 90vw;
  }
}

// Dark theme support
.dark-theme {
  .confirm-dialog {
    .dialog-content p {
      color: rgba(255, 255, 255, 0.87);
    }
  }
}

// Animation
.confirm-dialog {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
