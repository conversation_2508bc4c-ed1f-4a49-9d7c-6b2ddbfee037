import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Project } from '../../../models/project.model';

export interface ProjectDialogData {
  project?: Project;
  mode: 'create' | 'edit';
}

@Component({
  selector: 'app-project-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './project-dialog.component.html',
  styleUrls: ['./project-dialog.component.scss']
})
export class ProjectDialogComponent implements OnInit {
  projectForm: FormGroup;
  isEditMode: boolean;

  blockchainTypes = [
    { value: 'ethereum', label: 'Ethereum' },
    { value: 'bitcoin', label: 'Bitcoin' },
    { value: 'general', label: 'General' }
  ];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<ProjectDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ProjectDialogData
  ) {
    this.isEditMode = data.mode === 'edit';
    this.projectForm = this.createForm();
  }

  ngOnInit(): void {
    if (this.isEditMode && this.data.project) {
      this.projectForm.patchValue({
        name: this.data.project.name,
        description: this.data.project.description,
        blockchain_type: this.data.project.blockchain_type,
        repository_url: this.data.project.repository_url
      });
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      blockchain_type: ['ethereum', Validators.required],
      repository_url: ['', [Validators.pattern(/^https?:\/\/.+/)]]
    });
  }

  onSubmit(): void {
    if (this.projectForm.valid) {
      const formValue = this.projectForm.value;
      
      const project: Partial<Project> = {
        name: formValue.name,
        description: formValue.description,
        blockchain_type: formValue.blockchain_type,
        repository_url: formValue.repository_url || undefined,
        status: 'active'
      };

      if (this.isEditMode && this.data.project) {
        project.id = this.data.project.id;
        project.created_at = this.data.project.created_at;
        project.updated_at = new Date();
      } else {
        project.id = this.generateId();
        project.created_at = new Date();
        project.updated_at = new Date();
        project.scan_count = 0;
      }

      this.dialogRef.close(project);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  getErrorMessage(fieldName: string): string {
    const field = this.projectForm.get(fieldName);
    if (field?.hasError('required')) {
      return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
    }
    if (field?.hasError('minlength')) {
      const minLength = field.errors?.['minlength']?.requiredLength;
      return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${minLength} characters`;
    }
    if (field?.hasError('pattern')) {
      return 'Please enter a valid URL (http:// or https://)';
    }
    return '';
  }

  getBlockchainIcon(type: string): string {
    switch (type) {
      case 'ethereum':
        return 'currency_exchange';
      case 'bitcoin':
        return 'currency_bitcoin';
      case 'general':
        return 'code';
      default:
        return 'account_tree';
    }
  }
}
