"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecurityAnalyzer = void 0;
const vscode = __importStar(require("vscode"));
class SecurityAnalyzer {
    constructor(apiClient, configManager) {
        this.apiClient = apiClient;
        this.configManager = configManager;
        this.currentIssues = new Map();
        this.isScanning = false;
        this.diagnosticCollection = vscode.languages.createDiagnosticCollection('spt-security');
    }
    async scanProject(projectPath) {
        if (this.isScanning) {
            vscode.window.showWarningMessage('Scan already in progress');
            return null;
        }
        this.isScanning = true;
        try {
            // Show progress
            return await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'Scanning project for security issues...',
                cancellable: true
            }, async (progress, token) => {
                progress.report({ increment: 0, message: 'Starting scan...' });
                const chains = this.configManager.getConfig('chains');
                try {
                    // Call SPT backend API
                    const result = await this.apiClient.startScan({
                        project_path: projectPath,
                        chains: chains,
                        scan_type: 'full'
                    });
                    if (token.isCancellationRequested) {
                        return null;
                    }
                    progress.report({ increment: 50, message: 'Processing results...' });
                    // Get scan results
                    const scanResult = await this.waitForScanCompletion(result.scan_id, progress, token);
                    if (scanResult) {
                        await this.processScanResults(scanResult);
                        vscode.window.showInformationMessage(`Scan completed: ${scanResult.issues.length} issues found`);
                    }
                    return scanResult;
                }
                catch (error) {
                    vscode.window.showErrorMessage(`Scan failed: ${error}`);
                    return null;
                }
            });
        }
        finally {
            this.isScanning = false;
        }
    }
    async scanFile(filePath) {
        try {
            const chains = this.configManager.getConfig('chains');
            // Call SPT backend API for file scan
            const result = await this.apiClient.scanFile(filePath, chains);
            if (result && result.issues) {
                await this.processFileIssues(filePath, result.issues);
                return result.issues;
            }
            return [];
        }
        catch (error) {
            console.error('File scan failed:', error);
            return [];
        }
    }
    async waitForScanCompletion(scanId, progress, token) {
        const maxAttempts = 60; // 5 minutes max
        let attempts = 0;
        while (attempts < maxAttempts && !token.isCancellationRequested) {
            try {
                const result = await this.apiClient.getScanResult(scanId);
                if (result.status === 'completed') {
                    progress.report({ increment: 100, message: 'Scan completed!' });
                    return result;
                }
                else if (result.status === 'failed') {
                    throw new Error('Scan failed on server');
                }
                // Update progress
                const progressPercent = Math.min(90, (attempts / maxAttempts) * 90);
                progress.report({
                    increment: progressPercent - (attempts > 0 ? ((attempts - 1) / maxAttempts) * 90 : 0),
                    message: `Scanning... (${result.status})`
                });
                // Wait 5 seconds before next check
                await new Promise(resolve => setTimeout(resolve, 5000));
                attempts++;
            }
            catch (error) {
                console.error('Error checking scan status:', error);
                attempts++;
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }
        if (token.isCancellationRequested) {
            return null;
        }
        throw new Error('Scan timeout - please check the SPT backend');
    }
    async processScanResults(scanResult) {
        // Clear previous diagnostics
        this.diagnosticCollection.clear();
        this.currentIssues.clear();
        // Group issues by file
        const issuesByFile = new Map();
        for (const issue of scanResult.issues) {
            const filePath = issue.file;
            if (!issuesByFile.has(filePath)) {
                issuesByFile.set(filePath, []);
            }
            issuesByFile.get(filePath).push(issue);
        }
        // Process each file
        for (const [filePath, issues] of issuesByFile) {
            await this.processFileIssues(filePath, issues);
        }
        // Update context for commands
        vscode.commands.executeCommand('setContext', 'spt.hasSecurityIssues', scanResult.issues.length > 0);
    }
    async processFileIssues(filePath, issues) {
        this.currentIssues.set(filePath, issues);
        if (this.configManager.getConfig('showProblems')) {
            const diagnostics = issues.map(issue => {
                const range = new vscode.Range(Math.max(0, issue.line - 1), Math.max(0, issue.column - 1), Math.max(0, issue.line - 1), Math.max(0, issue.column + 10));
                const diagnostic = new vscode.Diagnostic(range, `[${issue.severity.toUpperCase()}] ${issue.title}: ${issue.description}`, this.getSeverityLevel(issue.severity));
                diagnostic.source = 'SPT';
                diagnostic.code = issue.type;
                return diagnostic;
            });
            const uri = vscode.Uri.file(filePath);
            this.diagnosticCollection.set(uri, diagnostics);
        }
        // Update decorations if enabled
        if (this.configManager.getConfig('showInlineDecorations')) {
            await this.updateDecorations(filePath, issues);
        }
    }
    getSeverityLevel(severity) {
        switch (severity) {
            case 'critical':
            case 'high':
                return vscode.DiagnosticSeverity.Error;
            case 'medium':
                return vscode.DiagnosticSeverity.Warning;
            case 'low':
                return vscode.DiagnosticSeverity.Information;
            case 'info':
                return vscode.DiagnosticSeverity.Hint;
            default:
                return vscode.DiagnosticSeverity.Warning;
        }
    }
    async updateDecorations(filePath, issues) {
        const editors = vscode.window.visibleTextEditors.filter(editor => editor.document.uri.fsPath === filePath);
        for (const editor of editors) {
            // Create decoration types for different severities
            const decorationTypes = this.getDecorationTypes();
            const decorationsByType = new Map();
            for (const issue of issues) {
                const decorationType = decorationTypes[issue.severity];
                if (!decorationType) {
                    continue;
                }
                if (!decorationsByType.has(issue.severity)) {
                    decorationsByType.set(issue.severity, []);
                }
                const range = new vscode.Range(Math.max(0, issue.line - 1), Math.max(0, issue.column - 1), Math.max(0, issue.line - 1), Math.max(0, issue.column + 10));
                decorationsByType.get(issue.severity).push({
                    range,
                    hoverMessage: `**${issue.title}**\n\n${issue.description}\n\n*Suggestion: ${issue.suggestion}*`
                });
            }
            // Apply decorations
            for (const [severity, decorations] of decorationsByType) {
                const decorationType = decorationTypes[severity];
                editor.setDecorations(decorationType, decorations);
            }
        }
    }
    getDecorationTypes() {
        return {
            critical: vscode.window.createTextEditorDecorationType({
                backgroundColor: 'rgba(255, 0, 0, 0.2)',
                border: '1px solid red',
                borderRadius: '2px'
            }),
            high: vscode.window.createTextEditorDecorationType({
                backgroundColor: 'rgba(255, 165, 0, 0.2)',
                border: '1px solid orange',
                borderRadius: '2px'
            }),
            medium: vscode.window.createTextEditorDecorationType({
                backgroundColor: 'rgba(255, 255, 0, 0.2)',
                border: '1px solid yellow',
                borderRadius: '2px'
            }),
            low: vscode.window.createTextEditorDecorationType({
                backgroundColor: 'rgba(0, 255, 0, 0.1)',
                border: '1px solid green',
                borderRadius: '2px'
            })
        };
    }
    async fixIssue(issue) {
        // Implement automatic fix suggestions
        vscode.window.showInformationMessage(`Fix suggestion for ${issue.title}: ${issue.suggestion}`);
    }
    async ignoreIssue(issue) {
        // Implement issue ignoring
        vscode.window.showInformationMessage(`Issue ${issue.title} has been ignored`);
    }
    getIssuesForFile(filePath) {
        return this.currentIssues.get(filePath) || [];
    }
    dispose() {
        this.diagnosticCollection.dispose();
    }
}
exports.SecurityAnalyzer = SecurityAnalyzer;
//# sourceMappingURL=analyzer.js.map