{"Secure Web3 Connection": {"prefix": "spt-web3-secure", "body": ["import Web3 from 'web3';", "", "// Secure Web3 connection with error handling", "async function connectWeb3() {", "    try {", "        if (typeof window.ethereum !== 'undefined') {", "            // Request account access", "            await window.ethereum.request({ method: 'eth_requestAccounts' });", "            ", "            const web3 = new Web3(window.ethereum);", "            ", "            // Verify network", "            const networkId = await web3.eth.net.getId();", "            const expectedNetworkId = ${1:1}; // Mainnet", "            ", "            if (networkId !== expectedNetworkId) {", "                throw new Error(`Wrong network. Expected ${expectedNetworkId}, got ${networkId}`);", "            }", "            ", "            return web3;", "        } else {", "            throw new Error('MetaMask not detected');", "        }", "    } catch (error) {", "        console.error('Web3 connection failed:', error);", "        throw error;", "    }", "}"], "description": "Secure Web3 connection with proper error handling and network validation"}, "Safe Contract Interaction": {"prefix": "spt-contract-safe", "body": ["async function safeContractCall(contract, method, params = [], options = {}) {", "    try {", "        // Validate contract address", "        if (!Web3.utils.isAddress(contract._address)) {", "            throw new Error('Invalid contract address');", "        }", "        ", "        // Estimate gas first", "        const gasEstimate = await contract.methods[method](...params).estimateGas(options);", "        const gasLimit = Math.floor(gasEstimate * 1.2); // Add 20% buffer", "        ", "        // Execute transaction with gas limit", "        const result = await contract.methods[method](...params).send({", "            ...options,", "            gas: gasLimit", "        });", "        ", "        return result;", "    } catch (error) {", "        console.error(`Contract call failed for method ${method}:`, error);", "        throw error;", "    }", "}"], "description": "Safe contract interaction with gas estimation and error handling"}, "Input Validation": {"prefix": "spt-validate-input", "body": ["function validateAddress(address) {", "    if (!address || typeof address !== 'string') {", "        throw new Error('Address must be a string');", "    }", "    ", "    if (!Web3.utils.isAddress(address)) {", "        throw new Error('Invalid Ethereum address');", "    }", "    ", "    if (address === '******************************************') {", "        throw new Error('Cannot use zero address');", "    }", "    ", "    return address.toLowerCase();", "}", "", "function validateAmount(amount) {", "    if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {", "        throw new Error('Amount must be a positive number');", "    }", "    ", "    return Web3.utils.toWei(amount.toString(), 'ether');", "}"], "description": "Input validation functions for addresses and amounts"}, "Secure Private Key Handling": {"prefix": "spt-private-key", "body": ["// WARNING: Never hardcode private keys in production!", "// Use environment variables or secure key management", "", "import dotenv from 'dotenv';", "dotenv.config();", "", "function getPrivateKey() {", "    const privateKey = process.env.PRIVATE_KEY;", "    ", "    if (!privateKey) {", "        throw new Error('PRIVATE_KEY environment variable not set');", "    }", "    ", "    if (!privateKey.startsWith('0x')) {", "        return '0x' + privateKey;", "    }", "    ", "    return private<PERSON>ey;", "}", "", "// Create account from private key", "function createAccount(web3) {", "    try {", "        const privateKey = getPrivateKey();", "        const account = web3.eth.accounts.privateKeyToAccount(privateKey);", "        web3.eth.accounts.wallet.add(account);", "        return account;", "    } catch (error) {", "        console.error('Failed to create account:', error);", "        throw error;", "    }", "}"], "description": "Secure private key handling with environment variables"}, "Transaction Monitoring": {"prefix": "spt-tx-monitor", "body": ["async function monitorTransaction(web3, txHash, maxWaitTime = 300000) {", "    return new Promise((resolve, reject) => {", "        const startTime = Date.now();", "        ", "        const checkTransaction = async () => {", "            try {", "                const receipt = await web3.eth.getTransactionReceipt(txHash);", "                ", "                if (receipt) {", "                    if (receipt.status) {", "                        resolve(receipt);", "                    } else {", "                        reject(new Error('Transaction failed'));", "                    }", "                    return;", "                }", "                ", "                // Check timeout", "                if (Date.now() - startTime > maxWaitTime) {", "                    reject(new Error('Transaction timeout'));", "                    return;", "                }", "                ", "                // Check again in 2 seconds", "                setTimeout(checkTransaction, 2000);", "            } catch (error) {", "                reject(error);", "            }", "        };", "        ", "        checkTransaction();", "    });", "}"], "description": "Transaction monitoring with timeout and status checking"}, "Rate Limiting": {"prefix": "spt-rate-limit", "body": ["class RateLimiter {", "    constructor(maxRequests = 10, windowMs = 60000) {", "        this.maxRequests = maxRequests;", "        this.windowMs = windowMs;", "        this.requests = new Map();", "    }", "    ", "    isAllowed(identifier) {", "        const now = Date.now();", "        const windowStart = now - this.windowMs;", "        ", "        if (!this.requests.has(identifier)) {", "            this.requests.set(identifier, []);", "        }", "        ", "        const userRequests = this.requests.get(identifier);", "        ", "        // Remove old requests", "        const validRequests = userRequests.filter(time => time > windowStart);", "        this.requests.set(identifier, validRequests);", "        ", "        if (validRequests.length >= this.maxRequests) {", "            return false;", "        }", "        ", "        validRequests.push(now);", "        return true;", "    }", "}", "", "const rateLimiter = new RateLimiter(${1:10}, ${2:60000}); // 10 requests per minute"], "description": "Rate limiting implementation for API calls"}, "Secure API Call": {"prefix": "spt-api-secure", "body": ["async function secureApiCall(url, options = {}) {", "    const controller = new AbortController();", "    const timeoutId = setTimeout(() => controller.abort(), ${1:10000}); // 10 second timeout", "    ", "    try {", "        const response = await fetch(url, {", "            ...options,", "            signal: controller.signal,", "            headers: {", "                'Content-Type': 'application/json',", "                ...options.headers", "            }", "        });", "        ", "        clearTimeout(timeoutId);", "        ", "        if (!response.ok) {", "            throw new Error(`HTTP error! status: ${response.status}`);", "        }", "        ", "        const data = await response.json();", "        return data;", "    } catch (error) {", "        clearTimeout(timeoutId);", "        ", "        if (error.name === 'AbortError') {", "            throw new Error('Request timeout');", "        }", "        ", "        throw error;", "    }", "}"], "description": "Secure API call with timeout and error handling"}}