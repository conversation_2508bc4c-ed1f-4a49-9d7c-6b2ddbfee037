package storage

import (
	"blockchain-spt/backend/pkg/database"
	"blockchain-spt/backend/pkg/models"
)

// DatabaseAdapter adapts database.Database to StorageInterface
type DatabaseAdapter struct {
	db *database.Database
}

// NewDatabaseAdapter creates a new database adapter
func NewDatabaseAdapter(db *database.Database) *DatabaseAdapter {
	return &DatabaseAdapter{db: db}
}

// GetScanResult retrieves a scan result by ID
func (d *DatabaseAdapter) GetScanResult(id string) (*models.ScanResult, error) {
	return d.db.GetScanResult(id)
}

// CreateScanResult creates a new scan result
func (d *DatabaseAdapter) CreateScanResult(result *models.ScanResult) error {
	return d.db.CreateScanResult(result)
}

// UpdateScanResult updates an existing scan result
func (d *DatabaseAdapter) UpdateScanResult(result *models.ScanResult) error {
	return d.db.UpdateScanResult(result)
}

// GetScanHistory retrieves scan history with limit
func (d *DatabaseAdapter) GetScanHistory(limit int) ([]*models.ScanResult, error) {
	return d.db.GetScanHistory(limit)
}
