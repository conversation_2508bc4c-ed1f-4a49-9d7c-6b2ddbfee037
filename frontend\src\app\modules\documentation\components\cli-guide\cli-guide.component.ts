import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatTableModule } from '@angular/material/table';

interface CliCommand {
  name: string;
  description: string;
  usage: string;
  options: CliOption[];
  examples: CliExample[];
  notes?: string;
}

interface CliOption {
  flag: string;
  description: string;
  type: string;
  default?: string;
  required?: boolean;
}

interface CliExample {
  command: string;
  description: string;
  output?: string;
}

@Component({
  selector: 'app-cli-guide',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTabsModule,
    MatCardModule,
    MatIconModule,
    MatExpansionModule,
    MatChipsModule,
    MatTableModule
  ],
  template: `
    <div class="cli-guide-container">
      <div class="page-header">
        <h1>
          <mat-icon>terminal</mat-icon>
          CLI Guide
        </h1>
        <p class="page-subtitle">
          Complete guide to the SPT command-line interface
        </p>
      </div>

      <div class="cli-overview">
        <mat-card class="overview-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>info</mat-icon>
            <mat-card-title>SPT CLI Overview</mat-card-title>
            <mat-card-subtitle>Powerful command-line security scanning tool</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <p>The SPT CLI provides comprehensive security scanning capabilities for blockchain applications directly from the command line.</p>
            <div class="cli-features">
              <div class="feature" *ngFor="let feature of cliFeatures">
                <mat-icon [style.color]="feature.color">{{ feature.icon }}</mat-icon>
                <div>
                  <strong>{{ feature.title }}</strong>
                  <p>{{ feature.description }}</p>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <mat-tab-group class="cli-tabs" animationDuration="300ms">
        <!-- Installation Tab -->
        <mat-tab label="Installation">
          <div class="tab-content">
            <h2>Installation Methods</h2>
            
            <div class="installation-methods">
              <mat-card class="method-card" *ngFor="let method of installationMethods">
                <mat-card-header>
                  <mat-icon mat-card-avatar>{{ method.icon }}</mat-icon>
                  <mat-card-title>{{ method.title }}</mat-card-title>
                  <mat-card-subtitle>{{ method.description }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="code-block">
                    <div class="code-header">
                      <mat-icon>terminal</mat-icon>
                      <span>Commands</span>
                    </div>
                    <pre><code>{{ method.commands }}</code></pre>
                  </div>
                  <div class="method-notes" *ngIf="method.notes">
                    <mat-icon>info</mat-icon>
                    <span>{{ method.notes }}</span>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Commands Tab -->
        <mat-tab label="Commands">
          <div class="tab-content">
            <h2>Available Commands</h2>
            <p>Complete reference for all SPT CLI commands and their options.</p>
            
            <div class="commands-list">
              <mat-expansion-panel 
                *ngFor="let command of cliCommands" 
                class="command-panel">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <code class="command-name">spt {{ command.name }}</code>
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ command.description }}
                  </mat-panel-description>
                </mat-expansion-panel-header>

                <div class="command-details">
                  <div class="usage-section">
                    <h4>Usage</h4>
                    <div class="code-block">
                      <pre><code>{{ command.usage }}</code></pre>
                    </div>
                  </div>

                  <div class="options-section" *ngIf="command.options.length > 0">
                    <h4>Options</h4>
                    <table mat-table [dataSource]="command.options" class="options-table">
                      <ng-container matColumnDef="flag">
                        <th mat-header-cell *matHeaderCellDef>Flag</th>
                        <td mat-cell *matCellDef="let option">
                          <code>{{ option.flag }}</code>
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="type">
                        <th mat-header-cell *matHeaderCellDef>Type</th>
                        <td mat-cell *matCellDef="let option">
                          <mat-chip>{{ option.type }}</mat-chip>
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="description">
                        <th mat-header-cell *matHeaderCellDef>Description</th>
                        <td mat-cell *matCellDef="let option">{{ option.description }}</td>
                      </ng-container>
                      <ng-container matColumnDef="default">
                        <th mat-header-cell *matHeaderCellDef>Default</th>
                        <td mat-cell *matCellDef="let option">
                          <code *ngIf="option.default">{{ option.default }}</code>
                          <span *ngIf="!option.default">-</span>
                        </td>
                      </ng-container>
                      <tr mat-header-row *matHeaderRowDef="optionColumns"></tr>
                      <tr mat-row *matRowDef="let row; columns: optionColumns;"></tr>
                    </table>
                  </div>

                  <div class="examples-section" *ngIf="command.examples.length > 0">
                    <h4>Examples</h4>
                    <div class="example" *ngFor="let example of command.examples">
                      <p class="example-description">{{ example.description }}</p>
                      <div class="code-block">
                        <div class="code-header">
                          <mat-icon>terminal</mat-icon>
                          <span>Command</span>
                        </div>
                        <pre><code>{{ example.command }}</code></pre>
                      </div>
                      <div class="output-block" *ngIf="example.output">
                        <div class="code-header">
                          <mat-icon>output</mat-icon>
                          <span>Output</span>
                        </div>
                        <pre><code>{{ example.output }}</code></pre>
                      </div>
                    </div>
                  </div>

                  <div class="notes-section" *ngIf="command.notes">
                    <h4>Notes</h4>
                    <div class="notes-content">
                      <mat-icon>info</mat-icon>
                      <p>{{ command.notes }}</p>
                    </div>
                  </div>
                </div>
              </mat-expansion-panel>
            </div>
          </div>
        </mat-tab>

        <!-- Integration Tab -->
        <mat-tab label="CI/CD Integration">
          <div class="tab-content">
            <h2>CI/CD Integration</h2>
            <p>Integrate SPT CLI into your continuous integration and deployment pipelines.</p>
            
            <div class="integration-examples">
              <mat-card class="integration-card" *ngFor="let integration of integrationExamples">
                <mat-card-header>
                  <mat-icon mat-card-avatar>{{ integration.icon }}</mat-icon>
                  <mat-card-title>{{ integration.platform }}</mat-card-title>
                  <mat-card-subtitle>{{ integration.description }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="code-block">
                    <div class="code-header">
                      <mat-icon>code</mat-icon>
                      <span>{{ integration.filename }}</span>
                    </div>
                    <pre><code>{{ integration.config }}</code></pre>
                  </div>
                  <div class="integration-notes" *ngIf="integration.notes">
                    <mat-icon>lightbulb</mat-icon>
                    <span>{{ integration.notes }}</span>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>

            <!-- CI/CD Best Practices Section -->
            <div class="best-practices-section">
              <h3>CI/CD Best Practices</h3>
              <div class="practices-grid">
                <mat-card class="practice-card" *ngFor="let practice of cicdBestPractices">
                  <mat-card-header>
                    <mat-icon mat-card-avatar [style.background-color]="practice.color">{{ practice.icon }}</mat-icon>
                    <mat-card-title>{{ practice.title }}</mat-card-title>
                  </mat-card-header>
                  <mat-card-content>
                    <p>{{ practice.description }}</p>
                    <ul class="practice-tips">
                      <li *ngFor="let tip of practice.tips">{{ tip }}</li>
                    </ul>
                  </mat-card-content>
                </mat-card>
              </div>
            </div>

            <!-- Environment-Specific Configurations -->
            <div class="environment-configs">
              <h3>Environment-Specific Configurations</h3>
              <mat-card class="env-config-card">
                <mat-card-header>
                  <mat-icon mat-card-avatar>settings_applications</mat-icon>
                  <mat-card-title>Environment Variables</mat-card-title>
                  <mat-card-subtitle>Configure SPT for different environments</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="env-examples">
                    <div class="env-example" *ngFor="let env of environmentConfigs">
                      <h4>{{ env.name }}</h4>
                      <div class="spt-code-block">
                        <div class="spt-code-header">
                          <mat-icon>terminal</mat-icon>
                          <span>{{ env.type }}</span>
                        </div>
                        <pre class="spt-code-content"><code>{{ env.config }}</code></pre>
                      </div>
                      <p class="env-description">{{ env.description }}</p>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Configuration Tab -->
        <mat-tab label="Configuration">
          <div class="tab-content">
            <h2>Configuration</h2>
            <p>Configure SPT CLI for your development environment and preferences.</p>
            
            <mat-card class="config-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>settings</mat-icon>
                <mat-card-title>Configuration File</mat-card-title>
                <mat-card-subtitle>spt.config.json</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="code-block">
                  <div class="code-header">
                    <mat-icon>code</mat-icon>
                    <span>JSON Configuration</span>
                  </div>
                  <pre><code>{{ configExample }}</code></pre>
                </div>
                <div class="config-description">
                  <h4>Configuration Options</h4>
                  <div class="config-options">
                    <div class="config-option" *ngFor="let option of configOptions">
                      <strong>{{ option.key }}</strong>
                      <p>{{ option.description }}</p>
                    </div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styles: [`
    .cli-guide-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      text-align: center;
      margin-bottom: 32px;
    }

    .page-header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      color: #1976d2;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      color: #666;
      font-size: 1.1em;
      margin: 0;
    }

    .cli-overview {
      margin-bottom: 32px;
    }

    .overview-card {
      border: 1px solid #e0e0e0;
    }

    .cli-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }

    .feature {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .feature mat-icon {
      margin-top: 2px;
    }

    .feature strong {
      display: block;
      margin-bottom: 4px;
    }

    .feature p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    .cli-tabs {
      margin-bottom: 32px;
    }

    .tab-content {
      padding: 24px 0;
    }

    .tab-content h2 {
      color: #1976d2;
      margin-bottom: 8px;
    }

    .installation-methods {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .method-card {
      height: fit-content;
    }

    .method-notes {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      padding: 8px 12px;
      background: #e3f2fd;
      border-radius: 4px;
      color: #1976d2;
    }

    .commands-list {
      margin-top: 24px;
    }

    .command-panel {
      margin-bottom: 8px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .command-name {
      background: #f5f5f5;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-weight: 500;
    }

    .command-details {
      padding: 16px 0;
    }

    .usage-section,
    .options-section,
    .examples-section,
    .notes-section {
      margin-bottom: 24px;
    }

    .usage-section h4,
    .options-section h4,
    .examples-section h4,
    .notes-section h4 {
      margin: 0 0 12px 0;
      color: #1976d2;
    }

    .code-block {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 16px;
    }

    .code-header {
      background: #f5f5f5;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      border-bottom: 1px solid #e0e0e0;
    }

    .code-block pre {
      margin: 0;
      padding: 16px;
      background: #fafafa;
      overflow-x: auto;
    }

    .code-block code {
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }

    .output-block {
      border: 1px solid #4caf50;
      border-radius: 8px;
      overflow: hidden;
      margin-top: 8px;
    }

    .output-block .code-header {
      background: #e8f5e8;
      border-bottom-color: #4caf50;
    }

    .output-block pre {
      background: #f1f8e9;
    }

    .options-table {
      width: 100%;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .options-table code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.9em;
    }

    .example {
      margin-bottom: 24px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 8px;
    }

    .example-description {
      margin: 0 0 12px 0;
      font-weight: 500;
      color: #1976d2;
    }

    .notes-content {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 12px;
      background: #e3f2fd;
      border-radius: 8px;
      color: #1976d2;
    }

    .notes-content p {
      margin: 0;
    }

    .integration-examples {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .integration-card {
      height: fit-content;
    }

    .integration-notes {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      padding: 8px 12px;
      background: #fff3e0;
      border-radius: 4px;
      color: #f57c00;
    }

    .config-card {
      margin-top: 24px;
    }

    .config-description {
      margin-top: 24px;
    }

    .config-description h4 {
      margin: 0 0 16px 0;
      color: #1976d2;
    }

    .config-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
    }

    .config-option {
      padding: 12px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .config-option strong {
      display: block;
      margin-bottom: 4px;
      color: #1976d2;
    }

    .config-option p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    @media (max-width: 768px) {
      .cli-features {
        grid-template-columns: 1fr;
      }
      
      .installation-methods {
        grid-template-columns: 1fr;
      }
      
      .integration-examples {
        grid-template-columns: 1fr;
      }
      
      .config-options {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class CliGuideComponent {
  optionColumns: string[] = ['flag', 'type', 'description', 'default'];

  cliFeatures = [
    {
      title: 'Security Scanning',
      description: 'Comprehensive security analysis for blockchain applications',
      icon: 'security',
      color: '#1976d2'
    },
    {
      title: 'Multiple Formats',
      description: 'Output results in JSON, YAML, CSV, or human-readable formats',
      icon: 'description',
      color: '#4caf50'
    },
    {
      title: 'CI/CD Integration',
      description: 'Easy integration with continuous integration pipelines',
      icon: 'integration_instructions',
      color: '#ff9800'
    },
    {
      title: 'Configurable',
      description: 'Flexible configuration options for different environments',
      icon: 'tune',
      color: '#9c27b0'
    }
  ];

  installationMethods = [
    {
      title: 'From Source',
      description: 'Build from source code',
      icon: 'code',
      commands: `git clone https://github.com/blockchain-spt/spt.git
cd spt
go build -o spt cmd/main.go`,
      notes: 'Requires Go 1.21+ to be installed'
    },
    {
      title: 'Using Make',
      description: 'Build using Makefile',
      icon: 'build',
      commands: `git clone https://github.com/blockchain-spt/spt.git
cd spt
make cli`,
      notes: 'Binary will be created in build/ directory'
    },
    {
      title: 'Go Install',
      description: 'Install directly with Go',
      icon: 'download',
      commands: `go install github.com/blockchain-spt/cmd/spt@latest`,
      notes: 'Installs to $GOPATH/bin'
    }
  ];

  cliCommands: CliCommand[] = [
    {
      name: 'scan',
      description: 'Perform security scan on files or directories',
      usage: 'spt scan [flags] [path]',
      options: [
        { flag: '--chain', type: 'string', description: 'Blockchain chain to analyze', default: 'all' },
        { flag: '--format', type: 'string', description: 'Output format (json, yaml, csv, table)', default: 'table' },
        { flag: '--output', type: 'string', description: 'Output file path' },
        { flag: '--severity', type: 'string', description: 'Minimum severity level', default: 'medium' },
        { flag: '--recursive', type: 'boolean', description: 'Scan directories recursively', default: 'true' }
      ],
      examples: [
        {
          command: 'spt scan ./contracts',
          description: 'Scan all files in contracts directory',
          output: `Scanning ./contracts...
Found 3 issues:
  HIGH: Potential reentrancy in contract.sol:42
  MEDIUM: Unchecked return value in token.sol:15
  MEDIUM: Gas optimization opportunity in utils.sol:8`
        },
        {
          command: 'spt scan --chain ethereum --format json ./src',
          description: 'Scan for Ethereum-specific issues and output as JSON'
        }
      ],
      notes: 'Use --help flag with any command to see detailed usage information'
    },
    {
      name: 'audit',
      description: 'Perform comprehensive security audit',
      usage: 'spt audit [flags] [path]',
      options: [
        { flag: '--generate-report', type: 'boolean', description: 'Generate detailed report', default: 'false' },
        { flag: '--report-path', type: 'string', description: 'Report output path', default: './audit-report.html' },
        { flag: '--template', type: 'string', description: 'Report template', default: 'standard' }
      ],
      examples: [
        {
          command: 'spt audit --generate-report ./project',
          description: 'Perform audit and generate HTML report'
        }
      ]
    },
    {
      name: 'check',
      description: 'Run specific security checks',
      usage: 'spt check [subcommand] [flags] [path]',
      options: [
        { flag: '--fix', type: 'boolean', description: 'Attempt to fix issues automatically', default: 'false' }
      ],
      examples: [
        {
          command: 'spt check deps --fix',
          description: 'Check dependencies and fix known vulnerabilities'
        },
        {
          command: 'spt check env',
          description: 'Check environment configuration for security issues'
        }
      ]
    }
  ];

  integrationExamples = [
    {
      platform: 'GitHub Actions',
      description: 'Integrate SPT into GitHub Actions workflow',
      icon: 'integration_instructions',
      filename: '.github/workflows/security.yml',
      config: `name: Security Scan
on: [push, pull_request]

jobs:
  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      - name: Install SPT
        run: go install github.com/blockchain-spt/cmd/spt@latest
      - name: Run Security Scan
        run: spt scan --format json --output security-report.json ./
      - name: Upload Results
        uses: actions/upload-artifact@v3
        with:
          name: security-report
          path: security-report.json`,
      notes: 'Add GITHUB_TOKEN to secrets for private repositories'
    },
    {
      platform: 'GitLab CI',
      description: 'Integrate SPT into GitLab CI/CD pipeline',
      icon: 'integration_instructions',
      filename: '.gitlab-ci.yml',
      config: `security_scan:
  stage: test
  image: golang:1.21
  script:
    - go install github.com/blockchain-spt/cmd/spt@latest
    - spt scan --format json --output security-report.json ./
    - spt audit --generate-report --report-path audit-report.html ./
  artifacts:
    reports:
      junit: security-report.json
    paths:
      - audit-report.html
    expire_in: 1 week
  only:
    - merge_requests
    - main`,
      notes: 'Configure artifact storage for report persistence'
    },
    {
      platform: 'Azure DevOps',
      description: 'Integrate SPT into Azure DevOps Pipeline',
      icon: 'cloud',
      filename: 'azure-pipelines.yml',
      config: `trigger:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - contracts/*
      - src/*

pool:
  vmImage: 'ubuntu-latest'

variables:
  GO_VERSION: '1.21'
  SPT_VERSION: 'latest'

stages:
- stage: SecurityScan
  displayName: 'Security Analysis'
  jobs:
  - job: SPTScan
    displayName: 'SPT Security Scan'
    steps:
    - task: GoTool@0
      displayName: 'Install Go'
      inputs:
        version: '\$(GO_VERSION)'

    - script: |
        go install github.com/blockchain-spt/cmd/spt@\$(SPT_VERSION)
        echo "SPT installed successfully"
      displayName: 'Install SPT CLI'

    - script: |
        spt scan --format json --output \$(Agent.TempDirectory)/security-report.json ./
        spt audit --generate-report --report-path \$(Agent.TempDirectory)/audit-report.html ./
      displayName: 'Run Security Scan'
      continueOnError: true

    - task: PublishTestResults@2
      displayName: 'Publish Security Results'
      inputs:
        testResultsFormat: 'JUnit'
        testResultsFiles: '\$(Agent.TempDirectory)/security-report.json'
        testRunTitle: 'SPT Security Scan Results'
      condition: always()

    - task: PublishBuildArtifacts@1
      displayName: 'Publish Security Reports'
      inputs:
        pathToPublish: '\$(Agent.TempDirectory)'
        artifactName: 'security-reports'
        publishLocation: 'Container'
      condition: always()

    - script: |
        if [ -f "\$(Agent.TempDirectory)/security-report.json" ]; then
          CRITICAL_COUNT=\$(jq '.summary.critical // 0' \$(Agent.TempDirectory)/security-report.json)
          HIGH_COUNT=\$(jq '.summary.high // 0' \$(Agent.TempDirectory)/security-report.json)
          if [ "\$CRITICAL_COUNT" -gt 0 ] || [ "\$HIGH_COUNT" -gt 5 ]; then
            echo "##vso[task.logissue type=error]Critical security issues found: \$CRITICAL_COUNT critical, \$HIGH_COUNT high"
            exit 1
          fi
        fi
      displayName: 'Evaluate Security Results'
      condition: always()`,
      notes: 'Configure service connections for private repositories and adjust thresholds as needed'
    },
    {
      platform: 'AWS CodeBuild',
      description: 'Integrate SPT into AWS CodeBuild pipeline',
      icon: 'cloud_queue',
      filename: 'buildspec.yml',
      config: `version: 0.2

env:
  variables:
    GO_VERSION: "1.21"
    SPT_VERSION: "latest"
  parameter-store:
    GITHUB_TOKEN: "/spt/github-token"  # Optional for private repos

phases:
  install:
    runtime-versions:
      golang: \$GO_VERSION
    commands:
      - echo "Installing SPT CLI..."
      - go install github.com/blockchain-spt/cmd/spt@\$SPT_VERSION
      - spt version

  pre_build:
    commands:
      - echo "Preparing security scan..."
      - mkdir -p reports
      - echo "Current directory contents:"
      - ls -la

  build:
    commands:
      - echo "Running SPT security scan..."
      - spt scan --format json --output reports/security-report.json ./
      - spt audit --generate-report --report-path reports/audit-report.html ./
      - echo "Security scan completed"

  post_build:
    commands:
      - echo "Processing security results..."
      - |
        if [ -f "reports/security-report.json" ]; then
          CRITICAL_COUNT=\$(jq '.summary.critical // 0' reports/security-report.json)
          HIGH_COUNT=\$(jq '.summary.high // 0' reports/security-report.json)
          MEDIUM_COUNT=\$(jq '.summary.medium // 0' reports/security-report.json)

          echo "Security Summary:"
          echo "  Critical: \$CRITICAL_COUNT"
          echo "  High: \$HIGH_COUNT"
          echo "  Medium: \$MEDIUM_COUNT"

          # Fail build if critical issues found
          if [ "\$CRITICAL_COUNT" -gt 0 ]; then
            echo "Build failed due to critical security issues"
            exit 1
          fi

          # Warning for high issues
          if [ "\$HIGH_COUNT" -gt 10 ]; then
            echo "Warning: High number of high-severity issues (\$HIGH_COUNT)"
          fi
        else
          echo "Security report not found"
          exit 1
        fi

artifacts:
  files:
    - 'reports/**/*'
  name: spt-security-reports

reports:
  spt-security:
    files:
      - 'reports/security-report.json'
    file-format: 'JUNITXML'`,
      notes: 'Store sensitive tokens in AWS Parameter Store or Secrets Manager'
    },
    {
      platform: 'AWS CodePipeline',
      description: 'Complete AWS CodePipeline with SPT integration',
      icon: 'account_tree',
      filename: 'cloudformation-pipeline.yml',
      config: `AWSTemplateFormatVersion: '2010-09-09'
Description: 'SPT Security Pipeline with CodePipeline'

Parameters:
  GitHubRepo:
    Type: String
    Description: GitHub repository name
  GitHubOwner:
    Type: String
    Description: GitHub repository owner
  GitHubToken:
    Type: String
    NoEcho: true
    Description: GitHub personal access token

Resources:
  # S3 Bucket for artifacts
  ArtifactsBucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Sub '\${AWS::StackName}-spt-artifacts'
      VersioningConfiguration:
        Status: Enabled
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true

  # CodeBuild Project for SPT Security Scan
  SPTSecurityProject:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: !Sub '\${AWS::StackName}-spt-security'
      ServiceRole: !GetAtt CodeBuildRole.Arn
      Artifacts:
        Type: CODEPIPELINE
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_MEDIUM
        Image: aws/codebuild/amazonlinux2-x86_64-standard:3.0
        EnvironmentVariables:
          - Name: GITHUB_TOKEN
            Value: !Ref GitHubToken
            Type: PARAMETER_STORE
      Source:
        Type: CODEPIPELINE
        BuildSpec: |
          version: 0.2
          phases:
            install:
              runtime-versions:
                golang: 1.21
              commands:
                - go install github.com/blockchain-spt/cmd/spt@latest
            build:
              commands:
                - mkdir -p reports
                - spt scan --format json --output reports/security-report.json ./
                - spt audit --generate-report --report-path reports/audit-report.html ./
          artifacts:
            files:
              - 'reports/**/*'

  # CodePipeline
  SPTPipeline:
    Type: AWS::CodePipeline::Pipeline
    Properties:
      Name: !Sub '\${AWS::StackName}-spt-pipeline'
      RoleArn: !GetAtt CodePipelineRole.Arn
      ArtifactStore:
        Type: S3
        Location: !Ref ArtifactsBucket
      Stages:
        - Name: Source
          Actions:
            - Name: SourceAction
              ActionTypeId:
                Category: Source
                Owner: ThirdParty
                Provider: GitHub
                Version: '1'
              Configuration:
                Owner: !Ref GitHubOwner
                Repo: !Ref GitHubRepo
                Branch: main
                OAuthToken: !Ref GitHubToken
              OutputArtifacts:
                - Name: SourceOutput

        - Name: SecurityScan
          Actions:
            - Name: SPTScan
              ActionTypeId:
                Category: Build
                Owner: AWS
                Provider: CodeBuild
                Version: '1'
              Configuration:
                ProjectName: !Ref SPTSecurityProject
              InputArtifacts:
                - Name: SourceOutput
              OutputArtifacts:
                - Name: SecurityOutput

  # IAM Roles (simplified - add specific permissions as needed)
  CodeBuildRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: codebuild.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/CloudWatchLogsFullAccess
      Policies:
        - PolicyName: S3Access
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                Resource: !Sub '\${ArtifactsBucket}/*'

  CodePipelineRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: codepipeline.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: PipelinePolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:PutObject
                  - s3:GetBucketVersioning
                Resource:
                  - !Sub '\${ArtifactsBucket}'
                  - !Sub '\${ArtifactsBucket}/*'
              - Effect: Allow
                Action:
                  - codebuild:BatchGetBuilds
                  - codebuild:StartBuild
                Resource: !GetAtt SPTSecurityProject.Arn`,
      notes: 'Deploy using AWS CloudFormation. Customize IAM permissions based on your security requirements.'
    },
    {
      platform: 'Docker',
      description: 'Containerized SPT for consistent CI/CD environments',
      icon: 'developer_board',
      filename: 'Dockerfile',
      config: `# Multi-stage Dockerfile for SPT CLI
FROM golang:1.21-alpine AS builder

# Install dependencies
RUN apk add --no-cache git ca-certificates

# Set working directory
WORKDIR /app

# Install SPT CLI
RUN go install github.com/blockchain-spt/cmd/spt@latest

# Create final image
FROM alpine:latest

# Install runtime dependencies
RUN apk add --no-cache ca-certificates jq curl

# Copy SPT binary from builder
COPY --from=builder /go/bin/spt /usr/local/bin/spt

# Create non-root user
RUN addgroup -g 1001 spt && \\
    adduser -D -u 1001 -G spt spt

# Set working directory
WORKDIR /workspace

# Change ownership
RUN chown -R spt:spt /workspace

# Switch to non-root user
USER spt

# Set entrypoint
ENTRYPOINT ["spt"]
CMD ["--help"]

# Usage examples:
# docker build -t spt-cli .
# docker run --rm -v \$(pwd):/workspace spt-cli scan ./
# docker run --rm -v \$(pwd):/workspace spt-cli audit --generate-report ./`,
      notes: 'Use this Docker image in any CI/CD system that supports containers'
    },
    {
      platform: 'Jenkins',
      description: 'Jenkins Pipeline with SPT integration',
      icon: 'build',
      filename: 'Jenkinsfile',
      config: `pipeline {
    agent any

    environment {
        GO_VERSION = '1.21'
        SPT_VERSION = 'latest'
        REPORTS_DIR = 'reports'
    }

    tools {
        go 'go-1.21'  // Configure in Jenkins Global Tools
    }

    stages {
        stage('Checkout') {
            steps {
                checkout scm
                script {
                    env.GIT_COMMIT_SHORT = sh(
                        script: 'git rev-parse --short HEAD',
                        returnStdout: true
                    ).trim()
                }
            }
        }

        stage('Install SPT') {
            steps {
                sh '''
                    echo "Installing SPT CLI..."
                    go install github.com/blockchain-spt/cmd/spt@\${SPT_VERSION}
                    spt version
                '''
            }
        }

        stage('Security Scan') {
            steps {
                sh '''
                    echo "Creating reports directory..."
                    mkdir -p \${REPORTS_DIR}

                    echo "Running SPT security scan..."
                    spt scan --format json --output \${REPORTS_DIR}/security-report.json ./

                    echo "Generating audit report..."
                    spt audit --generate-report --report-path \${REPORTS_DIR}/audit-report.html ./

                    echo "Security scan completed"
                '''
            }
            post {
                always {
                    // Archive artifacts
                    archiveArtifacts artifacts: '\${REPORTS_DIR}/**/*', fingerprint: true

                    // Publish HTML reports
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: '\${REPORTS_DIR}',
                        reportFiles: 'audit-report.html',
                        reportName: 'SPT Security Report'
                    ])
                }
            }
        }

        stage('Evaluate Results') {
            steps {
                script {
                    if (fileExists("\${REPORTS_DIR}/security-report.json")) {
                        def report = readJSON file: "\${REPORTS_DIR}/security-report.json"
                        def critical = report.summary?.critical ?: 0
                        def high = report.summary?.high ?: 0
                        def medium = report.summary?.medium ?: 0

                        echo "Security Summary:"
                        echo "  Critical: \${critical}"
                        echo "  High: \${high}"
                        echo "  Medium: \${medium}"

                        // Set build status based on results
                        if (critical > 0) {
                            currentBuild.result = 'FAILURE'
                            error("Build failed due to \${critical} critical security issues")
                        } else if (high > 10) {
                            currentBuild.result = 'UNSTABLE'
                            echo "Build marked unstable due to \${high} high-severity issues"
                        }

                        // Add build description
                        currentBuild.description = "Critical: \${critical}, High: \${high}, Medium: \${medium}"
                    } else {
                        currentBuild.result = 'FAILURE'
                        error("Security report not found")
                    }
                }
            }
        }
    }

    post {
        always {
            // Clean workspace
            cleanWs()
        }
        failure {
            // Send notifications on failure
            emailext (
                subject: "SPT Security Scan Failed: \${env.JOB_NAME} - \${env.BUILD_NUMBER}",
                body: "Security scan failed for commit \${env.GIT_COMMIT_SHORT}. Check the build logs for details.",
                to: "\${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
        unstable {
            // Send notifications on unstable builds
            emailext (
                subject: "SPT Security Scan Unstable: \${env.JOB_NAME} - \${env.BUILD_NUMBER}",
                body: "Security scan completed with warnings for commit \${env.GIT_COMMIT_SHORT}. Review the security report.",
                to: "\${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
    }
}`,
      notes: 'Configure Go tools and email notifications in Jenkins. Install required plugins: Pipeline, HTML Publisher, Email Extension.'
    }
  ];

  cicdBestPractices = [
    {
      title: 'Fail Fast Strategy',
      description: 'Configure your pipeline to fail immediately on critical security issues to prevent vulnerable code from progressing.',
      icon: 'error',
      color: '#f44336',
      tips: [
        'Set critical severity threshold to 0',
        'Use exit codes to stop pipeline execution',
        'Implement security gates at multiple stages',
        'Configure notifications for security failures'
      ]
    },
    {
      title: 'Artifact Management',
      description: 'Properly store and manage security reports and artifacts for compliance and tracking.',
      icon: 'archive',
      color: '#2196f3',
      tips: [
        'Archive security reports for audit trails',
        'Use versioned artifact storage',
        'Implement retention policies',
        'Enable artifact encryption for sensitive data'
      ]
    },
    {
      title: 'Parallel Execution',
      description: 'Optimize scan performance by running security checks in parallel with other tests.',
      icon: 'speed',
      color: '#4caf50',
      tips: [
        'Run security scans parallel to unit tests',
        'Use matrix builds for multiple environments',
        'Implement caching for faster scans',
        'Configure resource limits appropriately'
      ]
    },
    {
      title: 'Security Thresholds',
      description: 'Define appropriate security thresholds based on your project maturity and risk tolerance.',
      icon: 'tune',
      color: '#ff9800',
      tips: [
        'Start with strict thresholds for new projects',
        'Gradually improve legacy project thresholds',
        'Use different thresholds for different branches',
        'Document threshold decisions and rationale'
      ]
    },
    {
      title: 'Integration Testing',
      description: 'Test your CI/CD integration thoroughly before deploying to production pipelines.',
      icon: 'integration_instructions',
      color: '#9c27b0',
      tips: [
        'Test with sample vulnerable code',
        'Verify artifact generation and storage',
        'Test notification mechanisms',
        'Validate security gate functionality'
      ]
    },
    {
      title: 'Monitoring & Alerting',
      description: 'Implement comprehensive monitoring and alerting for your security pipeline.',
      icon: 'monitoring',
      color: '#607d8b',
      tips: [
        'Monitor pipeline execution times',
        'Set up alerts for scan failures',
        'Track security metrics over time',
        'Implement dashboard for security trends'
      ]
    }
  ];

  environmentConfigs = [
    {
      name: 'Development Environment',
      type: 'Environment Variables',
      config: `# Development - More verbose, all severities
export SPT_SEVERITY_THRESHOLD=low
export SPT_OUTPUT_FORMAT=table
export SPT_COLORS=true
export SPT_VERBOSE=true
export SPT_PARALLEL_SCANS=2
export SPT_TIMEOUT=10m
export SPT_CACHE_ENABLED=true
export SPT_CACHE_DIR=~/.spt/cache`,
      description: 'Development environment with verbose output and lower thresholds for learning and debugging.'
    },
    {
      name: 'Staging Environment',
      type: 'Environment Variables',
      config: `# Staging - Production-like with medium threshold
export SPT_SEVERITY_THRESHOLD=medium
export SPT_OUTPUT_FORMAT=json
export SPT_COLORS=false
export SPT_VERBOSE=false
export SPT_PARALLEL_SCANS=4
export SPT_TIMEOUT=15m
export SPT_FAIL_ON_HIGH=true
export SPT_GENERATE_REPORTS=true`,
      description: 'Staging environment that mirrors production settings with moderate security requirements.'
    },
    {
      name: 'Production Environment',
      type: 'Environment Variables',
      config: `# Production - Strict security requirements
export SPT_SEVERITY_THRESHOLD=high
export SPT_OUTPUT_FORMAT=json
export SPT_COLORS=false
export SPT_VERBOSE=false
export SPT_PARALLEL_SCANS=8
export SPT_TIMEOUT=30m
export SPT_FAIL_ON_CRITICAL=true
export SPT_FAIL_ON_HIGH=true
export SPT_AUDIT_ENABLED=true
export SPT_COMPLIANCE_MODE=true`,
      description: 'Production environment with strict security requirements and comprehensive auditing.'
    },
    {
      name: 'Docker Configuration',
      type: 'Docker Environment',
      config: `# Docker container environment variables
ENV SPT_SEVERITY_THRESHOLD=medium
ENV SPT_OUTPUT_FORMAT=json
ENV SPT_PARALLEL_SCANS=4
ENV SPT_TIMEOUT=20m
ENV SPT_CACHE_ENABLED=false
ENV SPT_WORKSPACE=/workspace
ENV SPT_REPORTS_DIR=/reports

# Volume mounts
# docker run -v \$(pwd):/workspace -v \$(pwd)/reports:/reports spt-cli`,
      description: 'Containerized environment configuration for consistent cross-platform execution.'
    },
    {
      name: 'Cloud-Native Configuration',
      type: 'Kubernetes ConfigMap',
      config: `apiVersion: v1
kind: ConfigMap
metadata:
  name: spt-config
  namespace: security
data:
  SPT_SEVERITY_THRESHOLD: "medium"
  SPT_OUTPUT_FORMAT: "json"
  SPT_PARALLEL_SCANS: "6"
  SPT_TIMEOUT: "25m"
  SPT_CLOUD_STORAGE: "true"
  SPT_METRICS_ENABLED: "true"
  SPT_DISTRIBUTED_SCAN: "true"
---
apiVersion: v1
kind: Secret
metadata:
  name: spt-secrets
  namespace: security
type: Opaque
stringData:
  github-token: "your-github-token"
  api-key: "your-api-key"`,
      description: 'Kubernetes-native configuration using ConfigMaps and Secrets for cloud deployments.'
    }
  ];

  configExample = `{
  "scanning": {
    "chains": ["ethereum", "bitcoin", "general"],
    "severity_threshold": "medium",
    "max_file_size": "10MB",
    "timeout": "5m",
    "parallel_scans": 4
  },
  "output": {
    "format": "table",
    "colors": true,
    "verbose": false
  },
  "rules": {
    "ethereum": {
      "check_reentrancy": true,
      "check_overflow": true,
      "check_access_control": true
    },
    "bitcoin": {
      "check_key_management": true,
      "check_transaction_validation": true
    }
  },
  "integrations": {
    "vscode": {
      "enabled": true,
      "server_url": "http://localhost:8080"
    }
  }
}`;

  configOptions = [
    {
      key: 'scanning.chains',
      description: 'Array of blockchain chains to analyze'
    },
    {
      key: 'scanning.severity_threshold',
      description: 'Minimum severity level to report'
    },
    {
      key: 'output.format',
      description: 'Default output format for scan results'
    },
    {
      key: 'rules.ethereum',
      description: 'Ethereum-specific security rules configuration'
    },
    {
      key: 'rules.bitcoin',
      description: 'Bitcoin-specific security rules configuration'
    },
    {
      key: 'integrations',
      description: 'Configuration for IDE and tool integrations'
    }
  ];
}
