import { Component } from '@angular/core';

interface CliCommand {
  name: string;
  description: string;
  usage: string;
  options: CliOption[];
  examples: CliExample[];
  notes?: string;
}

interface CliOption {
  flag: string;
  description: string;
  type: string;
  default?: string;
  required?: boolean;
}

interface CliExample {
  command: string;
  description: string;
  output?: string;
}

@Component({
  selector: 'app-cli-guide',
  template: `
    <div class="cli-guide-container">
      <div class="page-header">
        <h1>
          <mat-icon>terminal</mat-icon>
          CLI Guide
        </h1>
        <p class="page-subtitle">
          Complete guide to the SPT command-line interface
        </p>
      </div>

      <div class="cli-overview">
        <mat-card class="overview-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>info</mat-icon>
            <mat-card-title>SPT CLI Overview</mat-card-title>
            <mat-card-subtitle>Powerful command-line security scanning tool</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <p>The SPT CLI provides comprehensive security scanning capabilities for blockchain applications directly from the command line.</p>
            <div class="cli-features">
              <div class="feature" *ngFor="let feature of cliFeatures">
                <mat-icon [style.color]="feature.color">{{ feature.icon }}</mat-icon>
                <div>
                  <strong>{{ feature.title }}</strong>
                  <p>{{ feature.description }}</p>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <mat-tab-group class="cli-tabs" animationDuration="300ms">
        <!-- Installation Tab -->
        <mat-tab label="Installation">
          <div class="tab-content">
            <h2>Installation Methods</h2>
            
            <div class="installation-methods">
              <mat-card class="method-card" *ngFor="let method of installationMethods">
                <mat-card-header>
                  <mat-icon mat-card-avatar>{{ method.icon }}</mat-icon>
                  <mat-card-title>{{ method.title }}</mat-card-title>
                  <mat-card-subtitle>{{ method.description }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="code-block">
                    <div class="code-header">
                      <mat-icon>terminal</mat-icon>
                      <span>Commands</span>
                    </div>
                    <pre><code>{{ method.commands }}</code></pre>
                  </div>
                  <div class="method-notes" *ngIf="method.notes">
                    <mat-icon>info</mat-icon>
                    <span>{{ method.notes }}</span>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Commands Tab -->
        <mat-tab label="Commands">
          <div class="tab-content">
            <h2>Available Commands</h2>
            <p>Complete reference for all SPT CLI commands and their options.</p>
            
            <div class="commands-list">
              <mat-expansion-panel 
                *ngFor="let command of cliCommands" 
                class="command-panel">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <code class="command-name">spt {{ command.name }}</code>
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ command.description }}
                  </mat-panel-description>
                </mat-expansion-panel-header>

                <div class="command-details">
                  <div class="usage-section">
                    <h4>Usage</h4>
                    <div class="code-block">
                      <pre><code>{{ command.usage }}</code></pre>
                    </div>
                  </div>

                  <div class="options-section" *ngIf="command.options.length > 0">
                    <h4>Options</h4>
                    <table mat-table [dataSource]="command.options" class="options-table">
                      <ng-container matColumnDef="flag">
                        <th mat-header-cell *matHeaderCellDef>Flag</th>
                        <td mat-cell *matCellDef="let option">
                          <code>{{ option.flag }}</code>
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="type">
                        <th mat-header-cell *matHeaderCellDef>Type</th>
                        <td mat-cell *matCellDef="let option">
                          <mat-chip>{{ option.type }}</mat-chip>
                        </td>
                      </ng-container>
                      <ng-container matColumnDef="description">
                        <th mat-header-cell *matHeaderCellDef>Description</th>
                        <td mat-cell *matCellDef="let option">{{ option.description }}</td>
                      </ng-container>
                      <ng-container matColumnDef="default">
                        <th mat-header-cell *matHeaderCellDef>Default</th>
                        <td mat-cell *matCellDef="let option">
                          <code *ngIf="option.default">{{ option.default }}</code>
                          <span *ngIf="!option.default">-</span>
                        </td>
                      </ng-container>
                      <tr mat-header-row *matHeaderRowDef="optionColumns"></tr>
                      <tr mat-row *matRowDef="let row; columns: optionColumns;"></tr>
                    </table>
                  </div>

                  <div class="examples-section" *ngIf="command.examples.length > 0">
                    <h4>Examples</h4>
                    <div class="example" *ngFor="let example of command.examples">
                      <p class="example-description">{{ example.description }}</p>
                      <div class="code-block">
                        <div class="code-header">
                          <mat-icon>terminal</mat-icon>
                          <span>Command</span>
                        </div>
                        <pre><code>{{ example.command }}</code></pre>
                      </div>
                      <div class="output-block" *ngIf="example.output">
                        <div class="code-header">
                          <mat-icon>output</mat-icon>
                          <span>Output</span>
                        </div>
                        <pre><code>{{ example.output }}</code></pre>
                      </div>
                    </div>
                  </div>

                  <div class="notes-section" *ngIf="command.notes">
                    <h4>Notes</h4>
                    <div class="notes-content">
                      <mat-icon>info</mat-icon>
                      <p>{{ command.notes }}</p>
                    </div>
                  </div>
                </div>
              </mat-expansion-panel>
            </div>
          </div>
        </mat-tab>

        <!-- Integration Tab -->
        <mat-tab label="CI/CD Integration">
          <div class="tab-content">
            <h2>CI/CD Integration</h2>
            <p>Integrate SPT CLI into your continuous integration and deployment pipelines.</p>
            
            <div class="integration-examples">
              <mat-card class="integration-card" *ngFor="let integration of integrationExamples">
                <mat-card-header>
                  <mat-icon mat-card-avatar>{{ integration.icon }}</mat-icon>
                  <mat-card-title>{{ integration.platform }}</mat-card-title>
                  <mat-card-subtitle>{{ integration.description }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="code-block">
                    <div class="code-header">
                      <mat-icon>code</mat-icon>
                      <span>{{ integration.filename }}</span>
                    </div>
                    <pre><code>{{ integration.config }}</code></pre>
                  </div>
                  <div class="integration-notes" *ngIf="integration.notes">
                    <mat-icon>lightbulb</mat-icon>
                    <span>{{ integration.notes }}</span>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>

        <!-- Configuration Tab -->
        <mat-tab label="Configuration">
          <div class="tab-content">
            <h2>Configuration</h2>
            <p>Configure SPT CLI for your development environment and preferences.</p>
            
            <mat-card class="config-card">
              <mat-card-header>
                <mat-icon mat-card-avatar>settings</mat-icon>
                <mat-card-title>Configuration File</mat-card-title>
                <mat-card-subtitle>spt.config.json</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <div class="code-block">
                  <div class="code-header">
                    <mat-icon>code</mat-icon>
                    <span>JSON Configuration</span>
                  </div>
                  <pre><code>{{ configExample }}</code></pre>
                </div>
                <div class="config-description">
                  <h4>Configuration Options</h4>
                  <div class="config-options">
                    <div class="config-option" *ngFor="let option of configOptions">
                      <strong>{{ option.key }}</strong>
                      <p>{{ option.description }}</p>
                    </div>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styles: [`
    .cli-guide-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      text-align: center;
      margin-bottom: 32px;
    }

    .page-header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      color: #1976d2;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      color: #666;
      font-size: 1.1em;
      margin: 0;
    }

    .cli-overview {
      margin-bottom: 32px;
    }

    .overview-card {
      border: 1px solid #e0e0e0;
    }

    .cli-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }

    .feature {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .feature mat-icon {
      margin-top: 2px;
    }

    .feature strong {
      display: block;
      margin-bottom: 4px;
    }

    .feature p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    .cli-tabs {
      margin-bottom: 32px;
    }

    .tab-content {
      padding: 24px 0;
    }

    .tab-content h2 {
      color: #1976d2;
      margin-bottom: 8px;
    }

    .installation-methods {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .method-card {
      height: fit-content;
    }

    .method-notes {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      padding: 8px 12px;
      background: #e3f2fd;
      border-radius: 4px;
      color: #1976d2;
    }

    .commands-list {
      margin-top: 24px;
    }

    .command-panel {
      margin-bottom: 8px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .command-name {
      background: #f5f5f5;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-weight: 500;
    }

    .command-details {
      padding: 16px 0;
    }

    .usage-section,
    .options-section,
    .examples-section,
    .notes-section {
      margin-bottom: 24px;
    }

    .usage-section h4,
    .options-section h4,
    .examples-section h4,
    .notes-section h4 {
      margin: 0 0 12px 0;
      color: #1976d2;
    }

    .code-block {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 16px;
    }

    .code-header {
      background: #f5f5f5;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      border-bottom: 1px solid #e0e0e0;
    }

    .code-block pre {
      margin: 0;
      padding: 16px;
      background: #fafafa;
      overflow-x: auto;
    }

    .code-block code {
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }

    .output-block {
      border: 1px solid #4caf50;
      border-radius: 8px;
      overflow: hidden;
      margin-top: 8px;
    }

    .output-block .code-header {
      background: #e8f5e8;
      border-bottom-color: #4caf50;
    }

    .output-block pre {
      background: #f1f8e9;
    }

    .options-table {
      width: 100%;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .options-table code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.9em;
    }

    .example {
      margin-bottom: 24px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 8px;
    }

    .example-description {
      margin: 0 0 12px 0;
      font-weight: 500;
      color: #1976d2;
    }

    .notes-content {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 12px;
      background: #e3f2fd;
      border-radius: 8px;
      color: #1976d2;
    }

    .notes-content p {
      margin: 0;
    }

    .integration-examples {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .integration-card {
      height: fit-content;
    }

    .integration-notes {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      padding: 8px 12px;
      background: #fff3e0;
      border-radius: 4px;
      color: #f57c00;
    }

    .config-card {
      margin-top: 24px;
    }

    .config-description {
      margin-top: 24px;
    }

    .config-description h4 {
      margin: 0 0 16px 0;
      color: #1976d2;
    }

    .config-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
    }

    .config-option {
      padding: 12px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .config-option strong {
      display: block;
      margin-bottom: 4px;
      color: #1976d2;
    }

    .config-option p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    @media (max-width: 768px) {
      .cli-features {
        grid-template-columns: 1fr;
      }
      
      .installation-methods {
        grid-template-columns: 1fr;
      }
      
      .integration-examples {
        grid-template-columns: 1fr;
      }
      
      .config-options {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class CliGuideComponent {
  optionColumns: string[] = ['flag', 'type', 'description', 'default'];

  cliFeatures = [
    {
      title: 'Security Scanning',
      description: 'Comprehensive security analysis for blockchain applications',
      icon: 'security',
      color: '#1976d2'
    },
    {
      title: 'Multiple Formats',
      description: 'Output results in JSON, YAML, CSV, or human-readable formats',
      icon: 'description',
      color: '#4caf50'
    },
    {
      title: 'CI/CD Integration',
      description: 'Easy integration with continuous integration pipelines',
      icon: 'integration_instructions',
      color: '#ff9800'
    },
    {
      title: 'Configurable',
      description: 'Flexible configuration options for different environments',
      icon: 'tune',
      color: '#9c27b0'
    }
  ];

  installationMethods = [
    {
      title: 'From Source',
      description: 'Build from source code',
      icon: 'code',
      commands: `git clone https://github.com/blockchain-spt/spt.git
cd spt
go build -o spt cmd/main.go`,
      notes: 'Requires Go 1.21+ to be installed'
    },
    {
      title: 'Using Make',
      description: 'Build using Makefile',
      icon: 'build',
      commands: `git clone https://github.com/blockchain-spt/spt.git
cd spt
make cli`,
      notes: 'Binary will be created in build/ directory'
    },
    {
      title: 'Go Install',
      description: 'Install directly with Go',
      icon: 'download',
      commands: `go install github.com/blockchain-spt/cmd/spt@latest`,
      notes: 'Installs to $GOPATH/bin'
    }
  ];

  cliCommands: CliCommand[] = [
    {
      name: 'scan',
      description: 'Perform security scan on files or directories',
      usage: 'spt scan [flags] [path]',
      options: [
        { flag: '--chain', type: 'string', description: 'Blockchain chain to analyze', default: 'all' },
        { flag: '--format', type: 'string', description: 'Output format (json, yaml, csv, table)', default: 'table' },
        { flag: '--output', type: 'string', description: 'Output file path' },
        { flag: '--severity', type: 'string', description: 'Minimum severity level', default: 'medium' },
        { flag: '--recursive', type: 'boolean', description: 'Scan directories recursively', default: 'true' }
      ],
      examples: [
        {
          command: 'spt scan ./contracts',
          description: 'Scan all files in contracts directory',
          output: `Scanning ./contracts...
Found 3 issues:
  HIGH: Potential reentrancy in contract.sol:42
  MEDIUM: Unchecked return value in token.sol:15
  MEDIUM: Gas optimization opportunity in utils.sol:8`
        },
        {
          command: 'spt scan --chain ethereum --format json ./src',
          description: 'Scan for Ethereum-specific issues and output as JSON'
        }
      ],
      notes: 'Use --help flag with any command to see detailed usage information'
    },
    {
      name: 'audit',
      description: 'Perform comprehensive security audit',
      usage: 'spt audit [flags] [path]',
      options: [
        { flag: '--generate-report', type: 'boolean', description: 'Generate detailed report', default: 'false' },
        { flag: '--report-path', type: 'string', description: 'Report output path', default: './audit-report.html' },
        { flag: '--template', type: 'string', description: 'Report template', default: 'standard' }
      ],
      examples: [
        {
          command: 'spt audit --generate-report ./project',
          description: 'Perform audit and generate HTML report'
        }
      ]
    },
    {
      name: 'check',
      description: 'Run specific security checks',
      usage: 'spt check [subcommand] [flags] [path]',
      options: [
        { flag: '--fix', type: 'boolean', description: 'Attempt to fix issues automatically', default: 'false' }
      ],
      examples: [
        {
          command: 'spt check deps --fix',
          description: 'Check dependencies and fix known vulnerabilities'
        },
        {
          command: 'spt check env',
          description: 'Check environment configuration for security issues'
        }
      ]
    }
  ];

  integrationExamples = [
    {
      platform: 'GitHub Actions',
      description: 'Integrate SPT into GitHub Actions workflow',
      icon: 'integration_instructions',
      filename: '.github/workflows/security.yml',
      config: `name: Security Scan
on: [push, pull_request]

jobs:
  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.21'
      - name: Install SPT
        run: go install github.com/blockchain-spt/cmd/spt@latest
      - name: Run Security Scan
        run: spt scan --format json --output security-report.json ./
      - name: Upload Results
        uses: actions/upload-artifact@v3
        with:
          name: security-report
          path: security-report.json`,
      notes: 'Add GITHUB_TOKEN to secrets for private repositories'
    },
    {
      platform: 'GitLab CI',
      description: 'Integrate SPT into GitLab CI/CD pipeline',
      icon: 'integration_instructions',
      filename: '.gitlab-ci.yml',
      config: `security_scan:
  stage: test
  image: golang:1.21
  script:
    - go install github.com/blockchain-spt/cmd/spt@latest
    - spt scan --format json --output security-report.json ./
    - spt audit --generate-report --report-path audit-report.html ./
  artifacts:
    reports:
      junit: security-report.json
    paths:
      - audit-report.html
    expire_in: 1 week
  only:
    - merge_requests
    - main`,
      notes: 'Configure artifact storage for report persistence'
    }
  ];

  configExample = `{
  "scanning": {
    "chains": ["ethereum", "bitcoin", "general"],
    "severity_threshold": "medium",
    "max_file_size": "10MB",
    "timeout": "5m",
    "parallel_scans": 4
  },
  "output": {
    "format": "table",
    "colors": true,
    "verbose": false
  },
  "rules": {
    "ethereum": {
      "check_reentrancy": true,
      "check_overflow": true,
      "check_access_control": true
    },
    "bitcoin": {
      "check_key_management": true,
      "check_transaction_validation": true
    }
  },
  "integrations": {
    "vscode": {
      "enabled": true,
      "server_url": "http://localhost:8080"
    }
  }
}`;

  configOptions = [
    {
      key: 'scanning.chains',
      description: 'Array of blockchain chains to analyze'
    },
    {
      key: 'scanning.severity_threshold',
      description: 'Minimum severity level to report'
    },
    {
      key: 'output.format',
      description: 'Default output format for scan results'
    },
    {
      key: 'rules.ethereum',
      description: 'Ethereum-specific security rules configuration'
    },
    {
      key: 'rules.bitcoin',
      description: 'Bitcoin-specific security rules configuration'
    },
    {
      key: 'integrations',
      description: 'Configuration for IDE and tool integrations'
    }
  ];
}
