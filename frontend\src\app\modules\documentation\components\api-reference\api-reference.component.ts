import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';

interface ApiEndpoint {
  method: string;
  path: string;
  description: string;
  auth: boolean;
  parameters?: any[];
  requestBody?: string;
  responseExample?: string;
  notes?: string;
}

interface ApiSection {
  title: string;
  description: string;
  endpoints: ApiEndpoint[];
}

@Component({
  selector: 'app-api-reference',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatExpansionModule,
    MatChipsModule,
    MatTableModule,
    MatTooltipModule
  ],
  template: `
    <div class="api-reference-container">
      <div class="page-header">
        <h1>
          <mat-icon>api</mat-icon>
          API Reference
        </h1>
        <p class="page-subtitle">
          Complete reference for SPT REST API endpoints
        </p>
      </div>

      <div class="api-overview">
        <mat-card class="overview-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>info</mat-icon>
            <mat-card-title>API Overview</mat-card-title>
            <mat-card-subtitle>Base URL: http://localhost:8080/api/v1</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <p>The SPT API provides RESTful endpoints for security scanning, report generation, and configuration management.</p>
            <div class="api-info-grid">
              <div class="info-item">
                <mat-icon>security</mat-icon>
                <div>
                  <strong>Authentication</strong>
                  <p>Bearer token required for protected endpoints</p>
                </div>
              </div>
              <div class="info-item">
                <mat-icon>data_object</mat-icon>
                <div>
                  <strong>Content Type</strong>
                  <p>application/json for all requests and responses</p>
                </div>
              </div>
              <div class="info-item">
                <mat-icon>speed</mat-icon>
                <div>
                  <strong>Rate Limiting</strong>
                  <p>100 requests per minute per IP</p>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="api-sections">
        <div class="api-section" *ngFor="let section of apiSections">
          <h2>{{ section.title }}</h2>
          <p class="section-description">{{ section.description }}</p>
          
          <div class="endpoints">
            <mat-expansion-panel 
              *ngFor="let endpoint of section.endpoints" 
              class="endpoint-panel">
              <mat-expansion-panel-header>
                <mat-panel-title>
                  <span class="method-badge" [class]="'method-' + endpoint.method.toLowerCase()">
                    {{ endpoint.method }}
                  </span>
                  <span class="endpoint-path">{{ endpoint.path }}</span>
                </mat-panel-title>
                <mat-panel-description>
                  {{ endpoint.description }}
                  <mat-icon *ngIf="endpoint.auth" class="auth-icon" matTooltip="Authentication required">
                    lock
                  </mat-icon>
                </mat-panel-description>
              </mat-expansion-panel-header>

              <div class="endpoint-details">
                <div class="endpoint-description">
                  <p>{{ endpoint.description }}</p>
                  <div class="auth-info" *ngIf="endpoint.auth">
                    <mat-icon>security</mat-icon>
                    <span>Authentication required</span>
                  </div>
                </div>

                <div class="parameters" *ngIf="endpoint.parameters && endpoint.parameters.length > 0">
                  <h4>Parameters</h4>
                  <table mat-table [dataSource]="endpoint.parameters" class="parameters-table">
                    <ng-container matColumnDef="name">
                      <th mat-header-cell *matHeaderCellDef>Name</th>
                      <td mat-cell *matCellDef="let param">
                        <code>{{ param.name }}</code>
                      </td>
                    </ng-container>
                    <ng-container matColumnDef="type">
                      <th mat-header-cell *matHeaderCellDef>Type</th>
                      <td mat-cell *matCellDef="let param">
                        <mat-chip>{{ param.type }}</mat-chip>
                      </td>
                    </ng-container>
                    <ng-container matColumnDef="required">
                      <th mat-header-cell *matHeaderCellDef>Required</th>
                      <td mat-cell *matCellDef="let param">
                        <mat-icon *ngIf="param.required" class="required-icon">check_circle</mat-icon>
                        <mat-icon *ngIf="!param.required" class="optional-icon">radio_button_unchecked</mat-icon>
                      </td>
                    </ng-container>
                    <ng-container matColumnDef="description">
                      <th mat-header-cell *matHeaderCellDef>Description</th>
                      <td mat-cell *matCellDef="let param">{{ param.description }}</td>
                    </ng-container>
                    <tr mat-header-row *matHeaderRowDef="parameterColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: parameterColumns;"></tr>
                  </table>
                </div>

                <div class="request-example" *ngIf="endpoint.requestBody">
                  <h4>Request Body</h4>
                  <div class="code-block">
                    <div class="code-header">
                      <mat-icon>code</mat-icon>
                      <span>JSON</span>
                    </div>
                    <pre><code>{{ endpoint.requestBody }}</code></pre>
                  </div>
                </div>

                <div class="response-example" *ngIf="endpoint.responseExample">
                  <h4>Response Example</h4>
                  <div class="code-block">
                    <div class="code-header">
                      <mat-icon>code</mat-icon>
                      <span>JSON Response</span>
                    </div>
                    <pre><code>{{ endpoint.responseExample }}</code></pre>
                  </div>
                </div>

                <div class="endpoint-notes" *ngIf="endpoint.notes">
                  <h4>Notes</h4>
                  <div class="notes-content">
                    <mat-icon>info</mat-icon>
                    <p>{{ endpoint.notes }}</p>
                  </div>
                </div>
              </div>
            </mat-expansion-panel>
          </div>
        </div>
      </div>

      <div class="websocket-section">
        <mat-card class="websocket-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>swap_horiz</mat-icon>
            <mat-card-title>WebSocket Connection</mat-card-title>
            <mat-card-subtitle>Real-time updates and notifications</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <p>Connect to the WebSocket endpoint for real-time scan updates and notifications.</p>
            <div class="websocket-info">
              <div class="ws-endpoint">
                <strong>Endpoint:</strong> <code>ws://localhost:8080/ws</code>
              </div>
              <div class="ws-example">
                <h4>Connection Example</h4>
                <div class="code-block">
                  <pre><code>{{ websocketExample }}</code></pre>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .api-reference-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      text-align: center;
      margin-bottom: 32px;
    }

    .page-header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      color: #1976d2;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      color: #666;
      font-size: 1.1em;
      margin: 0;
    }

    .api-overview {
      margin-bottom: 32px;
    }

    .overview-card {
      border: 1px solid #e0e0e0;
    }

    .api-info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }

    .info-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 12px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .info-item mat-icon {
      color: #1976d2;
      margin-top: 2px;
    }

    .info-item strong {
      display: block;
      margin-bottom: 4px;
    }

    .info-item p {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    .api-sections {
      margin-bottom: 32px;
    }

    .api-section {
      margin-bottom: 48px;
    }

    .api-section h2 {
      color: #1976d2;
      margin-bottom: 8px;
    }

    .section-description {
      color: #666;
      margin-bottom: 24px;
    }

    .endpoints {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .endpoint-panel {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .method-badge {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.8em;
      font-weight: bold;
      margin-right: 12px;
      min-width: 60px;
      text-align: center;
      display: inline-block;
    }

    .method-get { background: #4caf50; color: white; }
    .method-post { background: #2196f3; color: white; }
    .method-put { background: #ff9800; color: white; }
    .method-delete { background: #f44336; color: white; }

    .endpoint-path {
      font-family: 'Courier New', monospace;
      font-weight: 500;
    }

    .auth-icon {
      color: #ff9800;
      margin-left: auto;
    }

    .endpoint-details {
      padding: 16px 0;
    }

    .endpoint-description {
      margin-bottom: 24px;
    }

    .auth-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
      color: #ff9800;
      font-size: 0.9em;
    }

    .parameters {
      margin-bottom: 24px;
    }

    .parameters h4 {
      margin: 0 0 12px 0;
      color: #1976d2;
    }

    .parameters-table {
      width: 100%;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
    }

    .parameters-table code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.9em;
    }

    .required-icon {
      color: #4caf50;
    }

    .optional-icon {
      color: #999;
    }

    .request-example,
    .response-example {
      margin-bottom: 24px;
    }

    .request-example h4,
    .response-example h4 {
      margin: 0 0 12px 0;
      color: #1976d2;
    }

    .code-block {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }

    .code-header {
      background: #f5f5f5;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      border-bottom: 1px solid #e0e0e0;
    }

    .code-block pre {
      margin: 0;
      padding: 16px;
      background: #fafafa;
      overflow-x: auto;
    }

    .code-block code {
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }

    .endpoint-notes {
      margin-bottom: 16px;
    }

    .endpoint-notes h4 {
      margin: 0 0 12px 0;
      color: #1976d2;
    }

    .notes-content {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      padding: 12px;
      background: #e3f2fd;
      border-radius: 8px;
      color: #1976d2;
    }

    .notes-content p {
      margin: 0;
    }

    .websocket-section {
      margin-bottom: 32px;
    }

    .websocket-card {
      border: 1px solid #e0e0e0;
    }

    .websocket-info {
      margin-top: 16px;
    }

    .ws-endpoint {
      margin-bottom: 16px;
      padding: 12px;
      background: #f5f5f5;
      border-radius: 8px;
    }

    .ws-endpoint code {
      font-family: 'Courier New', monospace;
      font-weight: 500;
    }

    .ws-example h4 {
      margin: 0 0 12px 0;
      color: #1976d2;
    }

    @media (max-width: 768px) {
      .api-info-grid {
        grid-template-columns: 1fr;
      }
      
      .method-badge {
        margin-right: 8px;
        min-width: 50px;
      }
    }
  `]
})
export class ApiReferenceComponent {
  parameterColumns: string[] = ['name', 'type', 'required', 'description'];

  websocketExample = `const ws = new WebSocket('ws://localhost:8080/ws');

ws.onopen = () => {
  console.log('Connected to SPT WebSocket');
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.type === 'scan_status') {
    console.log('Scan status:', data.status);
  }
};`;

  apiSections: ApiSection[] = [
    {
      title: 'Health & Status',
      description: 'Endpoints for checking system health and status',
      endpoints: [
        {
          method: 'GET',
          path: '/health',
          description: 'Check API health status',
          auth: false,
          responseExample: `{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "uptime": "2h 15m 30s"
}`
        }
      ]
    },
    {
      title: 'Security Scanning',
      description: 'Endpoints for initiating and managing security scans',
      endpoints: [
        {
          method: 'POST',
          path: '/scan/start',
          description: 'Start a new security scan',
          auth: true,
          parameters: [
            { name: 'path', type: 'string', required: true, description: 'Path to scan' },
            { name: 'chains', type: 'array', required: false, description: 'Blockchain chains to analyze' },
            { name: 'depth', type: 'number', required: false, description: 'Scan depth level' }
          ],
          requestBody: `{
  "path": "./contracts",
  "chains": ["ethereum", "bitcoin"],
  "depth": 3,
  "options": {
    "include_dependencies": true,
    "check_vulnerabilities": true
  }
}`,
          responseExample: `{
  "scan_id": "scan_123456789",
  "status": "started",
  "estimated_duration": "5-10 minutes",
  "created_at": "2024-01-15T10:30:00Z"
}`
        },
        {
          method: 'GET',
          path: '/scan/result/:id',
          description: 'Get scan results by ID',
          auth: true,
          parameters: [
            { name: 'id', type: 'string', required: true, description: 'Scan ID' }
          ],
          responseExample: `{
  "scan_id": "scan_123456789",
  "status": "completed",
  "results": {
    "total_files": 25,
    "issues_found": 3,
    "severity_breakdown": {
      "critical": 0,
      "high": 1,
      "medium": 2,
      "low": 0
    }
  },
  "completed_at": "2024-01-15T10:35:00Z"
}`
        },
        {
          method: 'GET',
          path: '/scan/history',
          description: 'Get scan history',
          auth: true,
          parameters: [
            { name: 'limit', type: 'number', required: false, description: 'Number of results to return' },
            { name: 'offset', type: 'number', required: false, description: 'Offset for pagination' }
          ],
          responseExample: `{
  "scans": [
    {
      "scan_id": "scan_123456789",
      "status": "completed",
      "created_at": "2024-01-15T10:30:00Z",
      "issues_count": 3
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0
}`
        },
        {
          method: 'GET',
          path: '/scan/file',
          description: 'Scan a single file',
          auth: true,
          parameters: [
            { name: 'file', type: 'string', required: true, description: 'File path to scan' },
            { name: 'chains', type: 'string', required: false, description: 'Comma-separated chains' }
          ],
          responseExample: `{
  "file_path": "./contract.sol",
  "issues": [
    {
      "severity": "medium",
      "type": "reentrancy",
      "line": 42,
      "description": "Potential reentrancy vulnerability"
    }
  ],
  "scan_time": "2024-01-15T10:30:00Z"
}`
        }
      ]
    },
    {
      title: 'Reports',
      description: 'Endpoints for generating and managing security reports',
      endpoints: [
        {
          method: 'POST',
          path: '/report/generate',
          description: 'Generate a security report',
          auth: true,
          requestBody: `{
  "scan_id": "scan_123456789",
  "format": "pdf",
  "template": "executive",
  "include_recommendations": true
}`,
          responseExample: `{
  "report_id": "report_987654321",
  "status": "generating",
  "download_url": "/api/v1/report/download/report_987654321",
  "estimated_completion": "2024-01-15T10:32:00Z"
}`
        }
      ]
    },
    {
      title: 'Security Checklist',
      description: 'Endpoints for security checklist management',
      endpoints: [
        {
          method: 'GET',
          path: '/checklist',
          description: 'Get security checklist items',
          auth: true,
          parameters: [
            { name: 'chain', type: 'string', required: false, description: 'Filter by blockchain chain' }
          ],
          responseExample: `{
  "checklist": [
    {
      "id": "eth_001",
      "title": "Reentrancy Protection",
      "description": "Implement checks-effects-interactions pattern",
      "chain": "ethereum",
      "severity": "high",
      "completed": false
    }
  ],
  "total": 1,
  "completed": 0
}`
        }
      ]
    },
    {
      title: 'Configuration',
      description: 'Endpoints for system configuration management',
      endpoints: [
        {
          method: 'GET',
          path: '/config',
          description: 'Get current configuration',
          auth: true,
          responseExample: `{
  "scanning": {
    "max_concurrent_scans": 5,
    "timeout_seconds": 300
  },
  "security": {
    "cors_enabled": true
  }
}`
        },
        {
          method: 'PUT',
          path: '/config',
          description: 'Update configuration',
          auth: true,
          requestBody: `{
  "scanning": {
    "max_concurrent_scans": 10,
    "timeout_seconds": 600
  }
}`,
          responseExample: `{
  "status": "updated",
  "message": "Configuration updated successfully"
}`
        }
      ]
    }
  ];
}
