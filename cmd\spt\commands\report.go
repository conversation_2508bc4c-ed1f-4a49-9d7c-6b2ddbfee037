package commands

import (
	"encoding/json"
	"fmt"
	"html/template"
	"os"
	"path/filepath"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/spf13/cobra"
)

// ReportOptions represents report command options
type ReportOptions struct {
	Input    string
	Output   string
	Format   string
	Template string
	Title    string
	Include  []string
	Exclude  []string
	Severity string
	Detailed bool
	Summary  bool
	Charts   bool
}

// NewReportCommand creates the report command
func NewReportCommand() *cobra.Command {
	opts := &ReportOptions{}

	cmd := &cobra.Command{
		Use:   "report [input]",
		Short: "Generate security reports",
		Long: `Generate comprehensive security reports from scan results.

The report command can generate various types of reports:
- HTML reports with charts and visualizations
- PDF reports for executive summaries
- JSON/YAML reports for integration
- CSV reports for data analysis

Examples:
  spt report scan-results.json                # Generate HTML report
  spt report --format pdf --output report.pdf # Generate PDF report
  spt report --template executive results.json # Executive summary
  spt report --format csv --output data.csv   # CSV for analysis`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			if len(args) > 0 {
				opts.Input = args[0]
			}
			return runReport(cmd, opts)
		},
	}

	// Add flags
	cmd.Flags().StringVarP(&opts.Output, "output", "o", "", "Output file path")
	cmd.Flags().StringVarP(&opts.Format, "format", "f", "html", "Report format (html, pdf, json, yaml, csv)")
	cmd.Flags().StringVar(&opts.Template, "template", "standard", "Report template (standard, executive, technical)")
	cmd.Flags().StringVar(&opts.Title, "title", "Security Report", "Report title")
	cmd.Flags().StringSliceVar(&opts.Include, "include", []string{}, "Include specific issue types")
	cmd.Flags().StringSliceVar(&opts.Exclude, "exclude", []string{}, "Exclude specific issue types")
	cmd.Flags().StringVar(&opts.Severity, "severity", "", "Minimum severity level")
	cmd.Flags().BoolVar(&opts.Detailed, "detailed", false, "Include detailed issue information")
	cmd.Flags().BoolVar(&opts.Summary, "summary", true, "Include executive summary")
	cmd.Flags().BoolVar(&opts.Charts, "charts", true, "Include charts and visualizations")

	return cmd
}

// runReport executes the report command
func runReport(cmd *cobra.Command, opts *ReportOptions) error {
	// Load scan results
	var issues []models.SecurityIssue
	var err error

	if opts.Input != "" {
		issues, err = loadScanResults(opts.Input)
		if err != nil {
			return fmt.Errorf("failed to load scan results: %w", err)
		}
	} else {
		return fmt.Errorf("input file required for report generation")
	}

	// Filter issues based on options
	filteredIssues := filterReportIssues(issues, opts)

	// Set default output file if not specified
	if opts.Output == "" {
		timestamp := time.Now().Format("2006-01-02-15-04-05")
		switch opts.Format {
		case "html":
			opts.Output = fmt.Sprintf("security-report-%s.html", timestamp)
		case "pdf":
			opts.Output = fmt.Sprintf("security-report-%s.pdf", timestamp)
		case "json":
			opts.Output = fmt.Sprintf("security-report-%s.json", timestamp)
		case "yaml":
			opts.Output = fmt.Sprintf("security-report-%s.yaml", timestamp)
		case "csv":
			opts.Output = fmt.Sprintf("security-report-%s.csv", timestamp)
		default:
			opts.Output = fmt.Sprintf("security-report-%s.html", timestamp)
		}
	}

	fmt.Printf("📊 Generating %s security report...\n", strings.ToUpper(opts.Format))
	fmt.Printf("📁 Input: %s (%d issues)\n", opts.Input, len(issues))
	fmt.Printf("📄 Output: %s (%d filtered issues)\n", opts.Output, len(filteredIssues))
	fmt.Printf("📋 Template: %s\n", opts.Template)
	fmt.Printf("\n")

	// Generate report based on format
	switch strings.ToLower(opts.Format) {
	case "html":
		err = generateHTMLReport(filteredIssues, opts)
	case "pdf":
		err = generatePDFReport(filteredIssues, opts)
	case "json":
		err = generateJSONReport(filteredIssues, opts)
	case "yaml":
		err = generateYAMLReport(filteredIssues, opts)
	case "csv":
		err = generateCSVReport(filteredIssues, opts)
	default:
		return fmt.Errorf("unsupported report format: %s", opts.Format)
	}

	if err != nil {
		return fmt.Errorf("failed to generate report: %w", err)
	}

	fmt.Printf("✅ Report generated successfully: %s\n", opts.Output)
	return nil
}

// loadScanResults loads scan results from a file
func loadScanResults(filePath string) ([]models.SecurityIssue, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var result struct {
		Issues []models.SecurityIssue `json:"issues"`
	}

	if err := json.Unmarshal(data, &result); err != nil {
		return nil, err
	}

	return result.Issues, nil
}

// filterReportIssues filters issues based on report options
func filterReportIssues(issues []models.SecurityIssue, opts *ReportOptions) []models.SecurityIssue {
	var filtered []models.SecurityIssue

	for _, issue := range issues {
		// Filter by severity
		if opts.Severity != "" {
			if !meetsSeverityThreshold(issue.Severity, opts.Severity) {
				continue
			}
		}

		// Filter by include types
		if len(opts.Include) > 0 {
			included := false
			for _, includeType := range opts.Include {
				if strings.Contains(strings.ToLower(issue.Type), strings.ToLower(includeType)) {
					included = true
					break
				}
			}
			if !included {
				continue
			}
		}

		// Filter by exclude types
		excluded := false
		for _, excludeType := range opts.Exclude {
			if strings.Contains(strings.ToLower(issue.Type), strings.ToLower(excludeType)) {
				excluded = true
				break
			}
		}
		if excluded {
			continue
		}

		filtered = append(filtered, issue)
	}

	return filtered
}

// generateHTMLReport generates an HTML report
func generateHTMLReport(issues []models.SecurityIssue, opts *ReportOptions) error {
	tmpl := getHTMLTemplate(opts.Template)

	data := struct {
		Title       string
		GeneratedAt string
		Issues      []models.SecurityIssue
		Summary     ReportSummary
		Options     *ReportOptions
	}{
		Title:       opts.Title,
		GeneratedAt: time.Now().Format("2006-01-02 15:04:05"),
		Issues:      issues,
		Summary:     generateReportSummary(issues),
		Options:     opts,
	}

	file, err := os.Create(opts.Output)
	if err != nil {
		return err
	}
	defer file.Close()

	t, err := template.New("report").Parse(tmpl)
	if err != nil {
		return err
	}

	return t.Execute(file, data)
}

// generatePDFReport generates a PDF report
func generatePDFReport(issues []models.SecurityIssue, opts *ReportOptions) error {
	// For now, generate HTML and suggest conversion
	htmlFile := strings.TrimSuffix(opts.Output, filepath.Ext(opts.Output)) + ".html"

	htmlOpts := *opts
	htmlOpts.Output = htmlFile
	htmlOpts.Format = "html"

	if err := generateHTMLReport(issues, &htmlOpts); err != nil {
		return err
	}

	fmt.Printf("📝 HTML report generated: %s\n", htmlFile)
	fmt.Printf("💡 To convert to PDF, use a tool like wkhtmltopdf:\n")
	fmt.Printf("   wkhtmltopdf %s %s\n", htmlFile, opts.Output)

	return nil
}

// generateJSONReport generates a JSON report
func generateJSONReport(issues []models.SecurityIssue, opts *ReportOptions) error {
	report := struct {
		Title       string                 `json:"title"`
		GeneratedAt string                 `json:"generated_at"`
		Summary     ReportSummary          `json:"summary"`
		Issues      []models.SecurityIssue `json:"issues"`
		Metadata    map[string]interface{} `json:"metadata"`
	}{
		Title:       opts.Title,
		GeneratedAt: time.Now().Format(time.RFC3339),
		Summary:     generateReportSummary(issues),
		Issues:      issues,
		Metadata: map[string]interface{}{
			"template": opts.Template,
			"format":   opts.Format,
			"filters": map[string]interface{}{
				"severity": opts.Severity,
				"include":  opts.Include,
				"exclude":  opts.Exclude,
			},
		},
	}

	file, err := os.Create(opts.Output)
	if err != nil {
		return err
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	return encoder.Encode(report)
}

// generateYAMLReport generates a YAML report
func generateYAMLReport(issues []models.SecurityIssue, opts *ReportOptions) error {
	// For simplicity, generate JSON and suggest conversion
	jsonFile := strings.TrimSuffix(opts.Output, filepath.Ext(opts.Output)) + ".json"

	jsonOpts := *opts
	jsonOpts.Output = jsonFile
	jsonOpts.Format = "json"

	if err := generateJSONReport(issues, &jsonOpts); err != nil {
		return err
	}

	fmt.Printf("📝 JSON report generated: %s\n", jsonFile)
	fmt.Printf("💡 Convert to YAML using: yq eval -P %s > %s\n", jsonFile, opts.Output)

	return nil
}

// generateCSVReport generates a CSV report
func generateCSVReport(issues []models.SecurityIssue, opts *ReportOptions) error {
	file, err := os.Create(opts.Output)
	if err != nil {
		return err
	}
	defer file.Close()

	// Write CSV header
	header := "Severity,Type,File,Line,Title,Description,Chain,Category,CWE,OWASP,Suggestion\n"
	if _, err := file.WriteString(header); err != nil {
		return err
	}

	// Write data rows
	for _, issue := range issues {
		row := fmt.Sprintf("%s,%s,%s,%d,%s,%s,%s,%s,%s,%s,%s\n",
			escapeCSV(issue.Severity),
			escapeCSV(issue.Type),
			escapeCSV(issue.File),
			issue.Line,
			escapeCSV(issue.Title),
			escapeCSV(issue.Description),
			escapeCSV(issue.Chain),
			escapeCSV(issue.Category),
			escapeCSV(issue.CWE),
			escapeCSV(issue.OWASP),
			escapeCSV(issue.Suggestion),
		)
		if _, err := file.WriteString(row); err != nil {
			return err
		}
	}

	return nil
}

// ReportSummary represents report summary data
type ReportSummary struct {
	TotalIssues    int            `json:"total_issues"`
	CriticalIssues int            `json:"critical_issues"`
	HighIssues     int            `json:"high_issues"`
	MediumIssues   int            `json:"medium_issues"`
	LowIssues      int            `json:"low_issues"`
	ChainBreakdown map[string]int `json:"chain_breakdown"`
	TypeBreakdown  map[string]int `json:"type_breakdown"`
	FilesAffected  int            `json:"files_affected"`
}

// generateReportSummary generates summary statistics
func generateReportSummary(issues []models.SecurityIssue) ReportSummary {
	summary := ReportSummary{
		TotalIssues:    len(issues),
		ChainBreakdown: make(map[string]int),
		TypeBreakdown:  make(map[string]int),
	}

	files := make(map[string]bool)

	for _, issue := range issues {
		// Count by severity
		switch strings.ToLower(issue.Severity) {
		case "critical":
			summary.CriticalIssues++
		case "high":
			summary.HighIssues++
		case "medium":
			summary.MediumIssues++
		case "low":
			summary.LowIssues++
		}

		// Count by chain
		summary.ChainBreakdown[issue.Chain]++

		// Count by type
		summary.TypeBreakdown[issue.Type]++

		// Track affected files
		files[issue.File] = true
	}

	summary.FilesAffected = len(files)
	return summary
}

// getHTMLTemplate returns the HTML template based on template type
func getHTMLTemplate(templateType string) string {
	switch templateType {
	case "executive":
		return getExecutiveHTMLTemplate()
	case "technical":
		return getTechnicalHTMLTemplate()
	default:
		return getStandardHTMLTemplate()
	}
}

// getStandardHTMLTemplate returns the standard HTML template
func getStandardHTMLTemplate() string {
	return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #eee; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 6px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .number { font-size: 2em; font-weight: bold; color: #007bff; }
        .issue { margin: 20px 0; padding: 20px; border-left: 4px solid #ccc; background: #fafafa; border-radius: 4px; }
        .critical { border-left-color: #dc3545; }
        .high { border-left-color: #fd7e14; }
        .medium { border-left-color: #ffc107; }
        .low { border-left-color: #28a745; }
        .issue-header { display: flex; justify-content: between; align-items: center; margin-bottom: 10px; }
        .severity { padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.8em; font-weight: bold; }
        .severity.critical { background: #dc3545; }
        .severity.high { background: #fd7e14; }
        .severity.medium { background: #ffc107; color: #333; }
        .severity.low { background: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{.Title}}</h1>
            <p>Generated on {{.GeneratedAt}}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>Total Issues</h3>
                <div class="number">{{.Summary.TotalIssues}}</div>
            </div>
            <div class="summary-card">
                <h3>Critical</h3>
                <div class="number" style="color: #dc3545;">{{.Summary.CriticalIssues}}</div>
            </div>
            <div class="summary-card">
                <h3>High</h3>
                <div class="number" style="color: #fd7e14;">{{.Summary.HighIssues}}</div>
            </div>
            <div class="summary-card">
                <h3>Files Affected</h3>
                <div class="number">{{.Summary.FilesAffected}}</div>
            </div>
        </div>

        <h2>Security Issues</h2>
        {{range .Issues}}
        <div class="issue {{.Severity}}">
            <div class="issue-header">
                <h3>{{.Title}}</h3>
                <span class="severity {{.Severity}}">{{.Severity}}</span>
            </div>
            <p><strong>File:</strong> {{.File}}:{{.Line}}</p>
            <p><strong>Type:</strong> {{.Type}}</p>
            <p><strong>Description:</strong> {{.Description}}</p>
            {{if .Suggestion}}<p><strong>Suggestion:</strong> {{.Suggestion}}</p>{{end}}
        </div>
        {{end}}
    </div>
</body>
</html>`
}

// getExecutiveHTMLTemplate returns the executive summary template
func getExecutiveHTMLTemplate() string {
	// Simplified executive template
	return getStandardHTMLTemplate()
}

// getTechnicalHTMLTemplate returns the technical details template
func getTechnicalHTMLTemplate() string {
	// Enhanced technical template
	return getStandardHTMLTemplate()
}

// Helper functions

func meetsSeverityThreshold(issueSeverity, minSeverity string) bool {
	severityLevels := map[string]int{
		"low":      1,
		"medium":   2,
		"high":     3,
		"critical": 4,
	}

	issueLevel := severityLevels[strings.ToLower(issueSeverity)]
	minLevel := severityLevels[strings.ToLower(minSeverity)]

	return issueLevel >= minLevel
}

func escapeCSV(s string) string {
	if strings.Contains(s, ",") || strings.Contains(s, "\"") || strings.Contains(s, "\n") {
		s = strings.ReplaceAll(s, "\"", "\"\"")
		return "\"" + s + "\""
	}
	return s
}
