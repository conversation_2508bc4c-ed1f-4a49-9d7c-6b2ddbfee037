package storage

import (
	"fmt"
	"sort"
	"sync"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// MemoryStorage provides an in-memory storage implementation
type MemoryStorage struct {
	scanResults       map[string]*models.ScanResult
	securityIssues    map[string]*models.SecurityIssue
	securityReports   map[string]*models.SecurityReport
	projectConfigs    map[string]*models.ProjectConfig
	securityChecklist map[string]*models.SecurityChecklist
	walletInfo        map[string]*models.WalletInfo
	contractInfo      map[string]*models.ContractInfo
	dependencyInfo    map[string]*models.DependencyInfo
	mutex             sync.RWMutex
	logger            *logrus.Logger
}

// NewMemoryStorage creates a new in-memory storage instance
func NewMemoryStorage() *MemoryStorage {
	logger := logrus.New()
	logger.SetFormatter(&logrus.JSONFormatter{})

	return &MemoryStorage{
		scanResults:       make(map[string]*models.ScanResult),
		securityIssues:    make(map[string]*models.SecurityIssue),
		securityReports:   make(map[string]*models.SecurityReport),
		projectConfigs:    make(map[string]*models.ProjectConfig),
		securityChecklist: make(map[string]*models.SecurityChecklist),
		walletInfo:        make(map[string]*models.WalletInfo),
		contractInfo:      make(map[string]*models.ContractInfo),
		dependencyInfo:    make(map[string]*models.DependencyInfo),
		logger:            logger,
	}
}

// Health checks storage connectivity (always returns nil for memory storage)
func (m *MemoryStorage) Health() error {
	return nil
}

// Close closes the storage (no-op for memory storage)
func (m *MemoryStorage) Close() error {
	return nil
}

// ScanResult operations
func (m *MemoryStorage) CreateScanResult(result *models.ScanResult) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if result.ID == "" {
		return fmt.Errorf("scan result ID cannot be empty")
	}

	// Set timestamps
	now := time.Now()
	if result.CreatedAt.IsZero() {
		result.CreatedAt = now
	}
	result.UpdatedAt = now

	m.scanResults[result.ID] = result
	m.logger.WithField("scan_id", result.ID).Debug("Created scan result in memory storage")
	return nil
}

func (m *MemoryStorage) GetScanResult(id string) (*models.ScanResult, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result, exists := m.scanResults[id]
	if !exists {
		return nil, fmt.Errorf("scan result not found: %s", id)
	}

	// Return a copy to avoid race conditions
	resultCopy := *result
	return &resultCopy, nil
}

func (m *MemoryStorage) UpdateScanResult(result *models.ScanResult) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if result.ID == "" {
		return fmt.Errorf("scan result ID cannot be empty")
	}

	// Update timestamp
	result.UpdatedAt = time.Now()

	m.scanResults[result.ID] = result
	m.logger.WithField("scan_id", result.ID).Debug("Updated scan result in memory storage")
	return nil
}

func (m *MemoryStorage) GetScanHistory(limit int) ([]*models.ScanResult, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// Convert map to slice
	results := make([]*models.ScanResult, 0, len(m.scanResults))
	for _, result := range m.scanResults {
		// Create a copy
		resultCopy := *result
		results = append(results, &resultCopy)
	}

	// Sort by created_at descending
	sort.Slice(results, func(i, j int) bool {
		return results[i].CreatedAt.After(results[j].CreatedAt)
	})

	// Apply limit
	if limit > 0 && len(results) > limit {
		results = results[:limit]
	}

	return results, nil
}

func (m *MemoryStorage) DeleteScanResult(id string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if _, exists := m.scanResults[id]; !exists {
		return fmt.Errorf("scan result not found: %s", id)
	}

	delete(m.scanResults, id)
	m.logger.WithField("scan_id", id).Debug("Deleted scan result from memory storage")
	return nil
}

// SecurityIssue operations
func (m *MemoryStorage) CreateSecurityIssue(issue *models.SecurityIssue) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if issue.ID == "" {
		return fmt.Errorf("security issue ID cannot be empty")
	}

	now := time.Now()
	if issue.CreatedAt.IsZero() {
		issue.CreatedAt = now
	}
	issue.UpdatedAt = now

	m.securityIssues[issue.ID] = issue
	return nil
}

func (m *MemoryStorage) GetSecurityIssuesByType(issueType string) ([]*models.SecurityIssue, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var issues []*models.SecurityIssue
	for _, issue := range m.securityIssues {
		if issue.Type == issueType {
			issueCopy := *issue
			issues = append(issues, &issueCopy)
		}
	}

	return issues, nil
}

func (m *MemoryStorage) GetSecurityIssuesBySeverity(severity string) ([]*models.SecurityIssue, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var issues []*models.SecurityIssue
	for _, issue := range m.securityIssues {
		if issue.Severity == severity {
			issueCopy := *issue
			issues = append(issues, &issueCopy)
		}
	}

	return issues, nil
}

// SecurityReport operations
func (m *MemoryStorage) CreateSecurityReport(report *models.SecurityReport) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if report.ID == "" {
		return fmt.Errorf("security report ID cannot be empty")
	}

	now := time.Now()
	if report.CreatedAt.IsZero() {
		report.CreatedAt = now
	}
	report.UpdatedAt = now

	m.securityReports[report.ID] = report
	return nil
}

func (m *MemoryStorage) GetSecurityReport(id string) (*models.SecurityReport, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	report, exists := m.securityReports[id]
	if !exists {
		return nil, fmt.Errorf("security report not found: %s", id)
	}

	reportCopy := *report
	return &reportCopy, nil
}

func (m *MemoryStorage) GetReportsByScanID(scanID string) ([]*models.SecurityReport, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	var reports []*models.SecurityReport
	for _, report := range m.securityReports {
		if report.ScanResultID == scanID {
			reportCopy := *report
			reports = append(reports, &reportCopy)
		}
	}

	return reports, nil
}

// ProjectConfig operations
func (m *MemoryStorage) CreateProjectConfig(config *models.ProjectConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if config.ID == "" {
		return fmt.Errorf("project config ID cannot be empty")
	}

	now := time.Now()
	if config.CreatedAt.IsZero() {
		config.CreatedAt = now
	}
	config.UpdatedAt = now

	m.projectConfigs[config.ProjectPath] = config
	return nil
}

func (m *MemoryStorage) GetProjectConfig(projectPath string) (*models.ProjectConfig, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	config, exists := m.projectConfigs[projectPath]
	if !exists {
		return nil, fmt.Errorf("project config not found for path: %s", projectPath)
	}

	configCopy := *config
	return &configCopy, nil
}

func (m *MemoryStorage) UpdateProjectConfig(config *models.ProjectConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if config.ProjectPath == "" {
		return fmt.Errorf("project path cannot be empty")
	}

	config.UpdatedAt = time.Now()
	m.projectConfigs[config.ProjectPath] = config
	return nil
}

// GetScanStatistics returns basic statistics
func (m *MemoryStorage) GetScanStatistics() (map[string]interface{}, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["total_scans"] = len(m.scanResults)
	stats["total_issues"] = len(m.securityIssues)
	stats["total_reports"] = len(m.securityReports)

	// Count by status
	statusCounts := make(map[string]int)
	for _, result := range m.scanResults {
		statusCounts[string(result.Status)]++
	}
	stats["scans_by_status"] = statusCounts

	// Count by severity
	severityCounts := make(map[string]int)
	for _, issue := range m.securityIssues {
		severityCounts[issue.Severity]++
	}
	stats["issues_by_severity"] = severityCounts

	return stats, nil
}

// Additional placeholder methods to match database interface
func (m *MemoryStorage) CreateSecurityChecklist(checklist *models.SecurityChecklist) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.securityChecklist[checklist.ID] = checklist
	return nil
}

func (m *MemoryStorage) GetSecurityChecklist(chain string, category string) ([]*models.SecurityChecklist, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	var checklist []*models.SecurityChecklist
	for _, item := range m.securityChecklist {
		if (item.Chain == chain || item.Chain == "general") && (category == "" || item.Category == category) {
			itemCopy := *item
			checklist = append(checklist, &itemCopy)
		}
	}
	return checklist, nil
}

func (m *MemoryStorage) UpdateChecklistItem(id string, status string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if item, exists := m.securityChecklist[id]; exists {
		item.Status = status
		item.UpdatedAt = time.Now()
	}
	return nil
}

func (m *MemoryStorage) CreateWalletInfo(wallet *models.WalletInfo) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.walletInfo[wallet.ID] = wallet
	return nil
}

func (m *MemoryStorage) GetExposedWallets() ([]*models.WalletInfo, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	var wallets []*models.WalletInfo
	for _, wallet := range m.walletInfo {
		if wallet.IsExposed {
			walletCopy := *wallet
			wallets = append(wallets, &walletCopy)
		}
	}
	return wallets, nil
}

func (m *MemoryStorage) CreateContractInfo(contract *models.ContractInfo) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.contractInfo[contract.ID] = contract
	return nil
}

func (m *MemoryStorage) GetContractsByChain(chain string) ([]*models.ContractInfo, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	var contracts []*models.ContractInfo
	for _, contract := range m.contractInfo {
		if contract.Chain == chain {
			contractCopy := *contract
			contracts = append(contracts, &contractCopy)
		}
	}
	return contracts, nil
}

func (m *MemoryStorage) CreateDependencyInfo(dependency *models.DependencyInfo) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.dependencyInfo[dependency.ID] = dependency
	return nil
}

func (m *MemoryStorage) GetVulnerableDependencies() ([]*models.DependencyInfo, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	var dependencies []*models.DependencyInfo
	for _, dep := range m.dependencyInfo {
		if dep.HasVulnerability {
			depCopy := *dep
			dependencies = append(dependencies, &depCopy)
		}
	}
	return dependencies, nil
}
