import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';

interface NavItem {
  label: string;
  route: string;
  icon: string;
  description: string;
}

@Component({
  selector: 'app-documentation-nav',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatDividerModule
  ],
  template: `
    <div class="nav-container">
      <div class="nav-header">
        <div class="nav-header-icon">
          <mat-icon>menu_book</mat-icon>
        </div>
        <div class="nav-header-text">
          <h3>Documentation</h3>
          <p class="nav-subtitle">Comprehensive guide to SPT</p>
        </div>
      </div>

      <mat-nav-list class="nav-list">
        <mat-list-item
          *ngFor="let item of navItems"
          [routerLink]="['/doc', item.route]"
          routerLinkActive="active-nav-item"
          class="nav-item">
          <div class="nav-item-icon">
            <mat-icon>{{ item.icon }}</mat-icon>
          </div>
          <div class="nav-item-content">
            <div class="nav-item-title">{{ item.label }}</div>
            <div class="nav-description">{{ item.description }}</div>
          </div>
        </mat-list-item>
      </mat-nav-list>

      <div class="nav-divider"></div>

      <div class="nav-footer">
        <div class="version-info">
          <div class="version-icon">
            <mat-icon>verified</mat-icon>
          </div>
          <div class="version-text">
            <span class="version-number">SPT v1.0.0</span>
            <span class="version-status">Stable Release</span>
          </div>
        </div>
        <div class="quick-links">
          <a href="https://github.com/blockchain-spt" target="_blank" mat-button class="github-link">
            <mat-icon>code</mat-icon>
            <span>GitHub</span>
          </a>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .nav-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 32px 0;
    }

    .nav-header {
      padding: 0 32px 32px;
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      gap: 16px;
      position: relative;
    }

    .nav-header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 32px;
      right: 32px;
      height: 1px;
      background: #e2e8f0;
    }

    .nav-header-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .nav-header-icon mat-icon {
      color: white;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .nav-header-text h3 {
      margin: 0 0 4px 0;
      color: #1a202c;
      font-weight: 700;
      font-size: 1.25em;
      letter-spacing: -0.5px;
    }

    .nav-subtitle {
      margin: 0;
      color: #64748b;
      font-size: 0.875em;
      font-weight: 500;
    }

    .nav-list {
      flex: 1;
      padding: 0 24px;
    }

    .nav-item {
      margin: 8px 0;
      border-radius: 12px;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
      background: #ffffff;
      border: 1px solid #f1f5f9;
    }



    .nav-item:hover {
      background: #f8fafc;
      border-color: #e2e8f0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      transform: translateY(-1px);
    }

    .nav-item.active-nav-item {
      background: #f0f9ff;
      border-color: #0ea5e9;
      border-left: 4px solid #0ea5e9;
      box-shadow: 0 2px 8px rgba(14, 165, 233, 0.15);
    }

    .nav-item-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 10px;
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      position: relative;
      z-index: 1;
      min-width: 40px;
      min-height: 40px;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
    }

    .nav-item.active-nav-item .nav-item-icon {
      background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
      box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
    }

    .nav-item-icon mat-icon {
      color: white;
      font-size: 20px;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .nav-item-content {
      display: flex;
      flex-direction: column;
      flex: 1;
    }

    .nav-item-title {
      font-weight: 600;
      color: #1a202c;
      font-size: 0.95em;
      position: relative;
      z-index: 1;
      margin: 0;
    }

    .nav-item.active-nav-item .nav-item-title {
      color: #0ea5e9;
    }

    .nav-description {
      font-size: 0.8em;
      color: #64748b;
      margin-top: 4px;
      line-height: 1.3;
      position: relative;
      z-index: 1;
    }

    .nav-item.active-nav-item .nav-description {
      color: #0284c7;
    }

    .nav-divider {
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, #e8eaff 20%, #d1d9ff 50%, #e8eaff 80%, transparent 100%);
      margin: 20px 24px;
    }

    .nav-footer {
      padding: 20px 24px 0;
    }

    .version-info {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      padding: 12px;
      background: linear-gradient(135deg, #f8f9ff 0%, #e8eaff 100%);
      border-radius: 12px;
      border: 1px solid #e8eaff;
    }

    .version-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      border-radius: 8px;
      padding: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .version-icon mat-icon {
      color: white;
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .version-text {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .version-number {
      font-weight: 600;
      color: #4c63d2;
      font-size: 0.9em;
    }

    .version-status {
      font-size: 0.75em;
      color: #8892b0;
      font-weight: 500;
    }

    .quick-links {
      display: flex;
      gap: 8px;
    }

    .github-link {
      background: linear-gradient(135deg, #24292e 0%, #1a1e22 100%);
      color: white;
      border-radius: 10px;
      padding: 8px 16px;
      font-weight: 500;
      transition: all 0.3s ease;
      border: none;
      width: 100%;
      justify-content: flex-start;
    }

    .github-link:hover {
      background: linear-gradient(135deg, #2f363d 0%, #24292e 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(36, 41, 46, 0.3);
    }

    .github-link mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .github-link span {
      font-size: 0.9em;
    }

    @media (max-width: 768px) {
      .nav-container {
        padding: 16px 0;
      }

      .nav-header {
        padding: 0 16px 16px;
        margin-bottom: 16px;
        gap: 12px;
      }

      .nav-header::after {
        left: 16px;
        right: 16px;
      }

      .nav-header-icon {
        padding: 8px;
      }

      .nav-header-icon mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      .nav-list {
        padding: 0 8px;
      }

      .nav-divider {
        margin: 16px 16px;
      }

      .nav-footer {
        padding: 16px 16px 0;
      }

      .version-info {
        padding: 10px;
      }
    }
  `]
})
export class DocumentationNavComponent {
  navItems: NavItem[] = [
    {
      label: 'Overview',
      route: 'overview',
      icon: 'home',
      description: 'Introduction to SPT'
    },
    {
      label: 'Getting Started',
      route: 'getting-started',
      icon: 'play_arrow',
      description: 'Installation and setup'
    },
    {
      label: 'API Reference',
      route: 'api-reference',
      icon: 'api',
      description: 'REST API endpoints'
    },
    {
      label: 'Security Practices',
      route: 'security-practices',
      icon: 'security',
      description: 'Best practices guide'
    },
    {
      label: 'CLI Guide',
      route: 'cli-guide',
      icon: 'terminal',
      description: 'Command line interface'
    },
    {
      label: 'VS Code Extension',
      route: 'vscode-extension',
      icon: 'extension',
      description: 'IDE integration'
    },
    {
      label: 'Architecture',
      route: 'architecture',
      icon: 'account_tree',
      description: 'System architecture'
    }
  ];
}
