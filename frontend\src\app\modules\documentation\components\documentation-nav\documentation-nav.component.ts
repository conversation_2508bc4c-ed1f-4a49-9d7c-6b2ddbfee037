import { Component } from '@angular/core';

interface NavItem {
  label: string;
  route: string;
  icon: string;
  description: string;
}

@Component({
  selector: 'app-documentation-nav',
  template: `
    <div class="nav-container">
      <div class="nav-header">
        <h3>Documentation</h3>
        <p class="nav-subtitle">Comprehensive guide to SPT</p>
      </div>

      <mat-nav-list class="nav-list">
        <mat-list-item 
          *ngFor="let item of navItems" 
          [routerLink]="['/doc', item.route]"
          routerLinkActive="active-nav-item"
          class="nav-item">
          <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>
          <div matListItemTitle>{{ item.label }}</div>
          <div matListItemLine class="nav-description">{{ item.description }}</div>
        </mat-list-item>
      </mat-nav-list>

      <mat-divider></mat-divider>

      <div class="nav-footer">
        <div class="version-info">
          <mat-icon>info</mat-icon>
          <span>SPT v1.0.0</span>
        </div>
        <div class="quick-links">
          <a href="https://github.com/blockchain-spt" target="_blank" mat-button>
            <mat-icon>code</mat-icon>
            GitHub
          </a>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .nav-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 16px 0;
    }

    .nav-header {
      padding: 0 16px 16px;
      border-bottom: 1px solid #e0e0e0;
      margin-bottom: 16px;
    }

    .nav-header h3 {
      margin: 0 0 4px 0;
      color: #1976d2;
      font-weight: 500;
    }

    .nav-subtitle {
      margin: 0;
      color: #666;
      font-size: 0.9em;
    }

    .nav-list {
      flex: 1;
      padding: 0;
    }

    .nav-item {
      margin: 4px 8px;
      border-radius: 8px;
      transition: all 0.2s ease;
    }

    .nav-item:hover {
      background-color: rgba(25, 118, 210, 0.08);
    }

    .nav-item.active-nav-item {
      background-color: rgba(25, 118, 210, 0.12);
      color: #1976d2;
    }

    .nav-item.active-nav-item mat-icon {
      color: #1976d2;
    }

    .nav-description {
      font-size: 0.8em;
      color: #666;
      margin-top: 2px;
    }

    .nav-footer {
      padding: 16px;
      border-top: 1px solid #e0e0e0;
      margin-top: 16px;
    }

    .version-info {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      color: #666;
      font-size: 0.9em;
    }

    .version-info mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .quick-links {
      display: flex;
      gap: 8px;
    }

    .quick-links a {
      font-size: 0.9em;
    }

    .quick-links mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
  `]
})
export class DocumentationNavComponent {
  navItems: NavItem[] = [
    {
      label: 'Overview',
      route: 'overview',
      icon: 'home',
      description: 'Introduction to SPT'
    },
    {
      label: 'Getting Started',
      route: 'getting-started',
      icon: 'play_arrow',
      description: 'Installation and setup'
    },
    {
      label: 'API Reference',
      route: 'api-reference',
      icon: 'api',
      description: 'REST API endpoints'
    },
    {
      label: 'Security Practices',
      route: 'security-practices',
      icon: 'security',
      description: 'Best practices guide'
    },
    {
      label: 'CLI Guide',
      route: 'cli-guide',
      icon: 'terminal',
      description: 'Command line interface'
    },
    {
      label: 'VS Code Extension',
      route: 'vscode-extension',
      icon: 'extension',
      description: 'IDE integration'
    },
    {
      label: 'Architecture',
      route: 'architecture',
      icon: 'account_tree',
      description: 'System architecture'
    }
  ];
}
