import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';

interface NavItem {
  label: string;
  route: string;
  icon: string;
  description: string;
}

@Component({
  selector: 'app-documentation-nav',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatDividerModule
  ],
  template: `
    <nav class="spt-nav-container">
      <!-- Navigation Header -->
      <header class="spt-nav-header">
        <div class="spt-nav-brand">
          <div class="spt-nav-icon">
            <mat-icon>menu_book</mat-icon>
          </div>
          <div class="spt-nav-text">
            <h3 class="spt-nav-title">Documentation</h3>
            <p class="spt-nav-subtitle">Comprehensive SPT Guide</p>
          </div>
        </div>
      </header>

      <!-- Navigation Menu -->
      <div class="spt-nav-menu">
        <div class="spt-nav-section">
          <h4 class="spt-nav-section-title">Getting Started</h4>
          <ul class="spt-nav-list">
            <li *ngFor="let item of getStartedItems" class="spt-nav-item">
              <a
                [routerLink]="['/doc', item.route]"
                routerLinkActive="spt-nav-active"
                class="spt-nav-link">
                <div class="spt-nav-link-icon">
                  <mat-icon>{{ item.icon }}</mat-icon>
                </div>
                <div class="spt-nav-link-content">
                  <span class="spt-nav-link-title">{{ item.label }}</span>
                  <span class="spt-nav-link-desc">{{ item.description }}</span>
                </div>
              </a>
            </li>
          </ul>
        </div>

        <div class="spt-nav-section">
          <h4 class="spt-nav-section-title">Development</h4>
          <ul class="spt-nav-list">
            <li *ngFor="let item of developmentItems" class="spt-nav-item">
              <a
                [routerLink]="['/doc', item.route]"
                routerLinkActive="spt-nav-active"
                class="spt-nav-link">
                <div class="spt-nav-link-icon">
                  <mat-icon>{{ item.icon }}</mat-icon>
                </div>
                <div class="spt-nav-link-content">
                  <span class="spt-nav-link-title">{{ item.label }}</span>
                  <span class="spt-nav-link-desc">{{ item.description }}</span>
                </div>
              </a>
            </li>
          </ul>
        </div>

        <div class="spt-nav-section">
          <h4 class="spt-nav-section-title">Reference</h4>
          <ul class="spt-nav-list">
            <li *ngFor="let item of referenceItems" class="spt-nav-item">
              <a
                [routerLink]="['/doc', item.route]"
                routerLinkActive="spt-nav-active"
                class="spt-nav-link">
                <div class="spt-nav-link-icon">
                  <mat-icon>{{ item.icon }}</mat-icon>
                </div>
                <div class="spt-nav-link-content">
                  <span class="spt-nav-link-title">{{ item.label }}</span>
                  <span class="spt-nav-link-desc">{{ item.description }}</span>
                </div>
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Navigation Footer -->
      <footer class="spt-nav-footer">
        <div class="spt-version-card">
          <div class="spt-version-icon">
            <mat-icon>verified</mat-icon>
          </div>
          <div class="spt-version-info">
            <span class="spt-version-number">SPT v1.0.0</span>
            <span class="spt-version-status">Stable Release</span>
          </div>
        </div>

        <div class="spt-nav-links">
          <a href="https://github.com/blockchain-spt" target="_blank" class="spt-external-link">
            <mat-icon>code</mat-icon>
            <span>GitHub Repository</span>
          </a>
          <a href="#" class="spt-external-link">
            <mat-icon>help</mat-icon>
            <span>Support</span>
          </a>
        </div>
      </footer>
    </nav>
  `,
  styles: [`
    /* Navigation Container */
    .spt-nav-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      background: white;
      font-family: 'Inter', sans-serif;
    }

    /* Navigation Header */
    .spt-nav-header {
      padding: var(--spt-space-6);
      border-bottom: 1px solid var(--spt-gray-200);
      background: var(--spt-gray-50);
    }

    .spt-nav-brand {
      display: flex;
      align-items: center;
      gap: var(--spt-space-3);
    }

    .spt-nav-icon {
      background: linear-gradient(135deg, var(--spt-primary-600) 0%, var(--spt-secondary-600) 100%);
      border-radius: var(--spt-radius-xl);
      padding: var(--spt-space-3);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: var(--spt-shadow-md);
    }

    .spt-nav-icon mat-icon {
      color: white;
      font-size: 24px;
      width: 24px;
      height: 24px;
    }

    .spt-nav-text {
      flex: 1;
    }

    .spt-nav-title {
      font-size: var(--spt-text-lg);
      font-weight: var(--spt-font-bold);
      color: var(--spt-gray-900);
      margin: 0 0 var(--spt-space-1) 0;
      letter-spacing: -0.025em;
    }

    .spt-nav-subtitle {
      font-size: var(--spt-text-sm);
      color: var(--spt-gray-600);
      margin: 0;
      font-weight: var(--spt-font-medium);
    }

    /* Navigation Menu */
    .spt-nav-menu {
      flex: 1;
      overflow-y: auto;
      padding: var(--spt-space-4) 0;
    }

    .spt-nav-section {
      margin-bottom: var(--spt-space-6);
    }

    .spt-nav-section-title {
      font-size: var(--spt-text-xs);
      font-weight: var(--spt-font-semibold);
      color: var(--spt-gray-500);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin: 0 0 var(--spt-space-3) 0;
      padding: 0 var(--spt-space-6);
    }

    .spt-nav-list {
      list-style: none;
      margin: 0;
      padding: 0 var(--spt-space-4);
    }

    .spt-nav-item {
      margin-bottom: var(--spt-space-1);
    }

    /* Navigation Links */
    .spt-nav-link {
      display: flex;
      align-items: center;
      gap: var(--spt-space-3);
      padding: var(--spt-space-3) var(--spt-space-4);
      border-radius: var(--spt-radius-lg);
      text-decoration: none;
      color: var(--spt-gray-700);
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
    }

    .spt-nav-link:hover {
      background: var(--spt-gray-100);
      color: var(--spt-gray-900);
      transform: translateX(2px);
    }

    .spt-nav-link.spt-nav-active {
      background: var(--spt-primary-50);
      color: var(--spt-primary-700);
      border-left: 3px solid var(--spt-primary-600);
      box-shadow: var(--spt-shadow-sm);
    }

    .spt-nav-link.spt-nav-active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: var(--spt-primary-600);
    }

    .spt-nav-link-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: var(--spt-radius-lg);
      background: var(--spt-gray-200);
      transition: all 0.2s ease;
    }

    .spt-nav-link:hover .spt-nav-link-icon {
      background: var(--spt-primary-100);
      color: var(--spt-primary-600);
    }

    .spt-nav-link.spt-nav-active .spt-nav-link-icon {
      background: var(--spt-primary-600);
      color: white;
      box-shadow: var(--spt-shadow-sm);
    }

    .spt-nav-link-icon mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    .spt-nav-link-content {
      display: flex;
      flex-direction: column;
      gap: var(--spt-space-1);
      flex: 1;
    }

    .spt-nav-link-title {
      font-size: var(--spt-text-sm);
      font-weight: var(--spt-font-medium);
      line-height: 1.2;
    }

    .spt-nav-link-desc {
      font-size: var(--spt-text-xs);
      color: var(--spt-gray-500);
      line-height: 1.3;
    }

    .spt-nav-link.spt-nav-active .spt-nav-link-desc {
      color: var(--spt-primary-600);
    }

    /* Navigation Footer */
    .spt-nav-footer {
      padding: var(--spt-space-4) var(--spt-space-6) var(--spt-space-6);
      border-top: 1px solid var(--spt-gray-200);
      background: var(--spt-gray-50);
    }

    .spt-version-card {
      display: flex;
      align-items: center;
      gap: var(--spt-space-3);
      padding: var(--spt-space-4);
      background: white;
      border-radius: var(--spt-radius-xl);
      border: 1px solid var(--spt-gray-200);
      box-shadow: var(--spt-shadow-sm);
      margin-bottom: var(--spt-space-4);
    }

    .spt-version-icon {
      background: linear-gradient(135deg, var(--spt-success-500) 0%, var(--spt-success-600) 100%);
      border-radius: var(--spt-radius-lg);
      padding: var(--spt-space-2);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .spt-version-icon mat-icon {
      color: white;
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    .spt-version-info {
      display: flex;
      flex-direction: column;
      gap: var(--spt-space-1);
    }

    .spt-version-number {
      font-size: var(--spt-text-sm);
      font-weight: var(--spt-font-semibold);
      color: var(--spt-gray-900);
    }

    .spt-version-status {
      font-size: var(--spt-text-xs);
      color: var(--spt-success-600);
      font-weight: var(--spt-font-medium);
    }

    .spt-nav-links {
      display: flex;
      flex-direction: column;
      gap: var(--spt-space-2);
    }

    .spt-external-link {
      display: flex;
      align-items: center;
      gap: var(--spt-space-2);
      padding: var(--spt-space-2) var(--spt-space-3);
      border-radius: var(--spt-radius-lg);
      text-decoration: none;
      color: var(--spt-gray-600);
      font-size: var(--spt-text-sm);
      font-weight: var(--spt-font-medium);
      transition: all 0.2s ease;
      border: 1px solid var(--spt-gray-200);
      background: white;
    }

    .spt-external-link:hover {
      background: var(--spt-gray-100);
      color: var(--spt-gray-900);
      border-color: var(--spt-gray-300);
      transform: translateY(-1px);
      box-shadow: var(--spt-shadow-sm);
    }

    .spt-external-link mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }

    /* Global Icon Fixes */
    mat-icon {
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      vertical-align: middle !important;
      line-height: 1 !important;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .spt-nav-header {
        padding: var(--spt-space-4);
      }

      .spt-nav-icon {
        padding: var(--spt-space-2);
      }

      .spt-nav-icon mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      .spt-nav-title {
        font-size: var(--spt-text-base);
      }

      .spt-nav-subtitle {
        font-size: var(--spt-text-xs);
      }

      .spt-nav-list {
        padding: 0 var(--spt-space-3);
      }

      .spt-nav-footer {
        padding: var(--spt-space-3) var(--spt-space-4) var(--spt-space-4);
      }

      .spt-version-card {
        padding: var(--spt-space-3);
      }
    }

    @media (max-width: 480px) {
      .spt-nav-header {
        padding: var(--spt-space-3);
      }

      .spt-nav-brand {
        gap: var(--spt-space-2);
      }

      .spt-nav-list {
        padding: 0 var(--spt-space-2);
      }

      .spt-nav-link {
        padding: var(--spt-space-2) var(--spt-space-3);
      }

      .spt-nav-link-icon {
        width: 32px;
        height: 32px;
      }

      .spt-nav-link-icon mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }

      .spt-nav-footer {
        padding: var(--spt-space-2) var(--spt-space-3) var(--spt-space-3);
      }
    }
  `]
})
export class DocumentationNavComponent {
  getStartedItems: NavItem[] = [
    {
      label: 'Overview',
      route: 'overview',
      icon: 'home',
      description: 'Introduction to SPT'
    },
    {
      label: 'Getting Started',
      route: 'getting-started',
      icon: 'play_arrow',
      description: 'Installation and setup'
    },
    {
      label: 'Security Practices',
      route: 'security-practices',
      icon: 'security',
      description: 'Best practices guide'
    }
  ];

  developmentItems: NavItem[] = [
    {
      label: 'CLI Guide',
      route: 'cli-guide',
      icon: 'terminal',
      description: 'Command line interface'
    },
    {
      label: 'VS Code Extension',
      route: 'vscode-extension',
      icon: 'extension',
      description: 'IDE integration'
    }
  ];

  referenceItems: NavItem[] = [
    {
      label: 'API Reference',
      route: 'api-reference',
      icon: 'api',
      description: 'REST API endpoints'
    },
    {
      label: 'Architecture',
      route: 'architecture',
      icon: 'account_tree',
      description: 'System architecture'
    }
  ];
}
