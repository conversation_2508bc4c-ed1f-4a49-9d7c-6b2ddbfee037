package dependencies

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// PipAnalyzer analyzes pip requirements files
type PipAnalyzer struct {
	logger *logrus.Logger
}

// PoetryAnalyzer analyzes Poetry pyproject.toml files
type PoetryAnalyzer struct {
	logger *logrus.Logger
}

// NewPipAnalyzer creates a new pip analyzer
func NewPipAnalyzer() *PipAnalyzer {
	return &PipAnalyzer{
		logger: logrus.New(),
	}
}

// NewPoetryAnalyzer creates a new Poetry analyzer
func NewPoetryAnalyzer() *PoetryAnalyzer {
	return &PoetryAnalyzer{
		logger: logrus.New(),
	}
}

// AnalyzePackageFile analyzes pip requirements files
func (pip *PipAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	fileName := strings.ToLower(filePath)

	if strings.Contains(fileName, "requirements") && strings.HasSuffix(fileName, ".txt") {
		reqIssues := pip.analyzeRequirements(filePath, content)
		issues = append(issues, reqIssues...)
	}

	if strings.Contains(fileName, "pipfile") {
		pipfileIssues := pip.analyzePipfile(filePath, content)
		issues = append(issues, pipfileIssues...)
	}

	return issues, nil
}

// analyzeRequirements analyzes requirements.txt files
func (pip *PipAnalyzer) analyzeRequirements(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Parse package line
		pkg := pip.parsePackageLine(line)
		if pkg.Name == "" {
			continue
		}

		// Check for unpinned versions
		if pkg.Version == "" || strings.Contains(pkg.Version, "*") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("pip_unpinned_%s_%d", pkg.Name, i+1),
				Type:        "unpinned_dependency",
				Severity:    "medium",
				Title:       "Unpinned Python Package",
				Description: fmt.Sprintf("Package '%s' is not pinned to a specific version", pkg.Name),
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Pin packages to specific versions for reproducible builds",
				References:  []string{"https://pip.pypa.io/en/stable/user_guide/#requirements-files"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for insecure sources
		if strings.HasPrefix(line, "http://") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("pip_insecure_source_%d", i+1),
				Type:        "insecure_source",
				Severity:    "high",
				Title:       "Insecure Package Source",
				Description: "Package is downloaded from insecure HTTP source",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS sources for package downloads",
				References:  []string{"https://pip.pypa.io/en/stable/user_guide/#requirements-files"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for git dependencies
		if strings.Contains(line, "git+") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("pip_git_dep_%s_%d", pkg.Name, i+1),
				Type:        "git_dependency",
				Severity:    "low",
				Title:       "Git Dependency",
				Description: fmt.Sprintf("Package '%s' is installed from git repository", pkg.Name),
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Consider using published PyPI packages for better security",
				References:  []string{"https://pip.pypa.io/en/stable/user_guide/#vcs-support"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for known vulnerable packages
		if pip.isVulnerablePackage(pkg.Name, pkg.Version) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("pip_vulnerable_%s", pkg.Name),
				Type:        "vulnerable_dependency",
				Severity:    "high",
				Title:       "Vulnerable Python Package",
				Description: fmt.Sprintf("Package '%s' has known security vulnerabilities", pkg.Name),
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Update to latest secure version",
				References:  []string{"https://pyup.io/safety/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for deprecated packages
		if pip.isDeprecatedPackage(pkg.Name) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("pip_deprecated_%s", pkg.Name),
				Type:        "deprecated_dependency",
				Severity:    "medium",
				Title:       "Deprecated Python Package",
				Description: fmt.Sprintf("Package '%s' is deprecated", pkg.Name),
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  pip.getDeprecationSuggestion(pkg.Name),
				References:  []string{"https://pypi.org/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// analyzePipfile analyzes Pipfile for security issues
func (pip *PipAnalyzer) analyzePipfile(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Check for insecure PyPI sources
		if strings.Contains(line, "url") && strings.Contains(line, "http://") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("pipfile_insecure_url_%d", i+1),
				Type:        "insecure_source",
				Severity:    "high",
				Title:       "Insecure PyPI Source",
				Description: "Pipfile contains insecure HTTP PyPI source",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS for PyPI sources",
				References:  []string{"https://pipenv.pypa.io/en/latest/basics/#specifying-package-indexes"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}

		// Check for allow_prereleases
		if strings.Contains(line, "allow_prereleases") && strings.Contains(line, "true") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("pipfile_prereleases_%d", i+1),
				Type:        "prerelease_packages",
				Severity:    "low",
				Title:       "Prerelease Packages Allowed",
				Description: "Pipfile allows prerelease packages which may be unstable",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Avoid prerelease packages in production",
				References:  []string{"https://pipenv.pypa.io/en/latest/basics/#specifying-package-indexes"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// Package represents a parsed package requirement
type Package struct {
	Name    string
	Version string
	Extras  []string
	Source  string
}

// parsePackageLine parses a package line from requirements.txt
func (pip *PipAnalyzer) parsePackageLine(line string) Package {
	pkg := Package{}

	// Remove comments
	if idx := strings.Index(line, "#"); idx != -1 {
		line = line[:idx]
	}
	line = strings.TrimSpace(line)

	// Handle git URLs
	if strings.Contains(line, "git+") {
		pkg.Source = "git"
		// Extract package name from git URL
		if strings.Contains(line, "egg=") {
			parts := strings.Split(line, "egg=")
			if len(parts) > 1 {
				pkg.Name = strings.Split(parts[1], "&")[0]
			}
		}
		return pkg
	}

	// Handle HTTP URLs
	if strings.HasPrefix(line, "http") {
		pkg.Source = "url"
		return pkg
	}

	// Handle local paths
	if strings.HasPrefix(line, ".") || strings.HasPrefix(line, "/") {
		pkg.Source = "local"
		return pkg
	}

	// Parse standard package specification
	// Format: package[extras]==version
	re := regexp.MustCompile(`^([a-zA-Z0-9_-]+)(\[[^\]]+\])?(.*)?$`)
	matches := re.FindStringSubmatch(line)

	if len(matches) > 1 {
		pkg.Name = matches[1]

		if len(matches) > 2 && matches[2] != "" {
			// Parse extras
			extras := strings.Trim(matches[2], "[]")
			pkg.Extras = strings.Split(extras, ",")
		}

		if len(matches) > 3 && matches[3] != "" {
			// Parse version specifier
			versionSpec := strings.TrimSpace(matches[3])
			// Remove operators like ==, >=, ~=, etc.
			versionRe := regexp.MustCompile(`[><=~!]+(.+)`)
			versionMatches := versionRe.FindStringSubmatch(versionSpec)
			if len(versionMatches) > 1 {
				pkg.Version = versionMatches[1]
			}
		}
	}

	return pkg
}

// isVulnerablePackage checks if a package has known vulnerabilities
func (pip *PipAnalyzer) isVulnerablePackage(name, version string) bool {
	vulnerablePackages := map[string][]string{
		"django":       {"<2.2.25", "<3.1.14", "<3.2.8"},
		"requests":     {"<2.25.2"},
		"urllib3":      {"<1.26.5"},
		"pillow":       {"<8.2.0"},
		"pyyaml":       {"<5.4"},
		"jinja2":       {"<2.11.3"},
		"werkzeug":     {"<2.0.0"},
		"flask":        {"<1.1.4"},
		"cryptography": {"<3.4.8"},
		"paramiko":     {"<2.7.2"},
	}

	if versions, exists := vulnerablePackages[strings.ToLower(name)]; exists {
		// Simplified version check
		for _, vulnVersion := range versions {
			if strings.HasPrefix(vulnVersion, "<") {
				return true // In real implementation, use proper version comparison
			}
		}
	}

	return false
}

// isDeprecatedPackage checks if a package is deprecated
func (pip *PipAnalyzer) isDeprecatedPackage(name string) bool {
	deprecated := []string{
		"pycrypto", "sha", "md5", "optparse", "imp", "formatter",
		"distutils", "distutils2", "distribute", "setuptools-scm",
	}

	for _, dep := range deprecated {
		if strings.ToLower(name) == dep {
			return true
		}
	}
	return false
}

// getDeprecationSuggestion returns suggestion for deprecated package
func (pip *PipAnalyzer) getDeprecationSuggestion(name string) string {
	suggestions := map[string]string{
		"pycrypto":   "Use pycryptodome instead",
		"sha":        "Use hashlib instead",
		"md5":        "Use hashlib instead",
		"optparse":   "Use argparse instead",
		"imp":        "Use importlib instead",
		"distutils":  "Use setuptools instead",
		"distribute": "Use setuptools instead",
	}

	if suggestion, exists := suggestions[strings.ToLower(name)]; exists {
		return suggestion
	}
	return "Find alternative package or remove if unused"
}

// GetSupportedFiles returns supported file patterns
func (pip *PipAnalyzer) GetSupportedFiles() []string {
	return []string{"requirements.txt", "requirements-dev.txt", "requirements-test.txt", "Pipfile"}
}

// GetPackageManager returns the package manager name
func (pip *PipAnalyzer) GetPackageManager() string {
	return "pip"
}

// Poetry analyzer methods

// AnalyzePackageFile analyzes Poetry pyproject.toml files
func (poetry *PoetryAnalyzer) AnalyzePackageFile(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	if strings.Contains(filePath, "pyproject.toml") {
		tomlIssues := poetry.analyzePyprojectToml(filePath, content)
		issues = append(issues, tomlIssues...)
	}

	return issues, nil
}

// analyzePyprojectToml analyzes pyproject.toml for security issues
func (poetry *PoetryAnalyzer) analyzePyprojectToml(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")
	inDependencies := false
	inDevDependencies := false

	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		// Track sections
		if strings.Contains(line, "[tool.poetry.dependencies]") {
			inDependencies = true
			inDevDependencies = false
			continue
		}
		if strings.Contains(line, "[tool.poetry.dev-dependencies]") || strings.Contains(line, "[tool.poetry.group.dev.dependencies]") {
			inDependencies = false
			inDevDependencies = true
			continue
		}
		if strings.HasPrefix(line, "[") {
			inDependencies = false
			inDevDependencies = false
			continue
		}

		if inDependencies || inDevDependencies {
			// Parse dependency line
			if strings.Contains(line, "=") {
				parts := strings.SplitN(line, "=", 2)
				if len(parts) == 2 {
					packageName := strings.TrimSpace(parts[0])
					versionSpec := strings.TrimSpace(parts[1])

					// Remove quotes
					versionSpec = strings.Trim(versionSpec, "\"'")

					// Check for wildcard versions
					if strings.Contains(versionSpec, "*") {
						issues = append(issues, models.SecurityIssue{
							ID:          fmt.Sprintf("poetry_wildcard_%s_%d", packageName, i+1),
							Type:        "wildcard_dependency",
							Severity:    "medium",
							Title:       "Wildcard Dependency Version",
							Description: fmt.Sprintf("Package '%s' uses wildcard version", packageName),
							File:        filePath,
							Line:        i + 1,
							Code:        line,
							Chain:       "general",
							Category:    "dependency",
							Suggestion:  "Pin to specific version ranges",
							References:  []string{"https://python-poetry.org/docs/dependency-specification/"},
							CreatedAt:   time.Now(),
							UpdatedAt:   time.Now(),
						})
					}

					// Check for git dependencies
					if strings.Contains(versionSpec, "git") {
						issues = append(issues, models.SecurityIssue{
							ID:          fmt.Sprintf("poetry_git_dep_%s_%d", packageName, i+1),
							Type:        "git_dependency",
							Severity:    "low",
							Title:       "Git Dependency",
							Description: fmt.Sprintf("Package '%s' is installed from git", packageName),
							File:        filePath,
							Line:        i + 1,
							Code:        line,
							Chain:       "general",
							Category:    "dependency",
							Suggestion:  "Consider using published PyPI packages",
							References:  []string{"https://python-poetry.org/docs/dependency-specification/"},
							CreatedAt:   time.Now(),
							UpdatedAt:   time.Now(),
						})
					}
				}
			}
		}

		// Check for insecure sources
		if strings.Contains(line, "url") && strings.Contains(line, "http://") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("poetry_insecure_url_%d", i+1),
				Type:        "insecure_source",
				Severity:    "high",
				Title:       "Insecure Package Source",
				Description: "Poetry configuration contains insecure HTTP source",
				File:        filePath,
				Line:        i + 1,
				Code:        line,
				Chain:       "general",
				Category:    "dependency",
				Suggestion:  "Use HTTPS for package sources",
				References:  []string{"https://python-poetry.org/docs/repositories/"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// GetSupportedFiles returns supported file patterns
func (poetry *PoetryAnalyzer) GetSupportedFiles() []string {
	return []string{"pyproject.toml"}
}

// GetPackageManager returns the package manager name
func (poetry *PoetryAnalyzer) GetPackageManager() string {
	return "poetry"
}
