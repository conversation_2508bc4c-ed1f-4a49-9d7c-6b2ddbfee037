package bitcoin

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// UTXOAnalyzer analyzes Bitcoin UTXO patterns and security
type UTXOAnalyzer struct {
	logger *logrus.Logger
}

// UTXO represents an Unspent Transaction Output
type UTXO struct {
	TxID          string
	OutputIndex   int
	Value         int64
	ScriptPubKey  string
	Address       string
	Confirmations int
	Spendable     bool
	Solvable      bool
	Safe          bool
}

// Transaction represents a Bitcoin transaction
type Transaction struct {
	TxID     string
	Version  int
	Inputs   []TransactionInput
	Outputs  []TransactionOutput
	LockTime int64
	Size     int
	Fee      int64
}

// TransactionInput represents a transaction input
type TransactionInput struct {
	PrevTxID    string
	OutputIndex int
	ScriptSig   string
	Sequence    uint32
	Witness     []string
}

// TransactionOutput represents a transaction output
type TransactionOutput struct {
	Value        int64
	ScriptPubKey string
	Address      string
}

// UTXOSet represents a collection of UTXOs
type UTXOSet struct {
	UTXOs        []UTXO
	TotalValue   int64
	Count        int
	DustOutputs  int
	LargeOutputs int
}

// NewUTXOAnalyzer creates a new UTXO analyzer
func NewUTXOAnalyzer() *UTXOAnalyzer {
	return &UTXOAnalyzer{
		logger: logrus.New(),
	}
}

// AnalyzeUTXOs analyzes UTXO patterns for security issues
func (ua *UTXOAnalyzer) AnalyzeUTXOs(filePath string, content string) ([]models.SecurityIssue, error) {
	var issues []models.SecurityIssue

	// Extract UTXO patterns from content
	utxos := ua.extractUTXOs(content)
	transactions := ua.extractTransactions(content)

	// Analyze UTXO set
	if len(utxos) > 0 {
		utxoSet := ua.buildUTXOSet(utxos)
		issues = append(issues, ua.analyzeUTXOSet(utxoSet, filePath)...)
	}

	// Analyze transactions
	for _, tx := range transactions {
		issues = append(issues, ua.analyzeTransaction(tx, filePath)...)
	}

	// Check for UTXO-related security patterns
	issues = append(issues, ua.checkUTXOPatterns(filePath, content)...)

	return issues, nil
}

// extractUTXOs extracts UTXO information from code content
func (ua *UTXOAnalyzer) extractUTXOs(content string) []UTXO {
	var utxos []UTXO

	// Pattern for UTXO objects/structures
	utxoPattern := regexp.MustCompile(`(?i)utxo|unspent.*output`)

	// Pattern for transaction IDs
	txidPattern := regexp.MustCompile(`(?i)([0-9a-f]{64})`)

	// Pattern for Bitcoin amounts (in satoshis or BTC)
	// amountPattern := regexp.MustCompile(`(?i)(amount|value|satoshi|btc)\s*[:=]\s*([0-9]+(?:\.[0-9]+)?)`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if utxoPattern.MatchString(line) {
			// Try to extract UTXO information from this line and surrounding context
			utxo := ua.parseUTXOFromContext(lines, i)
			if utxo.TxID != "" {
				utxos = append(utxos, utxo)
			}
		}
	}

	// Also look for transaction ID patterns
	txidMatches := txidPattern.FindAllString(content, -1)
	for _, txid := range txidMatches {
		// Create minimal UTXO entry for analysis
		utxo := UTXO{
			TxID:        txid,
			OutputIndex: 0, // Default
			Spendable:   true,
			Solvable:    true,
			Safe:        true,
		}
		utxos = append(utxos, utxo)
	}

	return utxos
}

// extractTransactions extracts transaction information from code content
func (ua *UTXOAnalyzer) extractTransactions(content string) []Transaction {
	var transactions []Transaction

	// Pattern for transaction structures
	txPattern := regexp.MustCompile(`(?i)transaction|tx`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if txPattern.MatchString(line) {
			tx := ua.parseTransactionFromContext(lines, i)
			if tx.TxID != "" {
				transactions = append(transactions, tx)
			}
		}
	}

	return transactions
}

// parseUTXOFromContext parses UTXO information from surrounding context
func (ua *UTXOAnalyzer) parseUTXOFromContext(lines []string, startLine int) UTXO {
	utxo := UTXO{
		Spendable: true,
		Solvable:  true,
		Safe:      true,
	}

	// Look at surrounding lines for UTXO details
	start := startLine - 2
	end := startLine + 3
	if start < 0 {
		start = 0
	}
	if end >= len(lines) {
		end = len(lines) - 1
	}

	context := strings.Join(lines[start:end], " ")

	// Extract transaction ID
	txidPattern := regexp.MustCompile(`(?i)([0-9a-f]{64})`)
	if matches := txidPattern.FindStringSubmatch(context); len(matches) > 1 {
		utxo.TxID = matches[1]
	}

	// Extract output index
	indexPattern := regexp.MustCompile(`(?i)(index|vout)\s*[:=]\s*([0-9]+)`)
	if matches := indexPattern.FindStringSubmatch(context); len(matches) > 2 {
		if idx, err := strconv.Atoi(matches[2]); err == nil {
			utxo.OutputIndex = idx
		}
	}

	// Extract value
	valuePattern := regexp.MustCompile(`(?i)(value|amount|satoshi)\s*[:=]\s*([0-9]+)`)
	if matches := valuePattern.FindStringSubmatch(context); len(matches) > 2 {
		if val, err := strconv.ParseInt(matches[2], 10, 64); err == nil {
			utxo.Value = val
		}
	}

	// Extract confirmations
	confirmPattern := regexp.MustCompile(`(?i)confirm(?:ation)?s?\s*[:=]\s*([0-9]+)`)
	if matches := confirmPattern.FindStringSubmatch(context); len(matches) > 1 {
		if conf, err := strconv.Atoi(matches[1]); err == nil {
			utxo.Confirmations = conf
		}
	}

	return utxo
}

// parseTransactionFromContext parses transaction information from context
func (ua *UTXOAnalyzer) parseTransactionFromContext(lines []string, startLine int) Transaction {
	tx := Transaction{
		Version: 1, // Default
	}

	// Look at surrounding lines for transaction details
	start := startLine - 3
	end := startLine + 10
	if start < 0 {
		start = 0
	}
	if end >= len(lines) {
		end = len(lines) - 1
	}

	context := strings.Join(lines[start:end], " ")

	// Extract transaction ID
	txidPattern := regexp.MustCompile(`(?i)([0-9a-f]{64})`)
	if matches := txidPattern.FindStringSubmatch(context); len(matches) > 1 {
		tx.TxID = matches[1]
	}

	// Extract fee
	feePattern := regexp.MustCompile(`(?i)fee\s*[:=]\s*([0-9]+)`)
	if matches := feePattern.FindStringSubmatch(context); len(matches) > 1 {
		if fee, err := strconv.ParseInt(matches[1], 10, 64); err == nil {
			tx.Fee = fee
		}
	}

	// Extract size
	sizePattern := regexp.MustCompile(`(?i)size\s*[:=]\s*([0-9]+)`)
	if matches := sizePattern.FindStringSubmatch(context); len(matches) > 1 {
		if size, err := strconv.Atoi(matches[1]); err == nil {
			tx.Size = size
		}
	}

	return tx
}

// buildUTXOSet builds a UTXO set from individual UTXOs
func (ua *UTXOAnalyzer) buildUTXOSet(utxos []UTXO) UTXOSet {
	set := UTXOSet{
		UTXOs: utxos,
		Count: len(utxos),
	}

	dustThreshold := int64(546)        // Standard dust threshold in satoshis
	largeThreshold := int64(100000000) // 1 BTC in satoshis

	for _, utxo := range utxos {
		set.TotalValue += utxo.Value

		if utxo.Value > 0 && utxo.Value < dustThreshold {
			set.DustOutputs++
		}

		if utxo.Value >= largeThreshold {
			set.LargeOutputs++
		}
	}

	return set
}

// analyzeUTXOSet analyzes a UTXO set for security issues
func (ua *UTXOAnalyzer) analyzeUTXOSet(utxoSet UTXOSet, filePath string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check for dust outputs
	if utxoSet.DustOutputs > 0 {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_dust_outputs_%d", time.Now().UnixNano()),
			Type:        "dust_outputs",
			Severity:    "low",
			Title:       "Dust Outputs Detected",
			Description: fmt.Sprintf("Found %d dust outputs (< 546 satoshis) that may be uneconomical to spend", utxoSet.DustOutputs),
			File:        filePath,
			Line:        1,
			Code:        fmt.Sprintf("Dust outputs: %d/%d", utxoSet.DustOutputs, utxoSet.Count),
			Chain:       "bitcoin",
			Category:    "wallet",
			Suggestion:  "Consolidate dust outputs or avoid creating outputs below dust threshold",
			References:  []string{"https://bitcoin.org/en/glossary/dust"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// Check for UTXO fragmentation
	if utxoSet.Count > 100 {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_utxo_fragmentation_%d", time.Now().UnixNano()),
			Type:        "utxo_fragmentation",
			Severity:    "medium",
			Title:       "High UTXO Fragmentation",
			Description: fmt.Sprintf("Wallet has %d UTXOs which may lead to high transaction fees", utxoSet.Count),
			File:        filePath,
			Line:        1,
			Code:        fmt.Sprintf("UTXO count: %d", utxoSet.Count),
			Chain:       "bitcoin",
			Category:    "wallet",
			Suggestion:  "Consider consolidating UTXOs during low fee periods",
			References:  []string{"https://bitcoin.org/en/developer-guide#transaction-fees-and-change"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// Check for large outputs (potential privacy issue)
	if utxoSet.LargeOutputs > 0 {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_large_outputs_%d", time.Now().UnixNano()),
			Type:        "large_outputs",
			Severity:    "low",
			Title:       "Large Output Values",
			Description: fmt.Sprintf("Found %d outputs >= 1 BTC which may impact privacy", utxoSet.LargeOutputs),
			File:        filePath,
			Line:        1,
			Code:        fmt.Sprintf("Large outputs: %d", utxoSet.LargeOutputs),
			Chain:       "bitcoin",
			Category:    "wallet",
			Suggestion:  "Consider breaking large outputs into smaller amounts for better privacy",
			References:  []string{"https://en.bitcoin.it/wiki/Privacy"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	// Check for unconfirmed UTXOs
	unconfirmedCount := 0
	for _, utxo := range utxoSet.UTXOs {
		if utxo.Confirmations == 0 {
			unconfirmedCount++
		}
	}

	if unconfirmedCount > 0 {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_unconfirmed_utxos_%d", time.Now().UnixNano()),
			Type:        "unconfirmed_utxos",
			Severity:    "medium",
			Title:       "Unconfirmed UTXOs",
			Description: fmt.Sprintf("Found %d unconfirmed UTXOs which may be subject to double-spending", unconfirmedCount),
			File:        filePath,
			Line:        1,
			Code:        fmt.Sprintf("Unconfirmed UTXOs: %d/%d", unconfirmedCount, utxoSet.Count),
			Chain:       "bitcoin",
			Category:    "wallet",
			Suggestion:  "Wait for confirmations before considering UTXOs as spendable",
			References:  []string{"https://bitcoin.org/en/developer-guide#transaction-confirmation"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return issues
}

// analyzeTransaction analyzes a transaction for security issues
func (ua *UTXOAnalyzer) analyzeTransaction(tx Transaction, filePath string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Check for high fees
	if tx.Fee > 0 && tx.Size > 0 {
		feeRate := float64(tx.Fee) / float64(tx.Size) // satoshis per byte

		if feeRate > 100 { // > 100 sat/byte is considered high
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_high_fee_%s", tx.TxID),
				Type:        "high_transaction_fee",
				Severity:    "low",
				Title:       "High Transaction Fee",
				Description: fmt.Sprintf("Transaction fee rate of %.2f sat/byte is unusually high", feeRate),
				File:        filePath,
				Line:        1,
				Code:        fmt.Sprintf("Fee: %d satoshis, Size: %d bytes", tx.Fee, tx.Size),
				Chain:       "bitcoin",
				Category:    "wallet",
				Suggestion:  "Review fee calculation to avoid overpaying",
				References:  []string{"https://bitcoin.org/en/developer-guide#transaction-fees-and-change"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	// Check for RBF (Replace-By-Fee) vulnerability
	for _, input := range tx.Inputs {
		if input.Sequence < 0xfffffffe {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_rbf_enabled_%s", tx.TxID),
				Type:        "rbf_enabled",
				Severity:    "low",
				Title:       "RBF Enabled Transaction",
				Description: "Transaction has Replace-By-Fee enabled, allowing fee bumping but reducing finality",
				File:        filePath,
				Line:        1,
				Code:        fmt.Sprintf("Sequence: 0x%x", input.Sequence),
				Chain:       "bitcoin",
				Category:    "wallet",
				Suggestion:  "Be aware that RBF transactions can be replaced until confirmed",
				References:  []string{"https://bitcoin.org/en/developer-guide#replace-by-fee-rbf"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
			break // Only report once per transaction
		}
	}

	// Check for version 1 transactions with timelock
	if tx.Version == 1 && tx.LockTime > 0 {
		issues = append(issues, models.SecurityIssue{
			ID:          fmt.Sprintf("btc_v1_timelock_%s", tx.TxID),
			Type:        "version1_timelock",
			Severity:    "low",
			Title:       "Version 1 Transaction with Timelock",
			Description: "Version 1 transaction uses locktime, consider using version 2 for better timelock support",
			File:        filePath,
			Line:        1,
			Code:        fmt.Sprintf("Version: %d, LockTime: %d", tx.Version, tx.LockTime),
			Chain:       "bitcoin",
			Category:    "smart_contract",
			Suggestion:  "Use transaction version 2 for timelock functionality",
			References:  []string{"https://bitcoin.org/en/developer-guide#locktime-and-sequence-number"},
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		})
	}

	return issues
}

// checkUTXOPatterns checks for UTXO-related security patterns in code
func (ua *UTXOAnalyzer) checkUTXOPatterns(filePath string, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	lines := strings.Split(content, "\n")

	// Check for hardcoded transaction IDs
	hardcodedTxPattern := regexp.MustCompile(`(?i)(txid|transaction_id|hash)\s*[:=]\s*["']([0-9a-f]{64})["']`)

	for i, line := range lines {
		if matches := hardcodedTxPattern.FindStringSubmatch(line); len(matches) > 2 {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_hardcoded_txid_%d", i+1),
				Type:        "hardcoded_txid",
				Severity:    "low",
				Title:       "Hardcoded Transaction ID",
				Description: "Transaction ID is hardcoded in source code",
				File:        filePath,
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Chain:       "bitcoin",
				Category:    "wallet",
				Suggestion:  "Use dynamic transaction ID lookup or configuration",
				References:  []string{"https://bitcoin.org/en/developer-guide#transactions"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	// Check for unsafe UTXO selection
	unsafeSelectionPattern := regexp.MustCompile(`(?i)(select.*utxo|utxo.*select).*random|shuffle`)

	for i, line := range lines {
		if unsafeSelectionPattern.MatchString(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("btc_unsafe_utxo_selection_%d", i+1),
				Type:        "unsafe_utxo_selection",
				Severity:    "medium",
				Title:       "Unsafe UTXO Selection",
				Description: "Random UTXO selection may impact privacy and efficiency",
				File:        filePath,
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Chain:       "bitcoin",
				Category:    "wallet",
				Suggestion:  "Use deterministic UTXO selection algorithms (e.g., coin selection)",
				References:  []string{"https://bitcoin.org/en/developer-guide#coin-selection"},
				CreatedAt:   time.Now(),
				UpdatedAt:   time.Now(),
			})
		}
	}

	// Check for missing confirmation checks
	confirmationPattern := regexp.MustCompile(`(?i)spend|use.*utxo`)
	noConfirmPattern := regexp.MustCompile(`(?i)confirm`)

	for i, line := range lines {
		if confirmationPattern.MatchString(line) {
			// Check surrounding lines for confirmation checks
			start := i - 2
			end := i + 3
			if start < 0 {
				start = 0
			}
			if end >= len(lines) {
				end = len(lines) - 1
			}

			context := strings.Join(lines[start:end], " ")
			if !noConfirmPattern.MatchString(context) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("btc_no_confirmation_check_%d", i+1),
					Type:        "no_confirmation_check",
					Severity:    "medium",
					Title:       "Missing Confirmation Check",
					Description: "UTXO usage without confirmation check may be vulnerable to double-spending",
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "bitcoin",
					Category:    "wallet",
					Suggestion:  "Check UTXO confirmations before spending",
					References:  []string{"https://bitcoin.org/en/developer-guide#transaction-confirmation"},
					CreatedAt:   time.Now(),
					UpdatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}
