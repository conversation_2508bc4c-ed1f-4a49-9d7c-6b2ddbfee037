<div class="project-dialog">
  <h2 mat-dialog-title>
    <mat-icon>{{ isEditMode ? 'edit' : 'add' }}</mat-icon>
    {{ isEditMode ? 'Edit Project' : 'Create New Project' }}
  </h2>

  <mat-dialog-content>
    <form [formGroup]="projectForm" class="project-form">
      <!-- Project Name -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Project Name</mat-label>
        <input matInput formControlName="name" placeholder="Enter project name">
        <mat-icon matSuffix>label</mat-icon>
        <mat-error *ngIf="projectForm.get('name')?.invalid && projectForm.get('name')?.touched">
          {{ getErrorMessage('name') }}
        </mat-error>
      </mat-form-field>

      <!-- Description -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Description</mat-label>
        <textarea matInput formControlName="description" 
                  placeholder="Describe your project" 
                  rows="3"></textarea>
        <mat-icon matSuffix>description</mat-icon>
        <mat-error *ngIf="projectForm.get('description')?.invalid && projectForm.get('description')?.touched">
          {{ getErrorMessage('description') }}
        </mat-error>
      </mat-form-field>

      <!-- Blockchain Type -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Blockchain Type</mat-label>
        <mat-select formControlName="blockchain_type">
          <mat-option *ngFor="let type of blockchainTypes" [value]="type.value">
            <mat-icon>{{ getBlockchainIcon(type.value) }}</mat-icon>
            {{ type.label }}
          </mat-option>
        </mat-select>
        <mat-icon matSuffix>account_tree</mat-icon>
      </mat-form-field>

      <!-- Repository URL (Optional) -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Repository URL (Optional)</mat-label>
        <input matInput formControlName="repository_url" 
               placeholder="https://github.com/username/repo">
        <mat-icon matSuffix>link</mat-icon>
        <mat-error *ngIf="projectForm.get('repository_url')?.invalid && projectForm.get('repository_url')?.touched">
          {{ getErrorMessage('repository_url') }}
        </mat-error>
      </mat-form-field>

      <!-- Project Info -->
      <div class="project-info" *ngIf="!isEditMode">
        <mat-icon>info</mat-icon>
        <span>After creating the project, you can start scanning for security vulnerabilities.</span>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()" type="button">
      <mat-icon>cancel</mat-icon>
      Cancel
    </button>
    <button mat-raised-button 
            color="primary" 
            (click)="onSubmit()" 
            [disabled]="projectForm.invalid"
            type="submit">
      <mat-icon>{{ isEditMode ? 'save' : 'add' }}</mat-icon>
      {{ isEditMode ? 'Update' : 'Create' }}
    </button>
  </mat-dialog-actions>
</div>
