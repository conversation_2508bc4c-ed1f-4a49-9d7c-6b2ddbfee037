import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatChipsModule } from '@angular/material/chips';
import { MatCheckboxModule } from '@angular/material/checkbox';

interface SecurityPractice {
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: string;
  codeExample?: string;
  badExample?: string;
  goodExample?: string;
  tools?: string[];
  references?: string[];
}

@Component({
  selector: 'app-security-practices',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatCardModule,
    MatIconModule,
    MatExpansionModule,
    MatChipsModule,
    MatCheckboxModule
  ],
  template: `
    <div class="security-practices-container">
      <div class="page-header">
        <h1>
          <mat-icon>security</mat-icon>
          Security Best Practices
        </h1>
        <p class="page-subtitle">
          Comprehensive security guidelines for blockchain development
        </p>
      </div>

      <div class="practices-overview">
        <mat-card class="overview-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>shield</mat-icon>
            <mat-card-title>Security Principles</mat-card-title>
            <mat-card-subtitle>Core principles for secure blockchain development</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="principles-grid">
              <div class="principle" *ngFor="let principle of securityPrinciples">
                <mat-icon [style.color]="principle.color">{{ principle.icon }}</mat-icon>
                <div>
                  <h4>{{ principle.title }}</h4>
                  <p>{{ principle.description }}</p>
                </div>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <mat-tab-group class="practices-tabs" animationDuration="300ms">
        <!-- Ethereum Security Tab -->
        <mat-tab label="Ethereum Security">
          <div class="tab-content">
            <h2>Smart Contract Security</h2>
            <p>Essential security practices for Ethereum smart contract development.</p>
            
            <div class="practices-list">
              <mat-expansion-panel 
                *ngFor="let practice of ethereumPractices" 
                class="practice-panel">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <span [class]="'spt-badge spt-badge-' + practice.severity">
                      {{ practice.severity.toUpperCase() }}
                    </span>
                    {{ practice.title }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ practice.description }}
                  </mat-panel-description>
                </mat-expansion-panel-header>

                <div class="practice-content">
                  <p>{{ practice.description }}</p>

                  <div class="code-examples" *ngIf="practice.badExample || practice.goodExample">
                    <div class="bad-example" *ngIf="practice.badExample">
                      <h4>
                        <mat-icon class="error-icon">close</mat-icon>
                        Bad Practice
                      </h4>
                      <div class="code-block bad">
                        <pre><code>{{ practice.badExample }}</code></pre>
                      </div>
                    </div>

                    <div class="good-example" *ngIf="practice.goodExample">
                      <h4>
                        <mat-icon class="success-icon">check</mat-icon>
                        Good Practice
                      </h4>
                      <div class="code-block good">
                        <pre><code>{{ practice.goodExample }}</code></pre>
                      </div>
                    </div>
                  </div>

                  <div class="tools-section" *ngIf="practice.tools && practice.tools.length > 0">
                    <h4>Recommended Tools</h4>
                    <div class="tools-chips">
                      <span *ngFor="let tool of practice.tools" class="spt-badge spt-badge-info">{{ tool }}</span>
                    </div>
                  </div>

                  <div class="references-section" *ngIf="practice.references && practice.references.length > 0">
                    <h4>References</h4>
                    <ul class="references-list">
                      <li *ngFor="let ref of practice.references">{{ ref }}</li>
                    </ul>
                  </div>
                </div>
              </mat-expansion-panel>
            </div>
          </div>
        </mat-tab>

        <!-- Bitcoin Security Tab -->
        <mat-tab label="Bitcoin Security">
          <div class="tab-content">
            <h2>Bitcoin Application Security</h2>
            <p>Security best practices for Bitcoin application development.</p>
            
            <div class="practices-list">
              <mat-expansion-panel 
                *ngFor="let practice of bitcoinPractices" 
                class="practice-panel">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <span [class]="'spt-badge spt-badge-' + practice.severity">
                      {{ practice.severity.toUpperCase() }}
                    </span>
                    {{ practice.title }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ practice.description }}
                  </mat-panel-description>
                </mat-expansion-panel-header>

                <div class="practice-content">
                  <p>{{ practice.description }}</p>

                  <div class="code-examples" *ngIf="practice.codeExample">
                    <h4>Implementation Example</h4>
                    <div class="code-block">
                      <pre><code>{{ practice.codeExample }}</code></pre>
                    </div>
                  </div>

                  <div class="tools-section" *ngIf="practice.tools && practice.tools.length > 0">
                    <h4>Recommended Tools</h4>
                    <div class="tools-chips">
                      <span *ngFor="let tool of practice.tools" class="spt-badge spt-badge-info">{{ tool }}</span>
                    </div>
                  </div>
                </div>
              </mat-expansion-panel>
            </div>
          </div>
        </mat-tab>

        <!-- General Security Tab -->
        <mat-tab label="General Security">
          <div class="tab-content">
            <h2>General Development Security</h2>
            <p>Universal security practices for blockchain development environments.</p>
            
            <div class="practices-list">
              <mat-expansion-panel 
                *ngFor="let practice of generalPractices" 
                class="practice-panel">
                <mat-expansion-panel-header>
                  <mat-panel-title>
                    <span [class]="'spt-badge spt-badge-' + practice.severity">
                      {{ practice.severity.toUpperCase() }}
                    </span>
                    {{ practice.title }}
                  </mat-panel-title>
                  <mat-panel-description>
                    {{ practice.description }}
                  </mat-panel-description>
                </mat-expansion-panel-header>

                <div class="practice-content">
                  <p>{{ practice.description }}</p>

                  <div class="code-examples" *ngIf="practice.codeExample">
                    <h4>Example</h4>
                    <div class="code-block">
                      <pre><code>{{ practice.codeExample }}</code></pre>
                    </div>
                  </div>

                  <div class="tools-section" *ngIf="practice.tools && practice.tools.length > 0">
                    <h4>Recommended Tools</h4>
                    <div class="tools-chips">
                      <span *ngFor="let tool of practice.tools" class="spt-badge spt-badge-info">{{ tool }}</span>
                    </div>
                  </div>
                </div>
              </mat-expansion-panel>
            </div>
          </div>
        </mat-tab>

        <!-- Security Checklist Tab -->
        <mat-tab label="Security Checklist">
          <div class="tab-content">
            <h2>Security Checklist</h2>
            <p>Use this checklist to ensure your blockchain application follows security best practices.</p>
            
            <div class="checklist-sections">
              <mat-card class="checklist-card" *ngFor="let section of checklistSections">
                <mat-card-header>
                  <mat-icon mat-card-avatar>{{ section.icon }}</mat-icon>
                  <mat-card-title>{{ section.title }}</mat-card-title>
                  <mat-card-subtitle>{{ section.description }}</mat-card-subtitle>
                </mat-card-header>
                <mat-card-content>
                  <div class="checklist-items">
                    <div class="checklist-item" *ngFor="let item of section.items">
                      <mat-checkbox>{{ item }}</mat-checkbox>
                    </div>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </div>
  `,
  styles: [`
    .security-practices-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      text-align: center;
      margin-bottom: 32px;
    }

    .page-header h1 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      color: #1976d2;
      margin: 0 0 8px 0;
    }

    .page-subtitle {
      color: #666;
      font-size: 1.1em;
      margin: 0;
    }

    .practices-overview {
      margin-bottom: 32px;
    }

    .overview-card {
      border: 1px solid #e0e0e0;
    }

    .principles-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-top: 16px;
    }

    .principle {
      padding: 16px;
      background: #f9f9f9;
      border-radius: 8px;
    }

    .practices-tabs {
      margin-bottom: 32px;
    }

    .tab-content {
      padding: 24px 0;
    }

    .tab-content h2 {
      color: #1976d2;
      margin-bottom: 8px;
    }

    .practices-list {
      margin-top: 24px;
    }

    .practice-panel {
      margin-bottom: var(--spt-space-3);
      border: 1px solid var(--spt-gray-200);
      border-radius: var(--spt-radius-xl);
      box-shadow: var(--spt-shadow-sm);
      transition: all 0.2s ease;
    }

    .practice-panel:hover {
      box-shadow: var(--spt-shadow-md);
      border-color: var(--spt-primary-300);
    }

    /* Severity styles removed - using global spt-badge classes */

    .practice-content {
      padding: 16px 0;
    }

    .code-examples {
      margin: 24px 0;
    }

    .bad-example,
    .good-example {
      margin-bottom: 16px;
    }

    .bad-example h4,
    .good-example h4 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 0 8px 0;
    }

    .error-icon {
      color: #f44336;
    }

    .success-icon {
      color: #4caf50;
    }

    .code-block {
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
    }

    .code-block.bad {
      border-color: #f44336;
      background: #ffebee;
    }

    .code-block.good {
      border-color: #4caf50;
      background: #e8f5e8;
    }

    .code-block pre {
      margin: 0;
      padding: 16px;
      overflow-x: auto;
    }

    .code-block code {
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
    }

    .tools-section,
    .references-section {
      margin-top: 24px;
    }

    .tools-section h4,
    .references-section h4 {
      margin: 0 0 12px 0;
      color: #1976d2;
    }

    .tools-chips {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spt-space-2);
      margin-top: var(--spt-space-2);
    }

    .references-list {
      margin: 0;
      padding-left: 20px;
    }

    .references-list li {
      margin-bottom: 4px;
      color: #666;
    }

    .checklist-sections {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      margin-top: 24px;
    }

    .checklist-card {
      height: fit-content;
    }

    .checklist-items {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .checklist-item {
      display: flex;
      align-items: center;
    }

    @media (max-width: 768px) {
      .principles-grid {
        grid-template-columns: 1fr;
      }
      
      .checklist-sections {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class SecurityPracticesComponent {
  securityPrinciples = [
    {
      title: 'Defense in Depth',
      description: 'Implement multiple layers of security controls',
      icon: 'layers',
      color: '#1976d2'
    },
    {
      title: 'Secure by Default',
      description: 'Start with secure configurations and settings',
      icon: 'lock',
      color: '#4caf50'
    },
    {
      title: 'Fail Securely',
      description: 'Handle errors gracefully without exposing sensitive data',
      icon: 'error_outline',
      color: '#ff9800'
    },
    {
      title: 'Least Privilege',
      description: 'Grant minimum necessary permissions',
      icon: 'person_remove',
      color: '#9c27b0'
    }
  ];

  ethereumPractices: SecurityPractice[] = [
    {
      title: 'Reentrancy Protection',
      description: 'Prevent reentrancy attacks using checks-effects-interactions pattern',
      severity: 'critical',
      category: 'Smart Contracts',
      badExample: `// Vulnerable to reentrancy
function withdraw(uint amount) public {
    require(balances[msg.sender] >= amount);
    msg.sender.call{value: amount}("");
    balances[msg.sender] -= amount;
}`,
      goodExample: `// Protected against reentrancy
function withdraw(uint amount) public {
    require(balances[msg.sender] >= amount);
    balances[msg.sender] -= amount;
    msg.sender.call{value: amount}("");
}`,
      tools: ['Slither', 'Mythril', 'OpenZeppelin ReentrancyGuard'],
      references: ['SWC-107: Reentrancy', 'OpenZeppelin Security Patterns']
    },
    {
      title: 'Integer Overflow Protection',
      description: 'Use SafeMath or Solidity 0.8+ for overflow protection',
      severity: 'high',
      category: 'Smart Contracts',
      goodExample: `pragma solidity ^0.8.0; // Built-in overflow protection

// Or use OpenZeppelin SafeMath
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

contract SafeContract {
    using SafeMath for uint256;
    
    function safeAdd(uint256 a, uint256 b) public pure returns (uint256) {
        return a.add(b); // Will revert on overflow
    }
}`,
      tools: ['OpenZeppelin SafeMath', 'Solidity 0.8+'],
      references: ['SWC-101: Integer Overflow']
    }
  ];

  bitcoinPractices: SecurityPractice[] = [
    {
      title: 'Private Key Management',
      description: 'Secure storage and handling of private keys',
      severity: 'critical',
      category: 'Wallet Security',
      codeExample: `// Use environment variables or secure key management
const privateKey = process.env.BITCOIN_PRIVATE_KEY;

// Never hardcode private keys
// const privateKey = "L1aW4aubDFB7yfras2S1mN3bqg9nwySY8nkoLmJebSLD5BWv3ENZ"; // BAD!

// Use hardware wallets for production
const wallet = new HardwareWallet({
    device: 'ledger',
    derivationPath: "m/44'/0'/0'/0/0"
});`,
      tools: ['Hardware Wallets', 'HashiCorp Vault', 'AWS Secrets Manager']
    },
    {
      title: 'Multisig Implementation',
      description: 'Use multisig wallets for enhanced security',
      severity: 'high',
      category: 'Wallet Security',
      codeExample: `// Use at least 2-of-3 multisig for significant amounts
const multisigAddress = bitcoin.payments.p2sh({
    redeem: bitcoin.payments.p2ms({
        m: 2, // Required signatures
        pubkeys: [pubkey1, pubkey2, pubkey3]
    })
});`,
      tools: ['Bitcoin Core', 'Electrum', 'BTCPay Server']
    }
  ];

  generalPractices: SecurityPractice[] = [
    {
      title: 'Environment Security',
      description: 'Secure configuration and environment management',
      severity: 'high',
      category: 'Environment',
      codeExample: `# Use environment variables for secrets
export PRIVATE_KEY="your-private-key"
export API_KEY="your-api-key"
export DATABASE_URL="your-database-url"

# .gitignore - Never commit sensitive files
.env
.env.local
.env.production
*.key
*.pem
wallet.dat`,
      tools: ['dotenv', 'HashiCorp Vault', 'AWS Secrets Manager']
    },
    {
      title: 'Dependency Management',
      description: 'Regular security audits of dependencies',
      severity: 'medium',
      category: 'Dependencies',
      codeExample: `{
  "scripts": {
    "audit": "npm audit",
    "audit-fix": "npm audit fix",
    "security-check": "npm audit --audit-level moderate"
  }
}

# Run regular security checks
npm audit
go mod tidy
cargo audit`,
      tools: ['npm audit', 'Snyk', 'OWASP Dependency Check']
    }
  ];

  checklistSections = [
    {
      title: 'Smart Contract Security',
      description: 'Essential checks for smart contracts',
      icon: 'smart_toy',
      items: [
        'Implement reentrancy protection',
        'Use SafeMath or Solidity 0.8+',
        'Validate all inputs',
        'Implement proper access controls',
        'Test with comprehensive test suite',
        'Conduct security audit',
        'Use established patterns and libraries'
      ]
    },
    {
      title: 'Wallet Security',
      description: 'Wallet and key management security',
      icon: 'account_balance_wallet',
      items: [
        'Use hardware wallets for production',
        'Implement multisig for large amounts',
        'Never hardcode private keys',
        'Use secure key derivation',
        'Implement proper backup procedures',
        'Test recovery procedures',
        'Monitor for suspicious activity'
      ]
    },
    {
      title: 'Environment Security',
      description: 'Development and deployment security',
      icon: 'cloud_circle',
      items: [
        'Use environment variables for secrets',
        'Enable HTTPS/TLS everywhere',
        'Implement proper logging',
        'Set up monitoring and alerting',
        'Regular security updates',
        'Network segmentation',
        'Incident response plan'
      ]
    },
    {
      title: 'Code Security',
      description: 'Secure coding practices',
      icon: 'code',
      items: [
        'Mandatory code reviews',
        'Automated security scanning',
        'Regular dependency updates',
        'Input validation everywhere',
        'Error handling without information leakage',
        'Secure communication protocols',
        'Regular penetration testing'
      ]
    }
  ];
}
