import * as vscode from 'vscode';
import { SecurityAnalyzer } from '../security/analyzer';

export class HoverProvider implements vscode.HoverProvider {
    constructor(private securityAnalyzer: SecurityAnalyzer) {}

    provideHover(document: vscode.TextDocument, position: vscode.Position, token: vscode.CancellationToken): vscode.ProviderResult<vscode.Hover> {
        const issues = this.securityAnalyzer.getIssuesForFile(document.uri.fsPath);
        
        for (const issue of issues) {
            const issueRange = new vscode.Range(
                Math.max(0, issue.line - 1),
                Math.max(0, issue.column - 1),
                Math.max(0, issue.line - 1),
                Math.max(0, issue.column + 10)
            );
            
            if (issueRange.contains(position)) {
                const markdown = new vscode.MarkdownString();
                markdown.appendMarkdown(`**🛡️ Security Issue: ${issue.title}**\n\n`);
                markdown.appendMarkdown(`**Severity:** ${issue.severity.toUpperCase()}\n\n`);
                markdown.appendMarkdown(`**Description:** ${issue.description}\n\n`);
                markdown.appendMarkdown(`**Suggestion:** ${issue.suggestion}\n\n`);
                
                if (issue.references && issue.references.length > 0) {
                    markdown.appendMarkdown(`**References:**\n`);
                    issue.references.forEach(ref => {
                        markdown.appendMarkdown(`- [${ref}](${ref})\n`);
                    });
                }
                
                return new vscode.Hover(markdown, issueRange);
            }
        }
        
        return undefined;
    }
}
