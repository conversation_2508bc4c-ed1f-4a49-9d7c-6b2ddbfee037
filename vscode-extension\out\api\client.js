"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SPTApiClient = void 0;
const axios_1 = __importDefault(require("axios"));
class SPTApiClient {
    constructor(configManager) {
        this.configManager = configManager;
        this.baseUrl = this.configManager.getConfig('serverUrl');
        this.apiKey = this.configManager.getConfig('apiKey');
        this.client = axios_1.default.create({
            baseURL: this.baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'SPT-VSCode-Extension/1.0.0'
            }
        });
        // Add request interceptor for authentication
        this.client.interceptors.request.use((config) => {
            if (this.apiKey) {
                config.headers['X-API-Key'] = this.apiKey;
                config.headers['Authorization'] = `Bearer ${this.apiKey}`;
            }
            return config;
        });
        // Add response interceptor for error handling
        this.client.interceptors.response.use((response) => response, (error) => {
            console.error('SPT API Error:', error.response?.data || error.message);
            return Promise.reject(error);
        });
    }
    async healthCheck() {
        try {
            const response = await this.client.get('/health');
            return response.data;
        }
        catch (error) {
            throw new Error(`Health check failed: ${error}`);
        }
    }
    async startScan(request) {
        try {
            const response = await this.client.post('/api/v1/scan/start', request);
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to start scan: ${error}`);
        }
    }
    async getScanResult(scanId) {
        try {
            const response = await this.client.get(`/api/v1/scan/result/${scanId}`);
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to get scan result: ${error}`);
        }
    }
    async getScanHistory(limit) {
        try {
            const params = limit ? { limit } : {};
            const response = await this.client.get('/api/v1/scan/history', { params });
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to get scan history: ${error}`);
        }
    }
    async scanFile(filePath, chains) {
        try {
            const params = {
                file: filePath,
                chains: chains.join(',')
            };
            const response = await this.client.get('/api/v1/scan/file', { params });
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to scan file: ${error}`);
        }
    }
    async generateReport(request) {
        try {
            const response = await this.client.post('/api/v1/report/generate', request);
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to generate report: ${error}`);
        }
    }
    async getSecurityChecklist(chain, category) {
        try {
            const params = {};
            if (chain) {
                params.chain = chain;
            }
            if (category) {
                params.category = category;
            }
            const response = await this.client.get('/api/v1/checklist', { params });
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to get security checklist: ${error}`);
        }
    }
    async getConfiguration() {
        try {
            const response = await this.client.get('/api/v1/config');
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to get configuration: ${error}`);
        }
    }
    async updateConfiguration(config) {
        try {
            const response = await this.client.put('/api/v1/config', config);
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to update configuration: ${error}`);
        }
    }
    // Update client configuration when settings change
    updateConfig() {
        this.baseUrl = this.configManager.getConfig('serverUrl');
        this.apiKey = this.configManager.getConfig('apiKey');
        this.client.defaults.baseURL = this.baseUrl;
        // Update headers
        if (this.apiKey) {
            this.client.defaults.headers['X-API-Key'] = this.apiKey;
            this.client.defaults.headers['Authorization'] = `Bearer ${this.apiKey}`;
        }
        else {
            delete this.client.defaults.headers['X-API-Key'];
            delete this.client.defaults.headers['Authorization'];
        }
    }
    // Test connection to SPT backend
    async testConnection() {
        try {
            await this.healthCheck();
            return true;
        }
        catch (error) {
            return false;
        }
    }
    // Get server status and information
    async getServerInfo() {
        try {
            const health = await this.healthCheck();
            return {
                connected: true,
                version: health.version,
                services: health.services
            };
        }
        catch (error) {
            return {
                connected: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
    // Cancel a running scan
    async cancelScan(scanId) {
        try {
            await this.client.post(`/api/v1/scan/${scanId}/cancel`);
        }
        catch (error) {
            throw new Error(`Failed to cancel scan: ${error}`);
        }
    }
    // Get scan statistics
    async getScanStatistics() {
        try {
            const response = await this.client.get('/api/v1/scan/statistics');
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to get scan statistics: ${error}`);
        }
    }
    // Export scan results
    async exportScanResults(scanId, format) {
        try {
            const response = await this.client.get(`/api/v1/scan/${scanId}/export`, {
                params: { format },
                responseType: 'blob'
            });
            return response.data;
        }
        catch (error) {
            throw new Error(`Failed to export scan results: ${error}`);
        }
    }
}
exports.SPTApiClient = SPTApiClient;
//# sourceMappingURL=client.js.map