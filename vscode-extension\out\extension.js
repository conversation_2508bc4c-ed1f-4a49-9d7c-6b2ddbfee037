"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = __importStar(require("vscode"));
const analyzer_1 = require("./security/analyzer");
const client_1 = require("./api/client");
const securityTreeProvider_1 = require("./views/securityTreeProvider");
const manager_1 = require("./diagnostics/manager");
const codeLensProvider_1 = require("./providers/codeLensProvider");
const hoverProvider_1 = require("./providers/hoverProvider");
const completionProvider_1 = require("./providers/completionProvider");
const client_2 = require("./websocket/client");
const manager_2 = require("./config/manager");
let securityAnalyzer;
let apiClient;
let securityTreeProvider;
let diagnosticsManager;
let webSocketClient;
let configManager;
function activate(context) {
    console.log('Blockchain Security Protocol extension is now active!');
    // Initialize configuration manager
    configManager = new manager_2.ConfigurationManager();
    // Initialize API client
    apiClient = new client_1.SPTApiClient(configManager);
    // Initialize WebSocket client for real-time updates
    webSocketClient = new client_2.WebSocketClient(configManager);
    // Initialize security analyzer
    securityAnalyzer = new analyzer_1.SecurityAnalyzer(apiClient, configManager);
    // Initialize diagnostics manager
    diagnosticsManager = new manager_1.DiagnosticsManager();
    // Initialize tree view provider
    securityTreeProvider = new securityTreeProvider_1.SecurityTreeProvider(context);
    // Register tree view
    const treeView = vscode.window.createTreeView('sptSecurityView', {
        treeDataProvider: securityTreeProvider,
        showCollapseAll: true
    });
    // Register commands
    registerCommands(context);
    // Register providers
    registerProviders(context);
    // Set up event listeners
    setupEventListeners(context);
    // Initialize status bar
    setupStatusBar(context);
    // Auto-scan on startup if enabled
    if (configManager.getConfig('autoScan')) {
        vscode.commands.executeCommand('spt.scanProject');
    }
    context.subscriptions.push(treeView, diagnosticsManager, webSocketClient, securityAnalyzer);
}
exports.activate = activate;
function registerCommands(context) {
    // Scan project command
    const scanProjectCommand = vscode.commands.registerCommand('spt.scanProject', async () => {
        if (!vscode.workspace.workspaceFolders) {
            vscode.window.showErrorMessage('No workspace folder open');
            return;
        }
        const workspaceFolder = vscode.workspace.workspaceFolders[0];
        await securityAnalyzer.scanProject(workspaceFolder.uri.fsPath);
    });
    // Scan file command
    const scanFileCommand = vscode.commands.registerCommand('spt.scanFile', async () => {
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            vscode.window.showErrorMessage('No active file to scan');
            return;
        }
        await securityAnalyzer.scanFile(activeEditor.document.uri.fsPath);
    });
    // Show security report command
    const showReportCommand = vscode.commands.registerCommand('spt.showSecurityReport', async () => {
        const panel = vscode.window.createWebviewPanel('sptSecurityReport', 'Security Report', vscode.ViewColumn.One, {
            enableScripts: true,
            retainContextWhenHidden: true
        });
        panel.webview.html = await generateSecurityReportHtml();
    });
    // Open dashboard command
    const openDashboardCommand = vscode.commands.registerCommand('spt.openDashboard', () => {
        const dashboardUrl = `${configManager.getConfig('serverUrl')}/dashboard`;
        vscode.env.openExternal(vscode.Uri.parse(dashboardUrl));
    });
    // Configure settings command
    const configureCommand = vscode.commands.registerCommand('spt.configureSettings', () => {
        vscode.commands.executeCommand('workbench.action.openSettings', 'spt');
    });
    // Refresh security view command
    const refreshCommand = vscode.commands.registerCommand('spt.refreshSecurityView', () => {
        securityTreeProvider.refresh();
    });
    // Fix issue command
    const fixIssueCommand = vscode.commands.registerCommand('spt.fixIssue', async (issue) => {
        await securityAnalyzer.fixIssue(issue);
    });
    // Ignore issue command
    const ignoreIssueCommand = vscode.commands.registerCommand('spt.ignoreIssue', async (issue) => {
        await securityAnalyzer.ignoreIssue(issue);
    });
    context.subscriptions.push(scanProjectCommand, scanFileCommand, showReportCommand, openDashboardCommand, configureCommand, refreshCommand, fixIssueCommand, ignoreIssueCommand);
}
function registerProviders(context) {
    // Register CodeLens provider
    const codeLensProvider = new codeLensProvider_1.CodeLensProvider(securityAnalyzer);
    const codeLensDisposable = vscode.languages.registerCodeLensProvider([
        { scheme: 'file', language: 'solidity' },
        { scheme: 'file', language: 'javascript' },
        { scheme: 'file', language: 'typescript' },
        { scheme: 'file', language: 'python' },
        { scheme: 'file', language: 'go' },
        { scheme: 'file', language: 'rust' }
    ], codeLensProvider);
    // Register Hover provider
    const hoverProvider = new hoverProvider_1.HoverProvider(securityAnalyzer);
    const hoverDisposable = vscode.languages.registerHoverProvider([
        { scheme: 'file', language: 'solidity' },
        { scheme: 'file', language: 'javascript' },
        { scheme: 'file', language: 'typescript' }
    ], hoverProvider);
    // Register Completion provider
    const completionProvider = new completionProvider_1.CompletionProvider();
    const completionDisposable = vscode.languages.registerCompletionItemProvider([
        { scheme: 'file', language: 'solidity' },
        { scheme: 'file', language: 'javascript' },
        { scheme: 'file', language: 'typescript' }
    ], completionProvider, '.', ' ');
    context.subscriptions.push(codeLensDisposable, hoverDisposable, completionDisposable);
}
function setupEventListeners(context) {
    // Listen for file save events
    const onSaveDisposable = vscode.workspace.onDidSaveTextDocument(async (document) => {
        if (configManager.getConfig('autoScan')) {
            await securityAnalyzer.scanFile(document.uri.fsPath);
        }
    });
    // Listen for file open events
    const onOpenDisposable = vscode.window.onDidChangeActiveTextEditor(async (editor) => {
        if (editor && configManager.getConfig('scanOnOpen')) {
            await securityAnalyzer.scanFile(editor.document.uri.fsPath);
        }
    });
    // Listen for configuration changes
    const onConfigChangeDisposable = vscode.workspace.onDidChangeConfiguration((event) => {
        if (event.affectsConfiguration('spt')) {
            configManager.refresh();
            // Reconnect WebSocket if server URL changed
            if (event.affectsConfiguration('spt.serverUrl')) {
                webSocketClient.reconnect();
            }
        }
    });
    context.subscriptions.push(onSaveDisposable, onOpenDisposable, onConfigChangeDisposable);
}
function setupStatusBar(context) {
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = '$(shield) SPT';
    statusBarItem.tooltip = 'Blockchain Security Protocol - Click to scan project';
    statusBarItem.command = 'spt.scanProject';
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);
}
async function generateSecurityReportHtml() {
    // This would generate a comprehensive HTML report
    // For now, return a placeholder
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Security Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
                .issue { margin: 10px 0; padding: 10px; border-left: 4px solid #ff6b6b; }
                .critical { border-color: #ff6b6b; }
                .high { border-color: #ffa500; }
                .medium { border-color: #ffeb3b; }
                .low { border-color: #4caf50; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🛡️ Blockchain Security Report</h1>
                <p>Generated by SPT Extension</p>
            </div>
            <div class="content">
                <h2>Security Analysis Results</h2>
                <p>Report generation is in progress...</p>
            </div>
        </body>
        </html>
    `;
}
function deactivate() {
    if (webSocketClient) {
        webSocketClient.disconnect();
    }
    if (diagnosticsManager) {
        diagnosticsManager.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map