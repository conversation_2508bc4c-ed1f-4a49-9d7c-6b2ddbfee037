export interface Project {
  id: string;
  name: string;
  description: string;
  blockchain_type: string;
  repository_url?: string;
  created_at: Date;
  updated_at: Date;
  status: 'active' | 'inactive' | 'archived';
  scan_count?: number;
  last_scan_date?: Date;
}

export interface CreateProjectRequest {
  name: string;
  description: string;
  blockchain_type: string;
  repository_url?: string;
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
  blockchain_type?: string;
  repository_url?: string;
  status?: 'active' | 'inactive' | 'archived';
}
