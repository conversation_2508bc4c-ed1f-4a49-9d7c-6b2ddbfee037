{"Typed Contract Interface": {"prefix": "spt-contract-types", "body": ["import { Contract } from 'web3-eth-contract';", "import { AbiItem } from 'web3-utils';", "", "interface ${1:ContractName}Methods {", "    ${2:methodName}(${3:params}): Promise<${4:ReturnType}>;", "}", "", "interface ${1:ContractName}Events {", "    ${5:EventName}: {", "        returnValues: {", "            ${6:eventParams}", "        };", "        raw: {", "            data: string;", "            topics: string[];", "        };", "        event: string;", "        signature: string;", "        logIndex: number;", "        transactionIndex: number;", "        transactionHash: string;", "        blockHash: string;", "        blockNumber: number;", "        address: string;", "    };", "}", "", "export interface ${1:ContractName}Contract extends Contract {", "    methods: ${1:ContractName}Methods;", "    events: ${1:ContractName}Events;", "}"], "description": "Typed contract interface for better type safety"}, "Secure Transaction Types": {"prefix": "spt-tx-types", "body": ["interface TransactionConfig {", "    from: string;", "    to?: string;", "    value?: string | number;", "    gas?: number;", "    gasPrice?: string | number;", "    data?: string;", "    nonce?: number;", "}", "", "interface TransactionReceipt {", "    status: boolean;", "    transactionHash: string;", "    transactionIndex: number;", "    blockHash: string;", "    blockNumber: number;", "    from: string;", "    to: string;", "    contractAddress?: string;", "    cumulativeGasUsed: number;", "    gasUsed: number;", "    logs: any[];", "    events?: any;", "}", "", "interface TransactionError extends Error {", "    code?: number;", "    data?: any;", "    receipt?: TransactionReceipt;", "}"], "description": "Type definitions for blockchain transactions"}, "Wallet Connection Types": {"prefix": "spt-wallet-types", "body": ["interface WalletProvider {", "    isMetaMask?: boolean;", "    isConnected(): boolean;", "    request(args: { method: string; params?: any[] }): Promise<any>;", "    on(event: string, handler: (...args: any[]) => void): void;", "    removeListener(event: string, handler: (...args: any[]) => void): void;", "}", "", "interface WalletState {", "    isConnected: boolean;", "    account: string | null;", "    chainId: number | null;", "    balance: string | null;", "    provider: WalletProvider | null;", "}", "", "interface WalletError {", "    code: number;", "    message: string;", "    data?: any;", "}", "", "enum WalletErrorCodes {", "    USER_REJECTED = 4001,", "    UNAUTHORIZED = 4100,", "    UNSUPPORTED_METHOD = 4200,", "    DISCONNECTED = 4900,", "    CHAIN_DISCONNECTED = 4901", "}"], "description": "Type definitions for wallet connections and states"}, "Security Validation Functions": {"prefix": "spt-validate-types", "body": ["import Web3 from 'web3';", "", "type EthereumAddress = string;", "type Wei = string;", "type Ether = string;", "", "function isValidAddress(address: string): address is EthereumAddress {", "    return Web3.utils.isAddress(address) && address !== '******************************************';", "}", "", "function validateAddress(address: string): EthereumAddress {", "    if (!isValidAddress(address)) {", "        throw new Error('Invalid Ethereum address');", "    }", "    return address as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;", "}", "", "function toWei(amount: string | number): Wei {", "    if (typeof amount === 'string' && isNaN(Number(amount))) {", "        throw new Error('Invalid amount format');", "    }", "    if (Number(amount) <= 0) {", "        throw new Error('Amount must be positive');", "    }", "    return Web3.utils.toWei(amount.toString(), 'ether');", "}", "", "function fromWei(amount: Wei): Ether {", "    return Web3.utils.fromWei(amount, 'ether');", "}"], "description": "Type-safe validation functions for blockchain data"}, "Async Error Handling": {"prefix": "spt-async-error", "body": ["type Result<T, E = Error> = {", "    success: true;", "    data: T;", "} | {", "    success: false;", "    error: E;", "};", "", "async function safeAsync<T>(", "    operation: () => Promise<T>", "): Promise<Result<T>> {", "    try {", "        const data = await operation();", "        return { success: true, data };", "    } catch (error) {", "        return { ", "            success: false, ", "            error: error instanceof Error ? error : new Error(String(error))", "        };", "    }", "}", "", "// Usage example:", "async function ${1:functionName}() {", "    const result = await safeAsync(async () => {", "        ${2:// Your async operation here}", "        return ${3:someValue};", "    });", "", "    if (result.success) {", "        console.log('Success:', result.data);", "    } else {", "        console.error('Error:', result.error.message);", "    }", "}"], "description": "Type-safe async error handling pattern"}, "Smart Contract Factory": {"prefix": "spt-contract-factory", "body": ["import Web3 from 'web3';", "import { Contract } from 'web3-eth-contract';", "import { AbiItem } from 'web3-utils';", "", "class ContractFactory<T extends Contract> {", "    private web3: Web3;", "    private abi: AbiItem[];", "", "    constructor(web3: Web3, abi: AbiItem[]) {", "        this.web3 = web3;", "        this.abi = abi;", "    }", "", "    async deploy(", "        bytecode: string,", "        constructorArgs: any[] = [],", "        options: {", "            from: string;", "            gas?: number;", "            gasPrice?: string;", "        }", "    ): Promise<T> {", "        const contract = new this.web3.eth.Contract(this.abi);", "", "        const deployTx = contract.deploy({", "            data: bytecode,", "            arguments: constructorArgs", "        });", "", "        const gasEstimate = await deployTx.estimateGas(options);", "        const deployedContract = await deployTx.send({", "            ...options,", "            gas: gasEstimate", "        });", "", "        return deployedContract as T;", "    }", "", "    connect(address: string): T {", "        if (!Web3.utils.isAddress(address)) {", "            throw new Error('Invalid contract address');", "        }", "        return new this.web3.eth.Contract(this.abi, address) as T;", "    }", "}"], "description": "Type-safe smart contract factory for deployment and connection"}}