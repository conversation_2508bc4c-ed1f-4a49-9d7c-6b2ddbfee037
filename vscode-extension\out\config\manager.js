"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationManager = void 0;
const vscode = __importStar(require("vscode"));
class ConfigurationManager {
    constructor() {
        this.configuration = vscode.workspace.getConfiguration(ConfigurationManager.SECTION);
    }
    getConfig(key) {
        return this.configuration.get(key);
    }
    async setConfig(key, value) {
        await this.configuration.update(key, value, vscode.ConfigurationTarget.Workspace);
    }
    getAllConfig() {
        return {
            enabled: this.getConfig('enabled'),
            serverUrl: this.getConfig('serverUrl'),
            apiKey: this.getConfig('apiKey'),
            autoScan: this.getConfig('autoScan'),
            scanOnOpen: this.getConfig('scanOnOpen'),
            chains: this.getConfig('chains'),
            severity: this.getConfig('severity'),
            showInlineDecorations: this.getConfig('showInlineDecorations'),
            showProblems: this.getConfig('showProblems'),
            enableCodeLens: this.getConfig('enableCodeLens'),
            enableHover: this.getConfig('enableHover')
        };
    }
    refresh() {
        this.configuration = vscode.workspace.getConfiguration(ConfigurationManager.SECTION);
    }
    isEnabled() {
        return this.getConfig('enabled');
    }
    getServerUrl() {
        return this.getConfig('serverUrl') || 'http://localhost:8080';
    }
    getApiKey() {
        return this.getConfig('apiKey') || '';
    }
    getChains() {
        return this.getConfig('chains') || ['ethereum', 'bitcoin', 'general'];
    }
    getMinimumSeverity() {
        return this.getConfig('severity') || 'medium';
    }
    shouldAutoScan() {
        return this.getConfig('autoScan');
    }
    shouldScanOnOpen() {
        return this.getConfig('scanOnOpen');
    }
    shouldShowInlineDecorations() {
        return this.getConfig('showInlineDecorations');
    }
    shouldShowProblems() {
        return this.getConfig('showProblems');
    }
    isCodeLensEnabled() {
        return this.getConfig('enableCodeLens');
    }
    isHoverEnabled() {
        return this.getConfig('enableHover');
    }
    // Validate configuration
    validateConfig() {
        const errors = [];
        const config = this.getAllConfig();
        // Validate server URL
        if (!config.serverUrl) {
            errors.push('Server URL is required');
        }
        else {
            try {
                new URL(config.serverUrl);
            }
            catch {
                errors.push('Invalid server URL format');
            }
        }
        // Validate chains
        if (!config.chains || config.chains.length === 0) {
            errors.push('At least one blockchain chain must be selected');
        }
        const validChains = ['ethereum', 'bitcoin', 'general'];
        const invalidChains = config.chains.filter(chain => !validChains.includes(chain));
        if (invalidChains.length > 0) {
            errors.push(`Invalid chains: ${invalidChains.join(', ')}`);
        }
        // Validate severity
        const validSeverities = ['critical', 'high', 'medium', 'low', 'info'];
        if (!validSeverities.includes(config.severity)) {
            errors.push('Invalid severity level');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    // Get configuration for display
    getConfigurationSummary() {
        const config = this.getAllConfig();
        return `
SPT Configuration:
- Enabled: ${config.enabled}
- Server URL: ${config.serverUrl}
- API Key: ${config.apiKey ? '***configured***' : 'not set'}
- Auto Scan: ${config.autoScan}
- Scan on Open: ${config.scanOnOpen}
- Chains: ${config.chains.join(', ')}
- Minimum Severity: ${config.severity}
- Inline Decorations: ${config.showInlineDecorations}
- Show Problems: ${config.showProblems}
- CodeLens: ${config.enableCodeLens}
- Hover: ${config.enableHover}
        `.trim();
    }
    // Reset to defaults
    async resetToDefaults() {
        const defaultConfig = {
            enabled: true,
            serverUrl: 'http://localhost:8080',
            apiKey: '',
            autoScan: true,
            scanOnOpen: false,
            chains: ['ethereum', 'bitcoin', 'general'],
            severity: 'medium',
            showInlineDecorations: true,
            showProblems: true,
            enableCodeLens: true,
            enableHover: true
        };
        for (const [key, value] of Object.entries(defaultConfig)) {
            await this.setConfig(key, value);
        }
    }
    // Import configuration from object
    async importConfig(config) {
        for (const [key, value] of Object.entries(config)) {
            if (value !== undefined) {
                await this.setConfig(key, value);
            }
        }
    }
    // Export configuration to object
    exportConfig() {
        return this.getAllConfig();
    }
    // Check if configuration has changed
    hasConfigChanged(previousConfig) {
        const currentConfig = this.getAllConfig();
        return JSON.stringify(currentConfig) !== JSON.stringify(previousConfig);
    }
    // Get workspace-specific configuration file path
    getWorkspaceConfigPath() {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            return vscode.Uri.joinPath(workspaceFolder.uri, '.vscode', 'spt.json').fsPath;
        }
        return undefined;
    }
    // Save configuration to workspace file
    async saveToWorkspace() {
        const configPath = this.getWorkspaceConfigPath();
        if (!configPath) {
            throw new Error('No workspace folder available');
        }
        const config = this.getAllConfig();
        const configJson = JSON.stringify(config, null, 2);
        const uri = vscode.Uri.file(configPath);
        await vscode.workspace.fs.writeFile(uri, Buffer.from(configJson, 'utf8'));
        vscode.window.showInformationMessage(`Configuration saved to ${configPath}`);
    }
    // Load configuration from workspace file
    async loadFromWorkspace() {
        const configPath = this.getWorkspaceConfigPath();
        if (!configPath) {
            throw new Error('No workspace folder available');
        }
        try {
            const uri = vscode.Uri.file(configPath);
            const configData = await vscode.workspace.fs.readFile(uri);
            const config = JSON.parse(configData.toString());
            await this.importConfig(config);
            vscode.window.showInformationMessage(`Configuration loaded from ${configPath}`);
        }
        catch (error) {
            throw new Error(`Failed to load configuration: ${error}`);
        }
    }
}
exports.ConfigurationManager = ConfigurationManager;
ConfigurationManager.SECTION = 'spt';
//# sourceMappingURL=manager.js.map