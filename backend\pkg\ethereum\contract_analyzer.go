package ethereum

import (
	"crypto/sha256"
	"fmt"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// ContractAnalyzer provides detailed analysis of Solidity contracts
type ContractAnalyzer struct {
	logger *logrus.Logger
	parser *ASTParser
}

// ContractMetrics represents contract complexity and quality metrics
type ContractMetrics struct {
	LinesOfCode          int
	CyclomaticComplexity int
	FunctionCount        int
	StateVariableCount   int
	EventCount           int
	ModifierCount        int
	ExternalCallCount    int
	SecurityScore        float64
	GasEfficiencyScore   float64
	CodeQualityScore     float64
}

// SecurityRisk represents a security risk assessment
type SecurityRisk struct {
	Level       string  // low, medium, high, critical
	Score       float64 // 0-100
	Factors     []string
	Mitigations []string
}

// NewContractAnalyzer creates a new contract analyzer
func NewContractAnalyzer() *ContractAnalyzer {
	return &ContractAnalyzer{
		logger: logrus.New(),
		parser: NewASTParser(),
	}
}

// AnalyzeContract performs comprehensive contract analysis
func (ca *ContractAnalyzer) AnalyzeContract(filePath string, content string) (*models.ContractInfo, error) {
	// Parse the contract AST (commented out for now)
	// ast, err := ca.parser.ParseContract(filePath)
	// if err != nil {
	//	return nil, fmt.Errorf("failed to parse contract: %w", err)
	// }

	// Calculate metrics (commented out for now)
	// metrics := ca.calculateMetrics(content, ast)

	// Assess security risks
	// securityRisk := ca.assessSecurityRisk(content, ast, metrics)

	// Generate contract hash for tracking
	contractHash := ca.generateContractHash(content)

	// Create contract info
	contractName := ca.extractContractName(content)
	solidityVersion := ca.extractSolidityVersion(content)
	contractInfo := &models.ContractInfo{
		ID:              contractHash,
		Address:         contractHash, // Using hash as address for now
		Chain:           "ethereum",
		Name:            &contractName,
		File:            filePath,
		CompilerVersion: &solidityVersion,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	return contractInfo, nil
}

// calculateMetrics calculates various contract metrics
func (ca *ContractAnalyzer) calculateMetrics(content string, ast *ContractAST) ContractMetrics {
	lines := strings.Split(content, "\n")

	// Count non-empty, non-comment lines
	loc := 0
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed != "" && !strings.HasPrefix(trimmed, "//") && !strings.HasPrefix(trimmed, "/*") {
			loc++
		}
	}

	// Calculate cyclomatic complexity
	complexity := ca.calculateCyclomaticComplexity(content)

	// Count external calls
	externalCalls := ca.countExternalCalls(content)

	// Calculate scores
	securityScore := ca.calculateSecurityScore(content, ast)
	gasScore := ca.calculateGasEfficiencyScore(content, ast)
	qualityScore := ca.calculateCodeQualityScore(content, ast)

	return ContractMetrics{
		LinesOfCode:          loc,
		CyclomaticComplexity: complexity,
		FunctionCount:        len(ast.Functions),
		StateVariableCount:   len(ast.StateVars),
		EventCount:           len(ast.Events),
		ModifierCount:        len(ast.Modifiers),
		ExternalCallCount:    externalCalls,
		SecurityScore:        securityScore,
		GasEfficiencyScore:   gasScore,
		CodeQualityScore:     qualityScore,
	}
}

// calculateCyclomaticComplexity calculates the cyclomatic complexity
func (ca *ContractAnalyzer) calculateCyclomaticComplexity(content string) int {
	// Count decision points: if, while, for, &&, ||, ?, case
	complexity := 1 // Base complexity

	patterns := []string{
		`\bif\s*\(`,
		`\bwhile\s*\(`,
		`\bfor\s*\(`,
		`\b&&\b`,
		`\b\|\|\b`,
		`\?`,
		`\bcase\b`,
		`\bcatch\b`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllString(content, -1)
		complexity += len(matches)
	}

	return complexity
}

// countExternalCalls counts external function calls
func (ca *ContractAnalyzer) countExternalCalls(content string) int {
	patterns := []string{
		`\.call\(`,
		`\.delegatecall\(`,
		`\.staticcall\(`,
		`\.send\(`,
		`\.transfer\(`,
	}

	count := 0
	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllString(content, -1)
		count += len(matches)
	}

	return count
}

// calculateSecurityScore calculates a security score (0-100)
func (ca *ContractAnalyzer) calculateSecurityScore(content string, ast *ContractAST) float64 {
	score := 100.0

	// Deduct points for security issues
	if ca.hasReentrancyRisk(content) {
		score -= 20
	}
	if ca.hasOverflowRisk(content, ast) {
		score -= 15
	}
	if ca.hasAccessControlIssues(ast) {
		score -= 10
	}
	if ca.hasUncheckedCalls(content) {
		score -= 10
	}
	if ca.hasTimestampDependence(content) {
		score -= 5
	}

	// Add points for security features
	if ca.hasReentrancyGuards(content) {
		score += 5
	}
	if ca.hasAccessControlModifiers(ast) {
		score += 5
	}
	if ca.usesSafeMath(content) {
		score += 5
	}

	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	return score
}

// calculateGasEfficiencyScore calculates gas efficiency score (0-100)
func (ca *ContractAnalyzer) calculateGasEfficiencyScore(content string, ast *ContractAST) float64 {
	score := 100.0

	// Deduct points for gas inefficiencies
	if ca.hasUnoptimizedLoops(content) {
		score -= 15
	}
	if ca.hasRedundantStorageReads(content) {
		score -= 10
	}
	if ca.hasUnpackedStructs(content) {
		score -= 10
	}
	if ca.hasInefficiientStringOps(content) {
		score -= 5
	}

	// Add points for optimizations
	if ca.hasOptimizedStorage(content) {
		score += 5
	}
	if ca.usesEvents(ast) {
		score += 5
	}

	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	return score
}

// calculateCodeQualityScore calculates code quality score (0-100)
func (ca *ContractAnalyzer) calculateCodeQualityScore(content string, ast *ContractAST) float64 {
	score := 100.0

	// Deduct points for quality issues
	if ca.hasLongFunctions(ast) {
		score -= 10
	}
	if ca.hasDeepNesting(content) {
		score -= 10
	}
	if ca.hasMagicNumbers(content) {
		score -= 5
	}
	if ca.hasInconsistentNaming(ast) {
		score -= 5
	}

	// Add points for good practices
	if ca.hasGoodDocumentation(content) {
		score += 5
	}
	if ca.hasConsistentStyle(content) {
		score += 5
	}

	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	return score
}

// assessSecurityRisk assesses overall security risk
func (ca *ContractAnalyzer) assessSecurityRisk(content string, ast *ContractAST, metrics ContractMetrics) SecurityRisk {
	var factors []string
	var mitigations []string

	score := metrics.SecurityScore

	// Assess risk factors
	if metrics.ExternalCallCount > 5 {
		factors = append(factors, "High number of external calls")
		mitigations = append(mitigations, "Review all external calls for reentrancy protection")
	}

	if metrics.CyclomaticComplexity > 20 {
		factors = append(factors, "High cyclomatic complexity")
		mitigations = append(mitigations, "Refactor complex functions into smaller units")
	}

	if ca.hasPrivilegedFunctions(ast) {
		factors = append(factors, "Functions with elevated privileges")
		mitigations = append(mitigations, "Implement multi-signature or timelock for privileged operations")
	}

	// Determine risk level
	var level string
	if score >= 80 {
		level = "low"
	} else if score >= 60 {
		level = "medium"
	} else if score >= 40 {
		level = "high"
	} else {
		level = "critical"
	}

	return SecurityRisk{
		Level:       level,
		Score:       score,
		Factors:     factors,
		Mitigations: mitigations,
	}
}

// Helper functions for security checks

func (ca *ContractAnalyzer) hasReentrancyRisk(content string) bool {
	return regexp.MustCompile(`\.call\(`).MatchString(content) &&
		regexp.MustCompile(`=\s*[^=]`).MatchString(content)
}

func (ca *ContractAnalyzer) hasOverflowRisk(content string, ast *ContractAST) bool {
	hasSafeMath := strings.Contains(content, "SafeMath")
	isModernSolidity := false

	for _, pragma := range ast.Pragmas {
		if pragma.Name == "solidity" && strings.Contains(pragma.Value, "0.8") {
			isModernSolidity = true
		}
	}

	return !hasSafeMath && !isModernSolidity && regexp.MustCompile(`[\+\-\*\/]`).MatchString(content)
}

func (ca *ContractAnalyzer) hasAccessControlIssues(ast *ContractAST) bool {
	for _, function := range ast.Functions {
		if (function.Visibility == "public" || function.Visibility == "external") &&
			len(function.Modifiers) == 0 {
			return true
		}
	}
	return false
}

func (ca *ContractAnalyzer) hasUncheckedCalls(content string) bool {
	return regexp.MustCompile(`\.call\(`).MatchString(content) &&
		!regexp.MustCompile(`require\(`).MatchString(content)
}

func (ca *ContractAnalyzer) hasTimestampDependence(content string) bool {
	return regexp.MustCompile(`block\.timestamp|now`).MatchString(content)
}

func (ca *ContractAnalyzer) hasReentrancyGuards(content string) bool {
	return strings.Contains(content, "ReentrancyGuard") ||
		strings.Contains(content, "nonReentrant")
}

func (ca *ContractAnalyzer) hasAccessControlModifiers(ast *ContractAST) bool {
	for _, modifier := range ast.Modifiers {
		if strings.Contains(modifier.Name, "only") || strings.Contains(modifier.Name, "auth") {
			return true
		}
	}
	return false
}

func (ca *ContractAnalyzer) usesSafeMath(content string) bool {
	return strings.Contains(content, "SafeMath") || strings.Contains(content, "using SafeMath")
}

// Helper functions for gas efficiency checks

func (ca *ContractAnalyzer) hasUnoptimizedLoops(content string) bool {
	return regexp.MustCompile(`for.*\.length`).MatchString(content)
}

func (ca *ContractAnalyzer) hasRedundantStorageReads(content string) bool {
	// Simplified check for multiple storage reads
	return regexp.MustCompile(`\w+\[.*\].*\w+\[.*\]`).MatchString(content)
}

func (ca *ContractAnalyzer) hasUnpackedStructs(content string) bool {
	// Simplified check - would need more sophisticated analysis
	return strings.Contains(content, "struct") && strings.Contains(content, "uint256")
}

func (ca *ContractAnalyzer) hasInefficiientStringOps(content string) bool {
	return regexp.MustCompile(`keccak256.*==.*keccak256`).MatchString(content)
}

func (ca *ContractAnalyzer) hasOptimizedStorage(content string) bool {
	return strings.Contains(content, "packed") || strings.Contains(content, "uint128")
}

func (ca *ContractAnalyzer) usesEvents(ast *ContractAST) bool {
	return len(ast.Events) > 0
}

// Helper functions for code quality checks

func (ca *ContractAnalyzer) hasLongFunctions(ast *ContractAST) bool {
	// Check if any function is longer than 50 lines (simplified)
	return len(ast.Functions) > 0 // Placeholder
}

func (ca *ContractAnalyzer) hasDeepNesting(content string) bool {
	lines := strings.Split(content, "\n")
	maxIndent := 0
	for _, line := range lines {
		indent := len(line) - len(strings.TrimLeft(line, " \t"))
		if indent > maxIndent {
			maxIndent = indent
		}
	}
	return maxIndent > 20 // More than 5 levels of nesting
}

func (ca *ContractAnalyzer) hasMagicNumbers(content string) bool {
	return regexp.MustCompile(`\b\d{2,}\b`).MatchString(content)
}

func (ca *ContractAnalyzer) hasInconsistentNaming(ast *ContractAST) bool {
	// Simplified check for naming consistency
	return false // Placeholder
}

func (ca *ContractAnalyzer) hasGoodDocumentation(content string) bool {
	return strings.Contains(content, "/**") || strings.Contains(content, "@dev")
}

func (ca *ContractAnalyzer) hasConsistentStyle(content string) bool {
	// Simplified style check
	return !regexp.MustCompile(`\t.*    `).MatchString(content) // Mixed tabs and spaces
}

func (ca *ContractAnalyzer) hasPrivilegedFunctions(ast *ContractAST) bool {
	for _, function := range ast.Functions {
		for _, modifier := range function.Modifiers {
			if strings.Contains(modifier, "only") {
				return true
			}
		}
	}
	return false
}

// Utility functions

func (ca *ContractAnalyzer) generateContractHash(content string) string {
	hash := sha256.Sum256([]byte(content))
	return fmt.Sprintf("%x", hash)[:16]
}

func (ca *ContractAnalyzer) extractContractName(content string) string {
	re := regexp.MustCompile(`contract\s+(\w+)`)
	matches := re.FindStringSubmatch(content)
	if len(matches) > 1 {
		return matches[1]
	}
	return "Unknown"
}

func (ca *ContractAnalyzer) extractSolidityVersion(content string) string {
	re := regexp.MustCompile(`pragma\s+solidity\s+([^;]+);`)
	matches := re.FindStringSubmatch(content)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}
	return "Unknown"
}

func (ca *ContractAnalyzer) extractFunctionNames(ast *ContractAST) []string {
	var names []string
	for _, function := range ast.Functions {
		names = append(names, function.Name)
	}
	return names
}

func (ca *ContractAnalyzer) extractEventNames(ast *ContractAST) []string {
	var names []string
	for _, event := range ast.Events {
		names = append(names, event.Name)
	}
	return names
}

func (ca *ContractAnalyzer) extractModifierNames(ast *ContractAST) []string {
	var names []string
	for _, modifier := range ast.Modifiers {
		names = append(names, modifier.Name)
	}
	return names
}

func (ca *ContractAnalyzer) extractDependencies(ast *ContractAST) []string {
	var deps []string
	for _, imp := range ast.Imports {
		deps = append(deps, imp.Path)
	}
	return deps
}

func (ca *ContractAnalyzer) estimateGasUsage(ast *ContractAST) int64 {
	// Simplified gas estimation
	baseGas := int64(21000) // Base transaction cost

	// Add gas for each function (rough estimate)
	functionGas := int64(len(ast.Functions)) * 1000

	// Add gas for state variables
	stateVarGas := int64(len(ast.StateVars)) * 20000

	return baseGas + functionGas + stateVarGas
}
