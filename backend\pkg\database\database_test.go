package database

import (
	"fmt"
	"os"
	"testing"
	"time"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func skipIfCGODisabled(t *testing.T) {
	// Skip database tests if CG<PERSON> is disabled
	cgoEnabled := os.Getenv("CGO_ENABLED")
	if cgoEnabled == "0" || cgoEnabled == "" {
		t.Skip("Skipping database tests: CGO is disabled")
	}
}

func setupTestDB(t *testing.T) *Database {
	cfg := &config.Config{
		Database: config.DBConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
	}

	db, err := New(cfg)
	if err != nil {
		// If SQLite fails (CGO not available), skip the test
		t.Skipf("Database not available: %v", err)
	}
	require.NotNil(t, db)

	return db
}

func TestNewDatabase(t *testing.T) {
	skipIfCGODisabled(t)

	cfg := &config.Config{
		Database: config.DBConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
	}

	db, err := New(cfg)
	if err != nil {
		// If SQLite fails (CGO not available), skip the test
		t.Skipf("Database not available: %v", err)
	}
	assert.NotNil(t, db)
	assert.True(t, db.IsConnected())

	err = db.Close()
	assert.NoError(t, err)
}

func TestCreateAndGetScanResult(t *testing.T) {
	skipIfCGODisabled(t)

	cfg := &config.Config{
		Database: config.DBConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
	}

	db, err := New(cfg)
	if err != nil {
		t.Skipf("Database not available: %v", err)
	}
	defer db.Close()

	now := time.Now()
	scanResult := &models.ScanResult{
		ID:           "test-scan-123",
		ProjectPath:  "/test/project",
		Chains:       []string{"ethereum", "bitcoin"},
		Status:       models.ScanStatusRunning,
		StartTime:    &now,
		FilesScanned: 10,
		LinesScanned: 1000,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	// Create scan result
	err = db.CreateScanResult(scanResult)
	assert.NoError(t, err)

	// Get scan result
	retrieved, err := db.GetScanResult("test-scan-123")
	assert.NoError(t, err)
	assert.NotNil(t, retrieved)
	assert.Equal(t, scanResult.ID, retrieved.ID)
	assert.Equal(t, scanResult.ProjectPath, retrieved.ProjectPath)
	assert.Equal(t, scanResult.Status, retrieved.Status)
}

func TestUpdateScanResult(t *testing.T) {
	skipIfCGODisabled(t)

	cfg := &config.Config{
		Database: config.DBConfig{
			Type:     "sqlite",
			Database: ":memory:",
		},
	}

	db, err := New(cfg)
	if err != nil {
		t.Skipf("Database not available: %v", err)
	}
	defer db.Close()

	now := time.Now()
	scanResult := &models.ScanResult{
		ID:          "test-scan-456",
		ProjectPath: "/test/project",
		Chains:      []string{"ethereum"},
		Status:      models.ScanStatusRunning,
		StartTime:   &now,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	// Create scan result
	err = db.CreateScanResult(scanResult)
	assert.NoError(t, err)

	// Update scan result
	endTime := time.Now()
	scanResult.Status = models.ScanStatusCompleted
	scanResult.EndTime = &endTime
	scanResult.FilesScanned = 5
	scanResult.LinesScanned = 500

	err = db.UpdateScanResult(scanResult)
	assert.NoError(t, err)

	// Verify update
	retrieved, err := db.GetScanResult("test-scan-456")
	assert.NoError(t, err)
	assert.Equal(t, models.ScanStatusCompleted, retrieved.Status)
	assert.Equal(t, 5, retrieved.FilesScanned)
	assert.Equal(t, 500, retrieved.LinesScanned)
}

func TestCreateSecurityIssue(t *testing.T) {
	skipIfCGODisabled(t)
	db := setupTestDB(t)
	defer db.Close()

	// First create a scan result
	now := time.Now()
	scanResult := &models.ScanResult{
		ID:          "test-scan-789",
		ProjectPath: "/test/project",
		Chains:      []string{"ethereum"},
		Status:      models.ScanStatusRunning,
		StartTime:   &now,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	err := db.CreateScanResult(scanResult)
	assert.NoError(t, err)

	// Create security issue
	scanResultID := scanResult.ID
	issue := &models.SecurityIssue{
		ID:           "issue-123",
		ScanResultID: &scanResultID,
		Type:         "hardcoded_secret",
		Severity:     "critical",
		Title:        "Hardcoded API Key",
		Description:  "API key found in source code",
		File:         "/test/file.js",
		Line:         10,
		Column:       5,
		Code:         `const apiKey = "secret123"`,
		Chain:        "general",
		Category:     "security",
		Suggestion:   "Use environment variables",
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	err = db.CreateSecurityIssue(issue)
	assert.NoError(t, err)

	// Verify the issue was created by getting the scan result with issues
	retrieved, err := db.GetScanResult("test-scan-789")
	assert.NoError(t, err)
	assert.Len(t, retrieved.Issues, 1)
	assert.Equal(t, issue.Title, retrieved.Issues[0].Title)
	assert.Equal(t, issue.Severity, retrieved.Issues[0].Severity)
}

func TestGetScanHistory(t *testing.T) {
	skipIfCGODisabled(t)
	db := setupTestDB(t)
	defer db.Close()

	// Create multiple scan results
	now := time.Now()
	for i := 0; i < 5; i++ {
		scanResult := &models.ScanResult{
			ID:          fmt.Sprintf("scan-%d", i),
			ProjectPath: fmt.Sprintf("/test/project-%d", i),
			Chains:      []string{"ethereum"},
			Status:      models.ScanStatusCompleted,
			StartTime:   &now,
			CreatedAt:   now.Add(time.Duration(i) * time.Minute),
			UpdatedAt:   now.Add(time.Duration(i) * time.Minute),
		}

		err := db.CreateScanResult(scanResult)
		assert.NoError(t, err)
	}

	// Get scan history with limit
	history, err := db.GetScanHistory(3)
	assert.NoError(t, err)
	assert.Len(t, history, 3)

	// Should be ordered by creation time (newest first)
	assert.Equal(t, "scan-4", history[0].ID)
	assert.Equal(t, "scan-3", history[1].ID)
	assert.Equal(t, "scan-2", history[2].ID)
}

func TestHealth(t *testing.T) {
	skipIfCGODisabled(t)
	db := setupTestDB(t)
	defer db.Close()

	err := db.Health()
	assert.NoError(t, err)
}

func TestGetStatistics(t *testing.T) {
	skipIfCGODisabled(t)
	db := setupTestDB(t)
	defer db.Close()

	// Create some test data
	now := time.Now()
	scanResult := &models.ScanResult{
		ID:          "stats-test",
		ProjectPath: "/test/project",
		Chains:      []string{"ethereum"},
		Status:      models.ScanStatusCompleted,
		StartTime:   &now,
		CreatedAt:   now,
		UpdatedAt:   now,
	}

	err := db.CreateScanResult(scanResult)
	assert.NoError(t, err)

	// Create some security issues
	scanResultID := scanResult.ID
	issues := []*models.SecurityIssue{
		{
			ID:           "issue-1",
			ScanResultID: &scanResultID,
			Type:         "hardcoded_secret",
			Severity:     "critical",
			Title:        "Critical Issue",
			Chain:        "ethereum",
			Category:     "security",
			CreatedAt:    now,
			UpdatedAt:    now,
		},
		{
			ID:           "issue-2",
			ScanResultID: &scanResultID,
			Type:         "sql_injection",
			Severity:     "high",
			Title:        "High Issue",
			Chain:        "ethereum",
			Category:     "security",
			CreatedAt:    now,
			UpdatedAt:    now,
		},
	}

	for _, issue := range issues {
		err := db.CreateSecurityIssue(issue)
		assert.NoError(t, err)
	}

	// Verify that issues were created
	retrieved, err := db.GetScanResult("stats-test")
	assert.NoError(t, err)
	assert.Len(t, retrieved.Issues, 2)
}

func TestDatabaseConnectionError(t *testing.T) {
	skipIfCGODisabled(t)
	cfg := &config.Config{
		Database: config.DBConfig{
			Type:     "postgres",
			Host:     "nonexistent-host",
			Port:     5432,
			Database: "nonexistent-db",
			Username: "user",
			Password: "pass",
		},
	}

	db, err := New(cfg)
	// Should handle connection errors gracefully
	if err != nil {
		assert.Error(t, err)
		assert.Nil(t, db)
	} else {
		// If no error, should not be connected
		assert.False(t, db.IsConnected())
	}
}
