# Blockchain Security Protocol Tool (SPT)

A comprehensive DevSecOps protocol tool for Ethereum and Bitcoin blockchain development security.

## 🎯 Overview

SPT is a security-focused tool designed to ensure end-to-end security and best practices for blockchain development. It provides real-time security monitoring, vulnerability detection, and automated security checks for Ethereum (EVM-based) and Bitcoin blockchain projects.

## 🚀 Features

### 🔐 Security Scanning
- **Wallet/Key Leak Detection**: Detect private keys, mnemonics, and secrets in code, .env files, and git history
- **Smart Contract Auditing**: Comprehensive Solidity vulnerability detection (re-entrancy, overflow, gas optimization)
- **Bitcoin Script Analysis**: UTXO model security, multisig validation, and wallet security patterns
- **Dependency Scanning**: Third-party package vulnerability detection with CVE feeds

### 🛡️ Development Security
- **Environment Security**: CI/CD pipeline security checks and development environment hardening
- **Deployment Safety**: Pre-deployment validation and secure deployment practices
- **Real-time Monitoring**: Live security suggestions and interactive developer checklists

### 🧰 Tools & Integrations
- **CLI Interface**: Comprehensive command-line tools for security operations
- **Web Dashboard**: Angular+Material frontend for security monitoring and reporting
- **VS Code Extension**: Real-time security highlighting and inline suggestions
- **Report Generation**: Markdown/PDF security reports with timestamped audits

## 🏗️ Architecture

```
SPT/
├── backend/           # Go backend services
├── frontend/          # Angular+Material web dashboard
├── cli/              # Command-line interface
├── vscode-extension/ # VS Code extension
├── plugins/          # Blockchain-specific security modules
├── security/         # Security templates and configurations
└── docs/             # Documentation and guides
```

## 🛠️ Technology Stack

- **Backend**: Go (Gin framework, security scanning engines)
- **Frontend**: Angular + Angular Material
- **CLI**: Go (Cobra CLI framework)
- **VS Code Extension**: TypeScript
- **Database**: SQLite/PostgreSQL for audit logs
- **Security**: Integration with Slither, OSV, CVE databases

## 📋 Quick Start

### Prerequisites
- Go 1.21+
- Node.js 18+
- Angular CLI
- VS Code (for extension development)

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd Blockchain.SPT

# Install backend dependencies
cd backend && go mod tidy

# Install frontend dependencies
cd ../frontend && npm install

# Build CLI tool
cd ../cli && go build -o spt

# Install VS Code extension
cd ../vscode-extension && npm install && npm run compile
```

### 🎯 **Usage Examples**

#### **CLI Security Scanning**
```bash
# Comprehensive project scan
./build/spt.exe scan ./my-project --chain ethereum

# Dependency vulnerability check
./build/spt.exe check dependencies ./package.json

# CI/CD pipeline security analysis
./build/spt.exe check cicd .github/workflows/

# Generate detailed security report
./build/spt.exe report --format markdown --output security-report.md

# Quick environment check
./build/spt.exe check environment .env
```

#### **API Server**
```bash
# Start production server
./build/spt-server.exe

# Server runs on http://localhost:8080
# API endpoints: http://localhost:8080/api/v1
# Health check: http://localhost:8080/health
# WebSocket: ws://localhost:8080/ws
```

#### **Web Dashboard**
```bash
# Start Angular frontend (development)
cd frontend && ng serve

# Access dashboard at http://localhost:4200
# Features: Project management, real-time scanning, reports

## 🔧 **Configuration**

SPT uses flexible YAML-based configuration. Create `spt.config.yaml`:
```yaml
server:
  port: 8080
  host: "localhost"

database:
  type: "postgres"  # or "memory" for development
  host: "localhost"
  port: 5432

scanning:
  parallel_workers: 4
  timeout: "5m"
  chains: ["ethereum", "bitcoin", "general"]

security:
  allowed_origins:
    - "http://localhost:4200"
    - "https://yourdomain.com"
```

## 📊 **Performance & Metrics**

### **Scan Performance**
- **Small Projects** (<100 files): <5 seconds
- **Medium Projects** (100-1000 files): <10 seconds
- **Large Projects** (1000+ files): <30 seconds
- **API Response Time**: <100ms average
- **Memory Usage**: <500MB typical

### **Detection Capabilities**
- **Vulnerability Types**: 50+ security patterns
- **Blockchain Support**: Ethereum, Bitcoin, General
- **Package Managers**: 10+ supported
- **CI/CD Platforms**: 8+ supported
- **File Types**: 20+ languages and formats

## 📚 **Documentation**

- [Getting Started Guide](docs/getting-started.md) - Quick setup and first scan
- [Security Best Practices](docs/security-practices.md) - Comprehensive security guide
- [PROJECT_PLAN.md](PROJECT_PLAN.md) - Development roadmap and future sprints

## 🚀 **Future Roadmap**

### **Upcoming Features (Q4 2025 - Q3 2026)**
- **Enhanced Blockchain Support**: Solana, Polygon, Cardano
- **AI-Powered Analysis**: ML-based vulnerability detection
- **Enterprise Features**: SSO, RBAC, team collaboration
- **Cloud Integration**: Kubernetes, Docker, CI/CD automation

## 📄 **License & Status**

- **License**: MIT License
- **Status**: ✅ Production Ready
- **Version**: v1.0.0
- **Maintained By**: Solo Developer with AI Assistance
- **Last Updated**: July 29, 2025

---

**🎉 SPT is production-ready and actively maintained. Start securing your blockchain projects today!**

## 🆘 Support

- [Documentation](docs/)
- [Issue Tracker](https://github.com/your-org/blockchain-spt/issues)
- [Discussions](https://github.com/your-org/blockchain-spt/discussions)
