package main

import (
	"blockchain-spt/cmd/spt/commands"
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

var (
	version = "1.0.0"
	commit  = "dev"
	date    = "unknown"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "spt",
		Short: "Blockchain Security Penetration Testing Tool",
		Long: `SPT (Security Penetration Testing) is a comprehensive security analysis tool
for blockchain applications and smart contracts.

It provides security scanning for:
- Ethereum smart contracts and DApps
- Bitcoin wallets and transactions
- Dependencies and third-party packages
- CI/CD configurations and environments
- Development environment security

Examples:
  spt scan --path ./project                    # Scan entire project
  spt audit ethereum --path ./contracts       # Audit Ethereum contracts
  spt check deps --path ./package.json        # Check dependencies
  spt analyze bitcoin --path ./wallet         # Analyze Bitcoin code
  spt report --format json --output report.json # Generate report`,
		Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
	}

	// Add global flags
	rootCmd.PersistentFlags().StringP("config", "c", "", "Config file path")
	rootCmd.PersistentFlags().StringP("log-level", "l", "info", "Log level (debug, info, warn, error)")
	rootCmd.PersistentFlags().BoolP("verbose", "v", false, "Verbose output")
	rootCmd.PersistentFlags().BoolP("quiet", "q", false, "Quiet mode (minimal output)")
	rootCmd.PersistentFlags().String("output-format", "table", "Output format (table, json, yaml, csv)")

	// Add subcommands
	rootCmd.AddCommand(commands.NewScanCommand())
	rootCmd.AddCommand(commands.NewAuditCommand())
	rootCmd.AddCommand(commands.NewCheckCommand())
	rootCmd.AddCommand(commands.NewAnalyzeCommand())
	rootCmd.AddCommand(commands.NewReportCommand())
	rootCmd.AddCommand(commands.NewConfigCommand())
	rootCmd.AddCommand(commands.NewUpdateCommand())
	rootCmd.AddCommand(commands.NewServerCommand())

	// Execute the root command
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}
