"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebSocketClient = void 0;
const vscode = __importStar(require("vscode"));
const ws_1 = __importDefault(require("ws"));
class WebSocketClient {
    constructor(configManager) {
        this.configManager = configManager;
        this.ws = null;
        this.reconnectTimer = null;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000; // 5 seconds
        this.eventEmitter = new vscode.EventEmitter();
        this.onMessage = this.eventEmitter.event;
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 99);
        this.statusBarItem.text = '$(plug) SPT';
        this.statusBarItem.tooltip = 'SPT WebSocket Connection';
        this.statusBarItem.show();
        this.connect();
    }
    connect() {
        if (this.isConnecting || this.ws?.readyState === ws_1.default.OPEN) {
            return;
        }
        this.isConnecting = true;
        this.updateStatusBar('connecting');
        const serverUrl = this.configManager.getServerUrl();
        const wsUrl = serverUrl.replace(/^http/, 'ws') + '/ws';
        try {
            this.ws = new ws_1.default(wsUrl);
            if (this.ws) {
                this.ws.on('open', () => {
                    console.log('SPT WebSocket connected');
                    this.isConnecting = false;
                    this.reconnectAttempts = 0;
                    this.updateStatusBar('connected');
                    // Send initial message
                    this.send({
                        type: 'subscribe',
                        data: {
                            client_type: 'vscode-extension',
                            version: '1.0.0'
                        },
                        timestamp: new Date().toISOString()
                    });
                });
                this.ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data.toString());
                        this.handleMessage(message);
                    }
                    catch (error) {
                        console.error('Failed to parse WebSocket message:', error);
                    }
                });
                this.ws.on('close', (code, reason) => {
                    console.log(`SPT WebSocket closed: ${code} - ${reason}`);
                    this.isConnecting = false;
                    this.ws = null;
                    this.updateStatusBar('disconnected');
                    this.scheduleReconnect();
                });
                this.ws.on('error', (error) => {
                    console.error('SPT WebSocket error:', error);
                    this.isConnecting = false;
                    this.updateStatusBar('error');
                    this.scheduleReconnect();
                });
            }
        }
        catch (error) {
            console.error('Failed to create WebSocket connection:', error);
            this.isConnecting = false;
            this.updateStatusBar('error');
            this.scheduleReconnect();
        }
    }
    handleMessage(message) {
        console.log('Received WebSocket message:', message.type);
        switch (message.type) {
            case 'welcome':
                vscode.window.showInformationMessage('Connected to SPT real-time updates');
                break;
            case 'scan_progress':
                this.handleScanProgress(message.data);
                break;
            case 'scan_result':
                this.handleScanResult(message.data);
                break;
            case 'system_alert':
                this.handleSystemAlert(message.data);
                break;
            case 'pong':
                // Handle ping/pong for keep-alive
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
        // Emit event for other components to listen
        this.eventEmitter.fire(message);
    }
    handleScanProgress(progress) {
        const progressPercent = Math.round(progress.progress * 100);
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: `Scanning: ${progress.scan_id}`,
            cancellable: false
        }, async (progressReporter) => {
            progressReporter.report({
                increment: progressPercent,
                message: `${progress.status} - ${progress.files_scanned}/${progress.total_files} files (${progress.issues_found} issues found)`
            });
        });
        // Update status bar with scan progress
        this.statusBarItem.text = `$(sync~spin) SPT: ${progressPercent}%`;
        this.statusBarItem.tooltip = `Scanning in progress: ${progress.current_file || 'Processing...'}`;
    }
    handleScanResult(result) {
        const issueCount = result.issues.length;
        const message = issueCount > 0
            ? `Scan completed: ${issueCount} security issues found`
            : 'Scan completed: No security issues found';
        vscode.window.showInformationMessage(message, 'View Results').then(selection => {
            if (selection === 'View Results') {
                vscode.commands.executeCommand('spt.showSecurityReport');
            }
        });
        // Reset status bar
        this.updateStatusBar('connected');
    }
    handleSystemAlert(alert) {
        const alertType = alert.alert_type || 'info';
        const message = alert.message || 'System alert received';
        switch (alertType) {
            case 'error':
                vscode.window.showErrorMessage(`SPT Alert: ${message}`);
                break;
            case 'warning':
                vscode.window.showWarningMessage(`SPT Alert: ${message}`);
                break;
            default:
                vscode.window.showInformationMessage(`SPT Alert: ${message}`);
        }
    }
    scheduleReconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
            this.updateStatusBar('failed');
            return;
        }
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts;
        console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, delay);
    }
    updateStatusBar(status) {
        switch (status) {
            case 'connecting':
                this.statusBarItem.text = '$(sync~spin) SPT';
                this.statusBarItem.tooltip = 'Connecting to SPT server...';
                this.statusBarItem.backgroundColor = undefined;
                break;
            case 'connected':
                this.statusBarItem.text = '$(plug) SPT';
                this.statusBarItem.tooltip = 'Connected to SPT server';
                this.statusBarItem.backgroundColor = undefined;
                break;
            case 'disconnected':
                this.statusBarItem.text = '$(debug-disconnect) SPT';
                this.statusBarItem.tooltip = 'Disconnected from SPT server';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
                break;
            case 'error':
                this.statusBarItem.text = '$(error) SPT';
                this.statusBarItem.tooltip = 'SPT connection error';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
                break;
            case 'failed':
                this.statusBarItem.text = '$(x) SPT';
                this.statusBarItem.tooltip = 'SPT connection failed - click to retry';
                this.statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.errorBackground');
                this.statusBarItem.command = 'spt.reconnectWebSocket';
                break;
        }
    }
    send(message) {
        if (this.ws?.readyState === ws_1.default.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
        else {
            console.warn('WebSocket not connected, message not sent:', message);
        }
    }
    reconnect() {
        this.disconnect();
        this.reconnectAttempts = 0;
        this.connect();
    }
    disconnect() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        this.isConnecting = false;
        this.updateStatusBar('disconnected');
    }
    isConnected() {
        return this.ws?.readyState === ws_1.default.OPEN;
    }
    getConnectionStatus() {
        if (!this.ws) {
            return 'disconnected';
        }
        switch (this.ws.readyState) {
            case ws_1.default.CONNECTING:
                return 'connecting';
            case ws_1.default.OPEN:
                return 'connected';
            case ws_1.default.CLOSING:
                return 'closing';
            case ws_1.default.CLOSED:
                return 'closed';
            default:
                return 'unknown';
        }
    }
    dispose() {
        this.disconnect();
        this.statusBarItem.dispose();
        this.eventEmitter.dispose();
    }
}
exports.WebSocketClient = WebSocketClient;
//# sourceMappingURL=client.js.map