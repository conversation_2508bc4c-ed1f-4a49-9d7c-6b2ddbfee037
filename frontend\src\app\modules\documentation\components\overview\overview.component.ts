import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';

@Component({
  selector: 'app-overview',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatChipsModule
  ],
  template: `
    <div class="overview-container">
      <div class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">
            <mat-icon class="hero-icon">shield</mat-icon>
            Blockchain Security Protocol Tool
          </h1>
          <p class="hero-subtitle">
            Comprehensive security analysis and auditing for Ethereum and Bitcoin blockchain applications
          </p>
          <div class="hero-actions">
            <button mat-raised-button color="primary" routerLink="/doc/getting-started">
              <mat-icon>play_arrow</mat-icon>
              Get Started
            </button>
            <button mat-stroked-button routerLink="/doc/api-reference">
              <mat-icon>api</mat-icon>
              API Reference
            </button>
          </div>
        </div>
      </div>

      <div class="features-section">
        <h2>Key Features</h2>
        <div class="features-grid">
          <mat-card class="feature-card" *ngFor="let feature of features">
            <mat-card-header>
              <mat-icon mat-card-avatar [style.background-color]="feature.color">
                {{ feature.icon }}
              </mat-icon>
              <mat-card-title>{{ feature.title }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <p>{{ feature.description }}</p>
              <mat-chip-listbox class="feature-tags">
                <mat-chip *ngFor="let tag of feature.tags">{{ tag }}</mat-chip>
              </mat-chip-listbox>
            </mat-card-content>
          </mat-card>
        </div>
      </div>

      <div class="tools-section">
        <h2>Available Tools</h2>
        <div class="tools-grid">
          <mat-card class="tool-card" *ngFor="let tool of tools">
            <mat-card-header>
              <mat-icon mat-card-avatar>{{ tool.icon }}</mat-icon>
              <mat-card-title>{{ tool.name }}</mat-card-title>
              <mat-card-subtitle>{{ tool.type }}</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>{{ tool.description }}</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button [routerLink]="tool.docLink">
                <mat-icon>description</mat-icon>
                Documentation
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>

      <div class="quick-start-section">
        <mat-card class="quick-start-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>rocket_launch</mat-icon>
            <mat-card-title>Quick Start</mat-card-title>
            <mat-card-subtitle>Get SPT running in minutes</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="quick-start-steps">
              <div class="step" *ngFor="let step of quickStartSteps; let i = index">
                <div class="step-number">{{ i + 1 }}</div>
                <div class="step-content">
                  <h4>{{ step.title }}</h4>
                  <p>{{ step.description }}</p>
                  <code class="step-code" *ngIf="step.code">{{ step.code }}</code>
                </div>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-raised-button color="primary" routerLink="/doc/getting-started">
              <mat-icon>arrow_forward</mat-icon>
              Full Installation Guide
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .overview-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .hero-section {
      text-align: center;
      padding: 64px 32px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 24px;
      margin-bottom: 48px;
      position: relative;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    }

    .hero-section::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
      pointer-events: none;
    }

    .hero-content {
      padding: 0 24px;
      position: relative;
      z-index: 1;
    }

    .hero-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 20px;
      font-size: 2.8em;
      font-weight: 700;
      margin: 0 0 20px 0;
      letter-spacing: -1px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .hero-icon {
      font-size: 56px;
      width: 56px;
      height: 56px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 16px;
      padding: 12px;
      backdrop-filter: blur(10px);
    }

    .hero-subtitle {
      font-size: 1.3em;
      margin: 0 0 40px 0;
      opacity: 0.95;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.5;
      font-weight: 400;
    }

    .hero-actions {
      display: flex;
      gap: 20px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .hero-actions button {
      padding: 12px 32px;
      font-size: 1.1em;
      font-weight: 600;
      border-radius: 50px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      text-transform: none;
      letter-spacing: 0.5px;
    }

    .hero-actions button:first-child {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
    }

    .hero-actions button:first-child:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    .hero-actions button:last-child {
      background: rgba(255, 255, 255, 0.1);
      border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .hero-actions button:last-child:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    .features-section,
    .tools-section {
      margin-bottom: 64px;
    }

    .features-section h2,
    .tools-section h2 {
      text-align: center;
      margin-bottom: 40px;
      color: #4c63d2;
      font-size: 2.2em;
      font-weight: 700;
      letter-spacing: -0.5px;
    }

    .features-grid,
    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 32px;
    }

    .feature-card,
    .tool-card {
      height: 100%;
      border-radius: 20px;
      border: none;
      background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      position: relative;
    }

    .feature-card::before,
    .tool-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    }

    .feature-card:hover,
    .tool-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
    }

    .feature-tags {
      margin-top: 20px;
    }

    .feature-tags mat-chip {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: white;
      border-radius: 20px;
      font-weight: 500;
      font-size: 0.85em;
      margin: 4px 8px 4px 0;
      padding: 8px 16px;
      border: none;
    }

    .quick-start-section {
      margin-bottom: 64px;
    }

    .quick-start-card {
      max-width: 900px;
      margin: 0 auto;
      border-radius: 24px;
      background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
      border: 1px solid #e8eaff;
      box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
      overflow: hidden;
      position: relative;
    }

    .quick-start-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6px;
      background: linear-gradient(90deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%);
    }

    .quick-start-steps {
      display: flex;
      flex-direction: column;
      gap: 32px;
      padding: 8px 0;
    }

    .step {
      display: flex;
      gap: 20px;
      align-items: flex-start;
      padding: 20px;
      background: rgba(255, 255, 255, 0.7);
      border-radius: 16px;
      border: 1px solid rgba(102, 126, 234, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      background: rgba(255, 255, 255, 0.9);
      border-color: rgba(102, 126, 234, 0.2);
      transform: translateX(8px);
    }

    .step-number {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
      font-size: 1.1em;
      flex-shrink: 0;
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .step-content h4 {
      margin: 0 0 12px 0;
      color: #4c63d2;
      font-weight: 600;
      font-size: 1.1em;
    }

    .step-content p {
      margin: 0 0 12px 0;
      color: #64748b;
      line-height: 1.6;
    }

    .step-code {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      padding: 12px 16px;
      border-radius: 12px;
      font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
      font-size: 0.9em;
      display: block;
      margin-top: 12px;
      border: 1px solid #e2e8f0;
      color: #475569;
      font-weight: 500;
    }

    @media (max-width: 768px) {
      .hero-title {
        font-size: 2em;
        flex-direction: column;
        gap: 8px;
      }

      .hero-actions {
        flex-direction: column;
        align-items: center;
      }

      .features-grid,
      .tools-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class OverviewComponent {
  features = [
    {
      title: 'Smart Contract Analysis',
      description: 'Comprehensive security analysis for Ethereum smart contracts with vulnerability detection and gas optimization suggestions.',
      icon: 'psychology',
      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      tags: ['Solidity', 'Ethereum', 'Security']
    },
    {
      title: 'Bitcoin Security',
      description: 'Advanced security checks for Bitcoin applications including wallet security, transaction validation, and UTXO management.',
      icon: 'account_balance_wallet',
      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      tags: ['Bitcoin', 'Wallet', 'UTXO']
    },
    {
      title: 'Real-time Monitoring',
      description: 'Live security monitoring with WebSocket connections, real-time alerts, and continuous vulnerability scanning.',
      icon: 'radar',
      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      tags: ['Real-time', 'Monitoring', 'Alerts']
    },
    {
      title: 'Comprehensive Reports',
      description: 'Detailed security reports with executive summaries, technical details, and actionable recommendations.',
      icon: 'analytics',
      color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      tags: ['Reports', 'Analytics', 'PDF']
    }
  ];

  tools = [
    {
      name: 'Web Dashboard',
      type: 'Angular Application',
      description: 'Modern web interface for security scanning, project management, and report generation.',
      icon: 'web',
      docLink: '/doc/getting-started'
    },
    {
      name: 'CLI Tool',
      type: 'Command Line',
      description: 'Powerful command-line interface for automated security scanning and CI/CD integration.',
      icon: 'terminal',
      docLink: '/doc/cli-guide'
    },
    {
      name: 'VS Code Extension',
      type: 'IDE Integration',
      description: 'Real-time security highlighting and inline suggestions directly in your code editor.',
      icon: 'integration_instructions',
      docLink: '/doc/vscode-extension'
    },
    {
      name: 'REST API',
      type: 'Backend Service',
      description: 'RESTful API for integrating SPT security scanning into your development workflow.',
      icon: 'api',
      docLink: '/doc/api-reference'
    }
  ];

  quickStartSteps = [
    {
      title: 'Clone Repository',
      description: 'Get the SPT source code from GitHub',
      code: 'git clone https://github.com/blockchain-spt/spt.git'
    },
    {
      title: 'Start Backend',
      description: 'Launch the Go backend server',
      code: 'cd backend && go run cmd/main.go'
    },
    {
      title: 'Start Frontend',
      description: 'Launch the Angular development server',
      code: 'cd frontend && npm start'
    },
    {
      title: 'Begin Scanning',
      description: 'Access the web dashboard and start your first security scan',
      code: 'Open http://localhost:4200'
    }
  ];
}
