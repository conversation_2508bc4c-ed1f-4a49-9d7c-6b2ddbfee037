import { Component } from '@angular/core';

@Component({
  selector: 'app-overview',
  template: `
    <div class="overview-container">
      <div class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">
            <mat-icon class="hero-icon">shield</mat-icon>
            Blockchain Security Protocol Tool
          </h1>
          <p class="hero-subtitle">
            Comprehensive security analysis and auditing for Ethereum and Bitcoin blockchain applications
          </p>
          <div class="hero-actions">
            <button mat-raised-button color="primary" routerLink="/doc/getting-started">
              <mat-icon>play_arrow</mat-icon>
              Get Started
            </button>
            <button mat-stroked-button routerLink="/doc/api-reference">
              <mat-icon>api</mat-icon>
              API Reference
            </button>
          </div>
        </div>
      </div>

      <div class="features-section">
        <h2>Key Features</h2>
        <div class="features-grid">
          <mat-card class="feature-card" *ngFor="let feature of features">
            <mat-card-header>
              <mat-icon mat-card-avatar [style.background-color]="feature.color">
                {{ feature.icon }}
              </mat-icon>
              <mat-card-title>{{ feature.title }}</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <p>{{ feature.description }}</p>
              <mat-chip-listbox class="feature-tags">
                <mat-chip *ngFor="let tag of feature.tags">{{ tag }}</mat-chip>
              </mat-chip-listbox>
            </mat-card-content>
          </mat-card>
        </div>
      </div>

      <div class="tools-section">
        <h2>Available Tools</h2>
        <div class="tools-grid">
          <mat-card class="tool-card" *ngFor="let tool of tools">
            <mat-card-header>
              <mat-icon mat-card-avatar>{{ tool.icon }}</mat-icon>
              <mat-card-title>{{ tool.name }}</mat-card-title>
              <mat-card-subtitle>{{ tool.type }}</mat-card-subtitle>
            </mat-card-header>
            <mat-card-content>
              <p>{{ tool.description }}</p>
            </mat-card-content>
            <mat-card-actions>
              <button mat-button [routerLink]="tool.docLink">
                <mat-icon>description</mat-icon>
                Documentation
              </button>
            </mat-card-actions>
          </mat-card>
        </div>
      </div>

      <div class="quick-start-section">
        <mat-card class="quick-start-card">
          <mat-card-header>
            <mat-icon mat-card-avatar>rocket_launch</mat-icon>
            <mat-card-title>Quick Start</mat-card-title>
            <mat-card-subtitle>Get SPT running in minutes</mat-card-subtitle>
          </mat-card-header>
          <mat-card-content>
            <div class="quick-start-steps">
              <div class="step" *ngFor="let step of quickStartSteps; let i = index">
                <div class="step-number">{{ i + 1 }}</div>
                <div class="step-content">
                  <h4>{{ step.title }}</h4>
                  <p>{{ step.description }}</p>
                  <code class="step-code" *ngIf="step.code">{{ step.code }}</code>
                </div>
              </div>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-raised-button color="primary" routerLink="/doc/getting-started">
              <mat-icon>arrow_forward</mat-icon>
              Full Installation Guide
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .overview-container {
      max-width: 1200px;
      margin: 0 auto;
    }

    .hero-section {
      text-align: center;
      padding: 48px 0;
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      color: white;
      border-radius: 12px;
      margin-bottom: 48px;
    }

    .hero-content {
      padding: 0 24px;
    }

    .hero-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      font-size: 2.5em;
      font-weight: 300;
      margin: 0 0 16px 0;
    }

    .hero-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
    }

    .hero-subtitle {
      font-size: 1.2em;
      margin: 0 0 32px 0;
      opacity: 0.9;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .hero-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .features-section,
    .tools-section {
      margin-bottom: 48px;
    }

    .features-section h2,
    .tools-section h2 {
      text-align: center;
      margin-bottom: 32px;
      color: #1976d2;
    }

    .features-grid,
    .tools-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
    }

    .feature-card,
    .tool-card {
      height: 100%;
    }

    .feature-tags {
      margin-top: 16px;
    }

    .quick-start-section {
      margin-bottom: 48px;
    }

    .quick-start-card {
      max-width: 800px;
      margin: 0 auto;
    }

    .quick-start-steps {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .step {
      display: flex;
      gap: 16px;
      align-items: flex-start;
    }

    .step-number {
      background: #1976d2;
      color: white;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      flex-shrink: 0;
    }

    .step-content h4 {
      margin: 0 0 8px 0;
      color: #1976d2;
    }

    .step-content p {
      margin: 0 0 8px 0;
      color: #666;
    }

    .step-code {
      background: #f5f5f5;
      padding: 8px 12px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 0.9em;
      display: block;
      margin-top: 8px;
    }

    @media (max-width: 768px) {
      .hero-title {
        font-size: 2em;
        flex-direction: column;
        gap: 8px;
      }

      .hero-actions {
        flex-direction: column;
        align-items: center;
      }

      .features-grid,
      .tools-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class OverviewComponent {
  features = [
    {
      title: 'Smart Contract Analysis',
      description: 'Comprehensive security analysis for Ethereum smart contracts with vulnerability detection and gas optimization suggestions.',
      icon: 'smart_toy',
      color: '#4caf50',
      tags: ['Solidity', 'Ethereum', 'Security']
    },
    {
      title: 'Bitcoin Security',
      description: 'Advanced security checks for Bitcoin applications including wallet security, transaction validation, and UTXO management.',
      icon: 'currency_bitcoin',
      color: '#ff9800',
      tags: ['Bitcoin', 'Wallet', 'UTXO']
    },
    {
      title: 'Real-time Monitoring',
      description: 'Live security monitoring with WebSocket connections, real-time alerts, and continuous vulnerability scanning.',
      icon: 'monitor_heart',
      color: '#f44336',
      tags: ['Real-time', 'Monitoring', 'Alerts']
    },
    {
      title: 'Comprehensive Reports',
      description: 'Detailed security reports with executive summaries, technical details, and actionable recommendations.',
      icon: 'assessment',
      color: '#9c27b0',
      tags: ['Reports', 'Analytics', 'PDF']
    }
  ];

  tools = [
    {
      name: 'Web Dashboard',
      type: 'Angular Application',
      description: 'Modern web interface for security scanning, project management, and report generation.',
      icon: 'dashboard',
      docLink: '/doc/getting-started'
    },
    {
      name: 'CLI Tool',
      type: 'Command Line',
      description: 'Powerful command-line interface for automated security scanning and CI/CD integration.',
      icon: 'terminal',
      docLink: '/doc/cli-guide'
    },
    {
      name: 'VS Code Extension',
      type: 'IDE Integration',
      description: 'Real-time security highlighting and inline suggestions directly in your code editor.',
      icon: 'extension',
      docLink: '/doc/vscode-extension'
    },
    {
      name: 'REST API',
      type: 'Backend Service',
      description: 'RESTful API for integrating SPT security scanning into your development workflow.',
      icon: 'api',
      docLink: '/doc/api-reference'
    }
  ];

  quickStartSteps = [
    {
      title: 'Clone Repository',
      description: 'Get the SPT source code from GitHub',
      code: 'git clone https://github.com/blockchain-spt/spt.git'
    },
    {
      title: 'Start Backend',
      description: 'Launch the Go backend server',
      code: 'cd backend && go run cmd/main.go'
    },
    {
      title: 'Start Frontend',
      description: 'Launch the Angular development server',
      code: 'cd frontend && npm start'
    },
    {
      title: 'Begin Scanning',
      description: 'Access the web dashboard and start your first security scan',
      code: 'Open http://localhost:4200'
    }
  ];
}
