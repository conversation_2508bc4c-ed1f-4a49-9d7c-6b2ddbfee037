import * as vscode from 'vscode';

export class SecurityTreeProvider implements vscode.TreeDataProvider<SecurityTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<SecurityTreeItem | undefined | null | void> = new vscode.EventEmitter<SecurityTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<SecurityTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor(private context: vscode.ExtensionContext) {}

    refresh(): void {
        this._onDidChangeTreeData.fire();
    }

    getTreeItem(element: SecurityTreeItem): vscode.TreeItem {
        return element;
    }

    getChildren(element?: SecurityTreeItem): Thenable<SecurityTreeItem[]> {
        if (!element) {
            return Promise.resolve([
                new SecurityTreeItem('Critical Issues', vscode.TreeItemCollapsibleState.Expanded),
                new SecurityTreeItem('High Issues', vscode.TreeItemCollapsibleState.Expanded),
                new SecurityTreeItem('Medium Issues', vscode.TreeItemCollapsibleState.Collapsed),
                new SecurityTreeItem('Low Issues', vscode.TreeItemCollapsibleState.Collapsed)
            ]);
        }
        return Promise.resolve([]);
    }
}

export class SecurityTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState
    ) {
        super(label, collapsibleState);
        this.tooltip = `${this.label}`;
        this.description = '';
    }
}
