{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,kDAAuD;AACvD,yCAA4C;AAC5C,uEAAoE;AACpE,mDAA2D;AAC3D,mEAAgE;AAChE,6DAA0D;AAC1D,uEAAoE;AACpE,+CAAqD;AACrD,8CAAwD;AAExD,IAAI,gBAAkC,CAAC;AACvC,IAAI,SAAuB,CAAC;AAC5B,IAAI,oBAA0C,CAAC;AAC/C,IAAI,kBAAsC,CAAC;AAC3C,IAAI,eAAgC,CAAC;AACrC,IAAI,aAAmC,CAAC;AAExC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IAErE,mCAAmC;IACnC,aAAa,GAAG,IAAI,8BAAoB,EAAE,CAAC;IAE3C,wBAAwB;IACxB,SAAS,GAAG,IAAI,qBAAY,CAAC,aAAa,CAAC,CAAC;IAE5C,oDAAoD;IACpD,eAAe,GAAG,IAAI,wBAAe,CAAC,aAAa,CAAC,CAAC;IAErD,+BAA+B;IAC/B,gBAAgB,GAAG,IAAI,2BAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IAElE,iCAAiC;IACjC,kBAAkB,GAAG,IAAI,4BAAkB,EAAE,CAAC;IAE9C,gCAAgC;IAChC,oBAAoB,GAAG,IAAI,2CAAoB,CAAC,OAAO,CAAC,CAAC;IAEzD,qBAAqB;IACrB,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,iBAAiB,EAAE;QAC7D,gBAAgB,EAAE,oBAAoB;QACtC,eAAe,EAAE,IAAI;KACxB,CAAC,CAAC;IAEH,oBAAoB;IACpB,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAE1B,qBAAqB;IACrB,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAE3B,yBAAyB;IACzB,mBAAmB,CAAC,OAAO,CAAC,CAAC;IAE7B,wBAAwB;IACxB,cAAc,CAAC,OAAO,CAAC,CAAC;IAExB,kCAAkC;IAClC,IAAI,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;QACrC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;KACrD;IAED,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,QAAQ,EACR,kBAAkB,EAClB,eAAe,EACf,gBAAgB,CACnB,CAAC;AACN,CAAC;AAlDD,4BAkDC;AAED,SAAS,gBAAgB,CAAC,OAAgC;IACtD,uBAAuB;IACvB,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,KAAK,IAAI,EAAE;QACrF,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE;YACpC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;YAC3D,OAAO;SACV;QAED,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC7D,MAAM,gBAAgB,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,EAAE,KAAK,IAAI,EAAE;QAC/E,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACpD,IAAI,CAAC,YAAY,EAAE;YACf,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO;SACV;QAED,MAAM,gBAAgB,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,+BAA+B;IAC/B,MAAM,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,wBAAwB,EAAE,KAAK,IAAI,EAAE;QAC3F,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC1C,mBAAmB,EACnB,iBAAiB,EACjB,MAAM,CAAC,UAAU,CAAC,GAAG,EACrB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,0BAA0B,EAAE,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,yBAAyB;IACzB,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACnF,MAAM,YAAY,GAAG,GAAG,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC;QACzE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,6BAA6B;IAC7B,MAAM,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,uBAAuB,EAAE,GAAG,EAAE;QACnF,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,gCAAgC;IAChC,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACnF,oBAAoB,CAAC,OAAO,EAAE,CAAC;IACnC,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,EAAE,KAAK,EAAE,KAAU,EAAE,EAAE;QACzF,MAAM,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,KAAK,EAAE,KAAU,EAAE,EAAE;QAC/F,MAAM,gBAAgB,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,kBAAkB,EAClB,eAAe,EACf,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,kBAAkB,CACrB,CAAC;AACN,CAAC;AAED,SAAS,iBAAiB,CAAC,OAAgC;IACvD,6BAA6B;IAC7B,MAAM,gBAAgB,GAAG,IAAI,mCAAgB,CAAC,gBAAgB,CAAC,CAAC;IAChE,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAChE;QACI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE;QACxC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;QAC1C,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;QAC1C,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE;QACtC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;QAClC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE;KACvC,EACD,gBAAgB,CACnB,CAAC;IAEF,0BAA0B;IAC1B,MAAM,aAAa,GAAG,IAAI,6BAAa,CAAC,gBAAgB,CAAC,CAAC;IAC1D,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAC1D;QACI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE;QACxC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;QAC1C,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;KAC7C,EACD,aAAa,CAChB,CAAC;IAEF,+BAA+B;IAC/B,MAAM,kBAAkB,GAAG,IAAI,uCAAkB,EAAE,CAAC;IACpD,MAAM,oBAAoB,GAAG,MAAM,CAAC,SAAS,CAAC,8BAA8B,CACxE;QACI,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE;QACxC,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;QAC1C,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE;KAC7C,EACD,kBAAkB,EAClB,GAAG,EACH,GAAG,CACN,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,kBAAkB,EAClB,eAAe,EACf,oBAAoB,CACvB,CAAC;AACN,CAAC;AAED,SAAS,mBAAmB,CAAC,OAAgC;IACzD,8BAA8B;IAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;QAC/E,IAAI,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;YACrC,MAAM,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SACxD;IACL,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;QAChF,IAAI,MAAM,IAAI,aAAa,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;YACjD,MAAM,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;SAC/D;IACL,CAAC,CAAC,CAAC;IAEH,mCAAmC;IACnC,MAAM,wBAAwB,GAAG,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,KAAK,EAAE,EAAE;QACjF,IAAI,KAAK,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAAE;YACnC,aAAa,CAAC,OAAO,EAAE,CAAC;YACxB,4CAA4C;YAC5C,IAAI,KAAK,CAAC,oBAAoB,CAAC,eAAe,CAAC,EAAE;gBAC7C,eAAe,CAAC,SAAS,EAAE,CAAC;aAC/B;SACJ;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,gBAAgB,EAChB,gBAAgB,EAChB,wBAAwB,CAC3B,CAAC;AACN,CAAC;AAED,SAAS,cAAc,CAAC,OAAgC;IACpD,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CACnD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,GAAG,CACN,CAAC;IAEF,aAAa,CAAC,IAAI,GAAG,eAAe,CAAC;IACrC,aAAa,CAAC,OAAO,GAAG,sDAAsD,CAAC;IAC/E,aAAa,CAAC,OAAO,GAAG,iBAAiB,CAAC;IAC1C,aAAa,CAAC,IAAI,EAAE,CAAC;IAErB,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC9C,CAAC;AAED,KAAK,UAAU,0BAA0B;IACrC,kDAAkD;IAClD,gCAAgC;IAChC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;KA0BN,CAAC;AACN,CAAC;AAED,SAAgB,UAAU;IACtB,IAAI,eAAe,EAAE;QACjB,eAAe,CAAC,UAAU,EAAE,CAAC;KAChC;IACD,IAAI,kBAAkB,EAAE;QACpB,kBAAkB,CAAC,OAAO,EAAE,CAAC;KAChC;AACL,CAAC;AAPD,gCAOC"}