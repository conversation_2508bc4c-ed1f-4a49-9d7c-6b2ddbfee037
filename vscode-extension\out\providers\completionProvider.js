"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CompletionProvider = void 0;
const vscode = __importStar(require("vscode"));
class CompletionProvider {
    provideCompletionItems(document, position, token, context) {
        const completionItems = [];
        // Add security-related completions for Solidity
        if (document.languageId === 'solidity') {
            // Safe math operations
            const safeMathCompletion = new vscode.CompletionItem('SafeMath', vscode.CompletionItemKind.Snippet);
            safeMathCompletion.insertText = new vscode.SnippetString('using SafeMath for uint256;');
            safeMathCompletion.documentation = new vscode.MarkdownString('Use SafeMath library to prevent integer overflow/underflow');
            completionItems.push(safeMathCompletion);
            // Reentrancy guard
            const reentrancyGuardCompletion = new vscode.CompletionItem('ReentrancyGuard', vscode.CompletionItemKind.Snippet);
            reentrancyGuardCompletion.insertText = new vscode.SnippetString('modifier nonReentrant() {\n    require(!_locked, "ReentrancyGuard: reentrant call");\n    _locked = true;\n    _;\n    _locked = false;\n}');
            reentrancyGuardCompletion.documentation = new vscode.MarkdownString('Add reentrancy protection to functions');
            completionItems.push(reentrancyGuardCompletion);
        }
        return completionItems;
    }
}
exports.CompletionProvider = CompletionProvider;
//# sourceMappingURL=completionProvider.js.map