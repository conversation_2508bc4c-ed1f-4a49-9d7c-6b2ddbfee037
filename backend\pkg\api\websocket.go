package api

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"blockchain-spt/backend/pkg/models"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// WebSocketManager manages WebSocket connections
type WebSocketManager struct {
	clients    map[*websocket.Conn]*Client
	register   chan *Client
	unregister chan *Client
	broadcast  chan []byte
	mutex      sync.RWMutex
}

// Client represents a WebSocket client
type Client struct {
	conn     *websocket.Conn
	send     chan []byte
	userID   string
	clientID string
}

// WebSocketMessage represents a WebSocket message
type WebSocketMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
	ClientID  string      `json:"client_id,omitempty"`
}

// ScanProgressMessage represents scan progress updates
type ScanProgressMessage struct {
	ScanID       string  `json:"scan_id"`
	Status       string  `json:"status"`
	Progress     float64 `json:"progress"`
	CurrentFile  string  `json:"current_file,omitempty"`
	FilesScanned int     `json:"files_scanned"`
	TotalFiles   int     `json:"total_files"`
	IssuesFound  int     `json:"issues_found"`
	Message      string  `json:"message,omitempty"`
}

// ScanResultMessage represents completed scan results
type ScanResultMessage struct {
	ScanID string                 `json:"scan_id"`
	Result *models.ScanResult     `json:"result"`
	Issues []models.SecurityIssue `json:"issues"`
}

// Use the upgrader from routes.go to avoid redeclaration

// NewWebSocketManager creates a new WebSocket manager
func NewWebSocketManager() *WebSocketManager {
	return &WebSocketManager{
		clients:    make(map[*websocket.Conn]*Client),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		broadcast:  make(chan []byte),
	}
}

// Run starts the WebSocket manager
func (manager *WebSocketManager) Run() {
	for {
		select {
		case client := <-manager.register:
			manager.mutex.Lock()
			manager.clients[client.conn] = client
			manager.mutex.Unlock()

			logrus.WithFields(logrus.Fields{
				"client_id": client.clientID,
				"user_id":   client.userID,
			}).Info("WebSocket client connected")

			// Send welcome message
			welcome := WebSocketMessage{
				Type:      "welcome",
				Data:      map[string]string{"message": "Connected to SPT WebSocket"},
				Timestamp: time.Now(),
			}
			client.send <- manager.marshalMessage(welcome)

		case client := <-manager.unregister:
			manager.mutex.Lock()
			if _, ok := manager.clients[client.conn]; ok {
				delete(manager.clients, client.conn)
				close(client.send)
			}
			manager.mutex.Unlock()

			logrus.WithFields(logrus.Fields{
				"client_id": client.clientID,
				"user_id":   client.userID,
			}).Info("WebSocket client disconnected")

		case message := <-manager.broadcast:
			manager.mutex.RLock()
			for conn, client := range manager.clients {
				select {
				case client.send <- message:
				default:
					delete(manager.clients, conn)
					close(client.send)
				}
			}
			manager.mutex.RUnlock()
		}
	}
}

// HandleWebSocket handles WebSocket connections
func (manager *WebSocketManager) HandleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logrus.WithError(err).Error("Failed to upgrade WebSocket connection")
		return
	}

	userID := c.GetString("user_id")
	if userID == "" {
		userID = "anonymous"
	}

	clientID := generateClientID()
	client := &Client{
		conn:     conn,
		send:     make(chan []byte, 256),
		userID:   userID,
		clientID: clientID,
	}

	manager.register <- client

	// Start goroutines for reading and writing
	go manager.writePump(client)
	go manager.readPump(client)
}

// readPump handles reading messages from the WebSocket
func (manager *WebSocketManager) readPump(client *Client) {
	defer func() {
		manager.unregister <- client
		client.conn.Close()
	}()

	client.conn.SetReadLimit(512)
	client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	client.conn.SetPongHandler(func(string) error {
		client.conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := client.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logrus.WithError(err).Error("WebSocket error")
			}
			break
		}

		// Handle incoming messages
		var wsMessage WebSocketMessage
		if err := json.Unmarshal(message, &wsMessage); err == nil {
			manager.handleClientMessage(client, &wsMessage)
		}
	}
}

// writePump handles writing messages to the WebSocket
func (manager *WebSocketManager) writePump(client *Client) {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		client.conn.Close()
	}()

	for {
		select {
		case message, ok := <-client.send:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				client.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := client.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Add queued messages to the current message
			n := len(client.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-client.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			client.conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := client.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleClientMessage handles messages from clients
func (manager *WebSocketManager) handleClientMessage(client *Client, message *WebSocketMessage) {
	switch message.Type {
	case "ping":
		response := WebSocketMessage{
			Type:      "pong",
			Data:      map[string]string{"message": "pong"},
			Timestamp: time.Now(),
		}
		client.send <- manager.marshalMessage(response)

	case "subscribe":
		// Handle subscription to specific events
		logrus.WithFields(logrus.Fields{
			"client_id": client.clientID,
			"type":      message.Type,
			"data":      message.Data,
		}).Info("WebSocket subscription")

	default:
		logrus.WithFields(logrus.Fields{
			"client_id": client.clientID,
			"type":      message.Type,
		}).Warn("Unknown WebSocket message type")
	}
}

// BroadcastScanProgress broadcasts scan progress to all connected clients
func (manager *WebSocketManager) BroadcastScanProgress(progress *ScanProgressMessage) {
	message := WebSocketMessage{
		Type:      "scan_progress",
		Data:      progress,
		Timestamp: time.Now(),
	}
	manager.broadcast <- manager.marshalMessage(message)
}

// BroadcastScanResult broadcasts scan results to all connected clients
func (manager *WebSocketManager) BroadcastScanResult(result *ScanResultMessage) {
	message := WebSocketMessage{
		Type:      "scan_result",
		Data:      result,
		Timestamp: time.Now(),
	}
	manager.broadcast <- manager.marshalMessage(message)
}

// BroadcastSystemAlert broadcasts system alerts
func (manager *WebSocketManager) BroadcastSystemAlert(alertType, message string) {
	alert := WebSocketMessage{
		Type: "system_alert",
		Data: map[string]string{
			"alert_type": alertType,
			"message":    message,
		},
		Timestamp: time.Now(),
	}
	manager.broadcast <- manager.marshalMessage(alert)
}

// GetConnectedClients returns the number of connected clients
func (manager *WebSocketManager) GetConnectedClients() int {
	manager.mutex.RLock()
	defer manager.mutex.RUnlock()
	return len(manager.clients)
}

// marshalMessage marshals a WebSocket message to JSON
func (manager *WebSocketManager) marshalMessage(message WebSocketMessage) []byte {
	data, err := json.Marshal(message)
	if err != nil {
		logrus.WithError(err).Error("Failed to marshal WebSocket message")
		return []byte(`{"type":"error","data":{"message":"Failed to marshal message"},"timestamp":"` + time.Now().Format(time.RFC3339) + `"}`)
	}
	return data
}

// generateClientID generates a unique client ID
func generateClientID() string {
	return fmt.Sprintf("client_%d", time.Now().UnixNano())
}
