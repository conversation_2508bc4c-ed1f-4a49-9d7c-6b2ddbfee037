{"version": 3, "file": "securityTreeProvider.js", "sourceRoot": "", "sources": ["../../src/views/securityTreeProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAEjC,MAAa,oBAAoB;IAI7B,YAAoB,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;QAH5C,yBAAoB,GAAoE,IAAI,MAAM,CAAC,YAAY,EAA8C,CAAC;QAC7J,wBAAmB,GAA6D,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;IAElE,CAAC;IAExD,OAAO;QACH,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED,WAAW,CAAC,OAAyB;QACjC,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,OAA0B;QAClC,IAAI,CAAC,OAAO,EAAE;YACV,OAAO,OAAO,CAAC,OAAO,CAAC;gBACnB,IAAI,gBAAgB,CAAC,iBAAiB,EAAE,MAAM,CAAC,wBAAwB,CAAC,QAAQ,CAAC;gBACjF,IAAI,gBAAgB,CAAC,aAAa,EAAE,MAAM,CAAC,wBAAwB,CAAC,QAAQ,CAAC;gBAC7E,IAAI,gBAAgB,CAAC,eAAe,EAAE,MAAM,CAAC,wBAAwB,CAAC,SAAS,CAAC;gBAChF,IAAI,gBAAgB,CAAC,YAAY,EAAE,MAAM,CAAC,wBAAwB,CAAC,SAAS,CAAC;aAChF,CAAC,CAAC;SACN;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;CACJ;AAzBD,oDAyBC;AAED,MAAa,gBAAiB,SAAQ,MAAM,CAAC,QAAQ;IACjD,YACoB,KAAa,EACb,gBAAiD;QAEjE,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAHf,UAAK,GAAL,KAAK,CAAQ;QACb,qBAAgB,GAAhB,gBAAgB,CAAiC;QAGjE,IAAI,CAAC,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;CACJ;AATD,4CASC"}