package commands

import (
	"fmt"
	"os"
	"path/filepath"

	"blockchain-spt/backend/pkg/config"

	"github.com/spf13/cobra"
	"gopkg.in/yaml.v2"
)

// NewConfigCommand creates the config command
func NewConfigCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "config",
		Short: "Manage SPT configuration",
		Long: `Manage SPT configuration files and settings.

The config command allows you to:
- Initialize default configuration
- Validate existing configuration
- Show current configuration
- Update configuration values`,
	}

	// Add subcommands
	cmd.AddCommand(newConfigInitCommand())
	cmd.AddCommand(newConfigShowCommand())
	cmd.AddCommand(newConfigValidateCommand())
	cmd.AddCommand(newConfigSetCommand())

	return cmd
}

// newConfigInitCommand creates the config init subcommand
func newConfigInitCommand() *cobra.Command {
	var (
		output string
		force  bool
	)

	cmd := &cobra.Command{
		Use:   "init",
		Short: "Initialize default configuration",
		Long: `Initialize a default SPT configuration file.

This creates a configuration file with sensible defaults that you can
customize for your specific needs.

Examples:
  spt config init                           # Create spt.yaml in current directory
  spt config init --output ~/.spt.yaml     # Create config in home directory
  spt config init --force                  # Overwrite existing config`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runConfigInit(output, force)
		},
	}

	cmd.Flags().StringVarP(&output, "output", "o", "spt.yaml", "Output configuration file path")
	cmd.Flags().BoolVarP(&force, "force", "f", false, "Overwrite existing configuration file")

	return cmd
}

// newConfigShowCommand creates the config show subcommand
func newConfigShowCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "show",
		Short: "Show current configuration",
		Long: `Display the current SPT configuration.

This shows the effective configuration that will be used,
including any defaults and overrides.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runConfigShow(cmd)
		},
	}

	return cmd
}

// newConfigValidateCommand creates the config validate subcommand
func newConfigValidateCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "validate [config-file]",
		Short: "Validate configuration file",
		Long: `Validate a SPT configuration file for syntax and semantic errors.

Examples:
  spt config validate                       # Validate default config
  spt config validate spt.yaml            # Validate specific file
  spt config validate ~/.spt.yaml         # Validate config in home directory`,
		Args: cobra.MaximumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			configFile := ""
			if len(args) > 0 {
				configFile = args[0]
			}
			return runConfigValidate(configFile)
		},
	}

	return cmd
}

// newConfigSetCommand creates the config set subcommand
func newConfigSetCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "set <key> <value>",
		Short: "Set configuration value",
		Long: `Set a configuration value in the SPT configuration file.

Examples:
  spt config set security.level strict     # Set security level
  spt config set logging.level debug       # Set log level
  spt config set scanning.timeout 600      # Set scan timeout`,
		Args: cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runConfigSet(args[0], args[1])
		},
	}

	return cmd
}

// runConfigInit initializes a default configuration file
func runConfigInit(output string, force bool) error {
	// Check if file exists and force is not set
	if _, err := os.Stat(output); err == nil && !force {
		return fmt.Errorf("configuration file already exists: %s (use --force to overwrite)", output)
	}

	// Create default configuration
	cfg := &config.Config{
		Environment: "development",
		Security: config.SecurityConfig{
			Level: "medium",
			Rules: config.SecurityRulesConfig{
				KeyLeakDetection: config.KeyLeakConfig{
					Enabled: true,
					Patterns: []string{
						"private.*key",
						"secret.*key",
						"mnemonic",
						"seed.*phrase",
					},
				},
				SmartContractAudit: config.ContractConfig{
					Enabled: true,
					Ethereum: config.EthereumConfig{
						Reentrancy:      true,
						IntegerOverflow: true,
						UncheckedCalls:  true,
						GasOptimization: true,
						AccessControl:   true,
					},
					Bitcoin: config.BitcoinConfig{
						ScriptValidation: true,
						MultisigSecurity: true,
						UTXOPatterns:     true,
					},
				},
				DependencyScanning: config.DependencyConfig{
					Enabled:  true,
					Sources:  []string{"npm", "pip", "cargo", "go"},
					Severity: []string{"critical", "high", "medium"},
				},
				EnvironmentSecurity: config.EnvSecurityConfig{
					Enabled:          true,
					CheckDockerfiles: true,
					CheckCICD:        true,
					CheckEnvFiles:    true,
				},
			},
		},
		Scanning: config.ScanningConfig{
			Paths: config.PathsConfig{
				Include: []string{
					"*.sol", "*.js", "*.ts", "*.py", "*.go", "*.rs",
					"*.json", "*.yml", "*.yaml", "*.toml",
					"Dockerfile", "docker-compose.yml",
					".env", ".env.*",
				},
				Exclude: []string{
					"node_modules", "vendor", ".git", "build", "dist",
					"target", "bin", "obj", "__pycache__",
					".idea", ".vscode", ".vs",
				},
			},
			FileTypes: []string{"sol", "js", "ts", "py", "go", "rs", "json", "yml", "yaml"},
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "text",
			Output: "stdout",
		},
	}

	// Create directory if it doesn't exist
	dir := filepath.Dir(output)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// Write configuration file
	file, err := os.Create(output)
	if err != nil {
		return fmt.Errorf("failed to create config file: %w", err)
	}
	defer file.Close()

	encoder := yaml.NewEncoder(file)
	if err := encoder.Encode(cfg); err != nil {
		return fmt.Errorf("failed to write config: %w", err)
	}

	fmt.Printf("✅ Configuration file created: %s\n", output)
	fmt.Printf("📝 You can now customize the configuration for your needs\n")
	fmt.Printf("🔍 Use 'spt config show' to view the current configuration\n")
	fmt.Printf("✔️  Use 'spt config validate' to validate your changes\n")

	return nil
}

// runConfigShow displays the current configuration
func runConfigShow(cmd *cobra.Command) error {
	cfg, err := loadConfig(cmd)
	if err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	fmt.Printf("📋 Current SPT Configuration:\n")
	fmt.Printf("═══════════════════════════════\n\n")

	// Display configuration in YAML format
	encoder := yaml.NewEncoder(os.Stdout)
	if err := encoder.Encode(cfg); err != nil {
		return fmt.Errorf("failed to display config: %w", err)
	}

	return nil
}

// runConfigValidate validates a configuration file
func runConfigValidate(configFile string) error {
	if configFile == "" {
		// Try to find default config files
		candidates := []string{"spt.yaml", "spt.yml", ".spt.yaml", ".spt.yml"}
		for _, candidate := range candidates {
			if _, err := os.Stat(candidate); err == nil {
				configFile = candidate
				break
			}
		}

		if configFile == "" {
			return fmt.Errorf("no configuration file found. Use 'spt config init' to create one")
		}
	}

	fmt.Printf("🔍 Validating configuration file: %s\n", configFile)

	// Load and validate configuration
	cfg, err := config.LoadFromFile(configFile)
	if err != nil {
		fmt.Printf("❌ Configuration validation failed:\n")
		fmt.Printf("   %v\n", err)
		return err
	}

	// Perform additional validation
	if err := validateConfigSemantic(cfg); err != nil {
		fmt.Printf("❌ Configuration semantic validation failed:\n")
		fmt.Printf("   %v\n", err)
		return err
	}

	fmt.Printf("✅ Configuration is valid!\n")
	fmt.Printf("📊 Configuration summary:\n")
	fmt.Printf("   - Environment: %s\n", cfg.Environment)
	fmt.Printf("   - Security Level: %s\n", cfg.Security.Level)
	fmt.Printf("   - Log Level: %s\n", cfg.Logging.Level)
	fmt.Printf("   - File Types: %d\n", len(cfg.Scanning.FileTypes))
	fmt.Printf("   - Include Patterns: %d\n", len(cfg.Scanning.Paths.Include))
	fmt.Printf("   - Exclude Patterns: %d\n", len(cfg.Scanning.Paths.Exclude))

	return nil
}

// runConfigSet sets a configuration value
func runConfigSet(key, value string) error {
	// This is a simplified implementation
	// In a full implementation, you would:
	// 1. Load existing config
	// 2. Parse the key path (e.g., "security.level")
	// 3. Update the specific value
	// 4. Save the config back

	fmt.Printf("🔧 Setting configuration value:\n")
	fmt.Printf("   Key: %s\n", key)
	fmt.Printf("   Value: %s\n", value)
	fmt.Printf("\n")
	fmt.Printf("⚠️  Note: Configuration modification is not yet implemented.\n")
	fmt.Printf("📝 Please edit the configuration file manually:\n")
	fmt.Printf("   1. Use 'spt config show' to see current config\n")
	fmt.Printf("   2. Edit the YAML file directly\n")
	fmt.Printf("   3. Use 'spt config validate' to check your changes\n")

	return nil
}

// validateConfigSemantic performs semantic validation of configuration
func validateConfigSemantic(cfg *config.Config) error {
	// Validate security level
	validSecurityLevels := []string{"low", "medium", "high", "strict"}
	isValidLevel := false
	for _, level := range validSecurityLevels {
		if cfg.Security.Level == level {
			isValidLevel = true
			break
		}
	}
	if !isValidLevel {
		return fmt.Errorf("invalid security level '%s', must be one of: %v", cfg.Security.Level, validSecurityLevels)
	}

	// Validate log level
	validLogLevels := []string{"debug", "info", "warn", "error"}
	isValidLogLevel := false
	for _, level := range validLogLevels {
		if cfg.Logging.Level == level {
			isValidLogLevel = true
			break
		}
	}
	if !isValidLogLevel {
		return fmt.Errorf("invalid log level '%s', must be one of: %v", cfg.Logging.Level, validLogLevels)
	}

	// Validate file types
	if len(cfg.Scanning.FileTypes) == 0 {
		return fmt.Errorf("at least one file type must be specified")
	}

	// Validate include patterns
	if len(cfg.Scanning.Paths.Include) == 0 {
		return fmt.Errorf("at least one include pattern must be specified")
	}

	return nil
}
