{"name": "blockchain-security-protocol", "displayName": "Blockchain Security Protocol (SPT)", "description": "Real-time blockchain security analysis for Ethereum, Bitcoin, and smart contracts with vulnerability detection and security recommendations", "version": "1.0.0", "publisher": "blockchain-spt", "repository": {"type": "git", "url": "https://github.com/blockchain-spt/vscode-extension"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Linters", "Other", "Programming Languages", "Snippets"], "keywords": ["blockchain", "security", "ethereum", "bitcoin", "solidity", "smart contracts", "vulnerability", "audit", "defi", "web3"], "activationEvents": ["onLanguage:solidity", "onLanguage:javascript", "onLanguage:typescript", "onLanguage:python", "onLanguage:go", "onLanguage:rust", "onCommand:spt.scanProject", "onCommand:spt.scanFile", "workspaceContains:**/*.sol", "workspaceContains:**/package.json", "workspaceContains:**/go.mod", "workspaceContains:**/Cargo.toml"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "spt.scanProject", "title": "Scan Project for Security Issues", "category": "SPT", "icon": "$(shield)"}, {"command": "spt.scanFile", "title": "<PERSON>an Current File", "category": "SPT", "icon": "$(search)"}, {"command": "spt.showSecurityReport", "title": "Show Security Report", "category": "SPT", "icon": "$(report)"}, {"command": "spt.openDashboard", "title": "Open Security Dashboard", "category": "SPT", "icon": "$(dashboard)"}, {"command": "spt.configureSettings", "title": "Configure SPT Settings", "category": "SPT", "icon": "$(settings-gear)"}, {"command": "spt.refreshSecurityView", "title": "Refresh Security View", "category": "SPT", "icon": "$(refresh)"}, {"command": "spt.fixIssue", "title": "Fix Security Issue", "category": "SPT", "icon": "$(lightbulb)"}, {"command": "spt.ignoreIssue", "title": "Ignore Security Issue", "category": "SPT", "icon": "$(eye-closed)"}], "menus": {"editor/context": [{"command": "spt.scanFile", "when": "resourceExtname == .sol || resourceExtname == .js || resourceExtname == .ts || resourceExtname == .py || resourceExtname == .go || resourceExtname == .rs", "group": "spt@1"}, {"command": "spt.fixIssue", "when": "spt.hasSecurityIssue", "group": "spt@2"}], "explorer/context": [{"command": "spt.scanProject", "when": "explorerResourceIsFolder", "group": "spt@1"}], "view/title": [{"command": "spt.refreshSecurityView", "when": "view == sptSecurityView", "group": "navigation"}]}, "views": {"explorer": [{"id": "sptSecurityView", "name": "Security Issues", "when": "spt.hasSecurityIssues"}]}, "viewsContainers": {"activitybar": [{"id": "spt-security", "title": "Blockchain Security", "icon": "$(shield)"}]}, "configuration": {"title": "Blockchain Security Protocol", "properties": {"spt.enabled": {"type": "boolean", "default": true, "description": "Enable/disable SPT security analysis"}, "spt.serverUrl": {"type": "string", "default": "http://localhost:8080", "description": "SPT backend server URL"}, "spt.apiKey": {"type": "string", "default": "", "description": "API key for SPT backend authentication"}, "spt.autoScan": {"type": "boolean", "default": true, "description": "Automatically scan files on save"}, "spt.scanOnOpen": {"type": "boolean", "default": false, "description": "Automatically scan files when opened"}, "spt.chains": {"type": "array", "items": {"type": "string", "enum": ["ethereum", "bitcoin", "general"]}, "default": ["ethereum", "bitcoin", "general"], "description": "Blockchain chains to analyze"}, "spt.severity": {"type": "string", "enum": ["critical", "high", "medium", "low", "info"], "default": "medium", "description": "Minimum severity level to show"}, "spt.showInlineDecorations": {"type": "boolean", "default": true, "description": "Show inline security decorations in editor"}, "spt.showProblems": {"type": "boolean", "default": true, "description": "Show security issues in Problems panel"}, "spt.enableCodeLens": {"type": "boolean", "default": true, "description": "Enable security-related CodeLens"}, "spt.enableHover": {"type": "boolean", "default": true, "description": "Enable security information on hover"}}}, "languages": [{"id": "solidity", "aliases": ["Solidity", "solidity"], "extensions": [".sol"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "solidity", "scopeName": "source.solidity", "path": "./syntaxes/solidity.tmLanguage.json"}], "snippets": [{"language": "solidity", "path": "./snippets/solidity.json"}, {"language": "javascript", "path": "./snippets/javascript.json"}, {"language": "typescript", "path": "./snippets/typescript.json"}], "problemMatchers": [{"name": "spt-security", "owner": "spt", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error|info):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/node": "16.x", "@types/vscode": "^1.74.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0", "eslint": "^8.28.0", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}}